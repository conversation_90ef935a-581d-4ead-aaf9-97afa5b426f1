[tool.poetry]
name = "finrobot-pro"
version = "1.0.0"
description = "Production-grade AI Agent Platform for Financial Analysis and Forecasting"
authors = ["FinRobot Team <<EMAIL>>"]
readme = "README.md"
packages = [{include = "finrobot"}]

[tool.poetry.dependencies]
python = "^3.10"
# Core ML/Data Science
pandas = "^2.0.3"
numpy = "^1.26.4"
scipy = "^1.11.0"
scikit-learn = "^1.5.0"
statsmodels = "^0.14.0"

# Advanced ML Models
lightgbm = "^4.1.0"
xgboost = "^2.0.0"
prophet = "^1.1.4"
torch = "^2.1.0"
pytorch-forecasting = "^1.0.0"

# Model Interpretation & Optimization
shap = "^0.44.0"
hyperopt = "^0.2.7"

# Workflow & Configuration
prefect = "^2.14.0"
hydra-core = "^1.3.2"
python-dotenv = "^1.0.0"
mlflow = "^2.8.0"

# Visualization
matplotlib = "^3.8.0"
plotly = "^5.17.0"
mplfinance = "^0.12.10"

# Financial Data APIs
finnhub-python = "^2.4.18"
yfinance = "^0.2.28"
alpha-vantage = "^2.3.1"
polygon-api-client = "^1.12.0"
tiingo = "^0.14.0"

# Data Storage & Caching
diskcache = "^5.6.3"
pyarrow = "^14.0.0"

# Utilities
tqdm = "^4.66.1"
tenacity = "^8.3.0"
ratelimit = "^2.2.1"
requests = "^2.31.0"

[tool.poetry.group.dev.dependencies]
# Testing
pytest = "^7.4.0"
pytest-cov = "^4.1.0"
pytest-mock = "^3.12.0"

# Code Quality
black = "^23.9.0"
isort = "^5.12.0"
flake8 = "^6.1.0"
mypy = "^1.6.0"
pre-commit = "^3.5.0"

# Documentation
sphinx = "^7.2.0"
sphinx-rtd-theme = "^1.3.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["finrobot"]

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --cov=finrobot --cov-report=term-missing --cov-report=html"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

[tool.coverage.run]
source = ["finrobot"]
omit = [
    "*/tests/*",
    "*/test_*.py",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
