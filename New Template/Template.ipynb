{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "source": ["# Create an HTML file that closely resembles the first page of your PDF\n", "\n", "html_content = \"\"\"\n", "<html lang=\"en\">\n", " <head>\n", "  <meta charset=\"utf-8\"/>\n", "  <meta content=\"width=device-width, initial-scale=1.0\" name=\"viewport\"/>\n", "  <title>\n", "   US EQUITY RESEARCH - Energy Company X\n", "  </title>\n", "  <script src=\"https://cdn.tailwindcss.com\">\n", "  </script>\n", "  <link href=\"https://fonts.googleapis.com/css2?family=DM+Sans:wght@300&amp;display=swap\" rel=\"stylesheet\"/>\n", "  <style>\n", "   body { font-family: 'D<PERSON> Sans', sans-serif; font-weight: 300; }\n", "        @page { size: 8.5in 11in; margin: 0; }\n", "        .page-container { width: 8.5in; height: 11in; padding: 0.5in; box-sizing: border-box; }\n", "        .financial-table tr:not(:last-child) { border-bottom: 1px solid #e5e7eb; }\n", "        .financial-table td { padding: 4px 0; }\n", "  </style>\n", " </head>\n", " <body class=\"bg-white text-xs\">\n", "  <div class=\"page-container mx-auto\">\n", "   <header class=\"mb-6\">\n", "    <div class=\"flex justify-between items-center mb-2\">\n", "     <div>\n", "     </div>\n", "     <p class=\"text-gray-500 text-[12px]\">\n", "      FinRobot Research\n", "     </p>\n", "    </div>\n", "    <div class=\"bg-black text-white px-2 py-1 w-full\">\n", "     <h1 class=\"text-2xl font-normal\">\n", "      US EQUITY RESEARCH\n", "     </h1>\n", "    </div>\n", "    <p class=\"text-gray-500 text-[12px] text-right mt-1\">\n", "     15 Apr 20XX\n", "    </p>\n", "   </header>\n", "   <main>\n", "    <div class=\"flex justify-between mb-6\">\n", "     <div>\n", "      <h2 class=\"text-[20px] text-gray-400 mb-3\">\n", "       Energy Company X\n", "      </h2>\n", "      <p class=\"text-gray-600 text-[14px]\">\n", "       Leading the Transition to Clean Energy\n", "      </p>\n", "     </div>\n", "     <div>\n", "      <p class=\"text-gray-400 text-[14px]\">\n", "       Analysts\n", "      </p>\n", "      <p class=\"text-gray-500\">\n", "       Analyst A | <EMAIL>\n", "      </p>\n", "      <p class=\"text-gray-500\">\n", "       Analyst B | <EMAIL>\n", "      </p>\n", "     </div>\n", "    </div>\n", "    <div class=\"flex gap-6\">\n", "     <div class=\"w-2/3\">\n", "      <h3 class=\"font-normal mb-2 text-[14px] text-gray-400\">\n", "       Company Overview\n", "      </h3>\n", "      <p class=\"mb-4 text-xs leading-tight\">\n", "       <span class=\"font-bold\">\n", "        Energy Company X is a leading utility and renewable energy company in the United States.\n", "       </span>\n", "       The company has two primary businesses: 1) A major electric utility division providing clean electricity to millions of customers, with significant generating capacity, and 2) A renewable energy division, which is a global leader in renewable energy generation from wind and solar, as well as a pioneer in battery storage technology. These two divisions contribute to the company's overall adjusted EPS.\n", "      </p>\n", "      <h3 class=\"font-normal mb-2 text-[14px] text-gray-400\">\n", "       Investment Overview\n", "      </h3>\n", "      <p class=\"mb-4 text-xs leading-tight\">\n", "       <span class=\"font-bold underline\">\n", "        A unique combination of regulated utility business with significant renewable energy exposure.\n", "       </span>\n", "       Energy Company X offers a compelling proposition, combining a large regulated utility business with a rapidly growing renewable energy division. The utility business benefits from steady growth in its service area, while the renewable energy division is poised for substantial expansion. The company has set ambitious targets for renewable capacity growth and is exploring innovative technologies such as green hydrogen. Despite these ambitious goals, Energy Company X has consistently demonstrated strong financial performance, outpacing industry averages in terms of EPS growth over the past decade.\n", "      </p>\n", "      <p class=\"mb-4 text-xs leading-tight\">\n", "       <span class=\"font-bold\">\n", "        Recent financial performance aligns with expectations.\n", "       </span>\n", "       The company reported adjusted EPS growth in line with consensus estimates, slightly exceeding management's guidance. Both the utility and renewable energy segments performed well, with the utility division maintaining high reliability and competitive rates, while the renewable energy division continued to expand its project pipeline.\n", "      </p>\n", "      <p class=\"mb-4 text-xs leading-tight\">\n", "       <span class=\"font-bold\">\n", "        Strong growth outlook maintained.\n", "       </span>\n", "       Management has reaffirmed its target for adjusted EPS CAGR through the mid-2020s. The company anticipates significant capital expenditure in the coming years, with a substantial portion allocated to renewables and energy storage projects. This strategy is expected to drive long-term growth and maintain the company's leadership position in the clean energy transition.\n", "      </p>\n", "      <p class=\"mb-4 text-xs leading-tight\">\n", "       <span class=\"font-bold\">\n", "        Positive recommendation with increased target price.\n", "       </span>\n", "       We maintain a positive outlook on Energy Company X, with an increased target price. The company is trading at a premium to its historical average, which we believe is justified given its strong growth prospects and leading position in both the utility and renewable energy sectors. We view the recent market pullback as an attractive entry point for investors seeking exposure to the clean energy transition with the stability of a regulated utility business.\n", "      </p>\n", "      <h3 class=\"font-normal mb-2 text-[14px] text-gray-400\">\n", "       Risks\n", "      </h3>\n", "      <p class=\"mb-4 text-xs leading-tight\">\n", "       Key risks include\n", "       <span class=\"font-bold\">\n", "        interest rate fluctuations\n", "       </span>\n", "       ,\n", "       <span class=\"font-bold\">\n", "        supply chain challenges\n", "       </span>\n", "       , and\n", "       <span class=\"font-bold\">\n", "        regulatory changes\n", "       </span>\n", "       . The company has mitigation strategies in place but investors should be aware of these potential headwinds.\n", "      </p>\n", "     </div>\n", "     <div class=\"w-1/3\">\n", "      <h3 class=\"font-normal mb-2 text-[14px] text-gray-400\">\n", "       Key Financial Data\n", "      </h3>\n", "      <div class=\"bg-gray-100 p-4 mb-4\">\n", "       <table class=\"w-full text-xs financial-table\">\n", "        <tr>\n", "         <td class=\"text-gray-500\">\n", "          Bloomberg Ticker\n", "         </td>\n", "         <td class=\"text-black text-right\">\n", "          ECX US\n", "         </td>\n", "        </tr>\n", "        <tr>\n", "         <td class=\"text-gray-500\">\n", "          Sector\n", "         </td>\n", "         <td class=\"text-black text-right\">\n", "          Utilities\n", "         </td>\n", "        </tr>\n", "        <tr>\n", "         <td class=\"text-gray-500\">\n", "          Share Price (USD)\n", "         </td>\n", "         <td class=\"text-black text-right\">\n", "          XX.XX\n", "         </td>\n", "        </tr>\n", "        <tr>\n", "         <td class=\"text-gray-500\">\n", "          Rating\n", "         </td>\n", "         <td class=\"text-black text-right\">\n", "          BUY\n", "         </td>\n", "        </tr>\n", "        <tr>\n", "         <td class=\"text-gray-500\">\n", "          12-mth Target Price (USD)\n", "         </td>\n", "         <td class=\"text-black text-right\">\n", "          XX.X\n", "         </td>\n", "        </tr>\n", "        <tr>\n", "         <td class=\"text-gray-500\">\n", "          Market Cap (USDb)\n", "         </td>\n", "         <td class=\"text-black text-right\">\n", "          XXX.X\n", "         </td>\n", "        </tr>\n", "        <tr>\n", "         <td class=\"text-gray-500\">\n", "          Volume (m shares)\n", "         </td>\n", "         <td class=\"text-black text-right\">\n", "          XX.X\n", "         </td>\n", "        </tr>\n", "        <tr>\n", "         <td class=\"text-gray-500\">\n", "          Free float (%)\n", "         </td>\n", "         <td class=\"text-black text-right\">\n", "          XX.X\n", "         </td>\n", "        </tr>\n", "        <tr>\n", "         <td class=\"text-gray-500\">\n", "          Dividend yield (%)\n", "         </td>\n", "         <td class=\"text-black text-right\">\n", "          X.X\n", "         </td>\n", "        </tr>\n", "        <tr>\n", "         <td class=\"text-gray-500\">\n", "          Net Debt to Equity (%)\n", "         </td>\n", "         <td class=\"text-black text-right\">\n", "          XXX.X\n", "         </td>\n", "        </tr>\n", "        <tr>\n", "         <td class=\"text-gray-500\">\n", "          Fwd. P/E (x)\n", "         </td>\n", "         <td class=\"text-black text-right\">\n", "          XX.X\n", "         </td>\n", "        </tr>\n", "        <tr>\n", "         <td class=\"text-gray-500\">\n", "          P/Book (x)\n", "         </td>\n", "         <td class=\"text-black text-right\">\n", "          X.X\n", "         </td>\n", "        </tr>\n", "        <tr>\n", "         <td class=\"text-gray-500\">\n", "          ROE (%)\n", "         </td>\n", "         <td class=\"text-black text-right\">\n", "          XX.X\n", "         </td>\n", "        </tr>\n", "       </table>\n", "      </div>\n", "      <p class=\"text-[12px] text-gray-500 mb-2 italic\">\n", "       Closing Price as of XX Apr 20XX\n", "      </p>\n", "      <p class=\"text-[12px] text-gray-500 mb-4\">\n", "       Source: Market Data Provider, Research Group\n", "      </p>\n", "      <h3 class=\"font-normal mb-2 text-[14px] text-gray-400\">\n", "       Indexed Share Price vs Composite Index Performance\n", "      </h3>\n", "      <img alt=\"Placeholder for graph showing Energy Company X's stock performance compared to a market index\" class=\"w-full mb-4\" height=\"200\" src=\"https://replicate.delivery/yhqm/prdmq2OHHg4jB98MW6IsistKsvmeYhIPU43wzPzg8BXc0csJA/out-0.png\" width=\"400\"/>\n", "      <p class=\"text-[12px] text-gray-500\">\n", "       Source: Market Data Provider\n", "      </p>\n", "     </div>\n", "    </div>\n", "   </main>\n", "   <footer class=\"mt-8 text-xs text-gray-500\">\n", "    <p>\n", "     Disclaimer: The information contained in this document is intended only for use by the person to whom it has been delivered and should not be disseminated or distributed to third parties without our prior written consent. Our firm accepts no liability whatsoever with respect to the use of this document or its contents.\n", "    </p>\n", "    <p>\n", "     Please refer to Disclaimer found at the end of this document.\n", "    </p>\n", "   </footer>\n", "   <div class=\"mt-4 text-right\">\n", "    <img alt=\"Research Group logo\" class=\"inline-block\" height=\"20\" src=\"https://replicate.delivery/yhqm/uamWKEkrvh70KlkPPLqmIkUu2pEStFh0xVzMWzvdu99NaO2E/out-0.png\" width=\"50\"/>\n", "   </div>\n", "  </div>\n", " </body>\n", "</html>\n", "\"\"\"\n", "\n", "# Write the HTML content to a file\n", "with open(\"/content/Equity_Research_Template_firstpage.html\", \"w\") as file:\n", "    file.write(html_content)\n", "\n", "print(\"HTML file created successfully.\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "xe8yo3ajTEd0", "outputId": "cb7fccfc-ea62-497a-edf7-3c439ccea2f0"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["HTML file created successfully.\n"]}]}, {"cell_type": "code", "source": ["html_content_second_page =\"\"\"\n", "<html lang=\"en\">\n", " <head>\n", "  <meta charset=\"utf-8\"/>\n", "  <meta content=\"width=device-width, initial-scale=1.0\" name=\"viewport\"/>\n", "  <title>\n", "   FinRobot Research Financial Summary\n", "  </title>\n", "  <script src=\"https://cdn.tailwindcss.com\">\n", "  </script>\n", "  <link href=\"https://fonts.googleapis.com/css2?family=DM+Sans:wght@300&amp;display=swap\" rel=\"stylesheet\"/>\n", "  <style>\n", "   body {\n", "            font-family: '<PERSON><PERSON> Sans', sans-serif;\n", "            font-weight: 300;\n", "            width: 8.5in;\n", "            height: 11in;\n", "            margin: 0 auto;\n", "            padding: 0.5in;\n", "            box-sizing: border-box;\n", "        }\n", "  </style>\n", " </head>\n", " <body class=\"bg-white\">\n", "  <div class=\"max-w-4xl mx-auto\">\n", "   <div class=\"text-right text-xs text-gray-500 mb-4\">\n", "    FinRobot Research\n", "   </div>\n", "   <div class=\"w-2/3\">\n", "    <div class=\"mb-8\">\n", "     <h2 class=\"text-gray-500 text-[14px] font-bold mb-2\">\n", "      FINANCIAL SUMMARY (USD M)\n", "     </h2>\n", "     <table class=\"w-full text-[16px]\">\n", "      <thead>\n", "       <tr class=\"border-b-2 border-black\">\n", "        <th class=\"text-left font-normal pb-1\">\n", "         FY Dec\n", "        </th>\n", "        <th class=\"text-right font-normal pb-1\">\n", "         FY2021A\n", "        </th>\n", "        <th class=\"text-right font-normal pb-1\">\n", "         FY2022A\n", "        </th>\n", "        <th class=\"text-right font-normal pb-1\">\n", "         FY2023A\n", "        </th>\n", "        <th class=\"text-right font-normal pb-1\">\n", "         FY2024F\n", "        </th>\n", "        <th class=\"text-right font-normal pb-1\">\n", "         FY2025F\n", "        </th>\n", "       </tr>\n", "      </thead>\n", "      <tbody>\n", "       <tr class=\"bg-gray-100\">\n", "        <td>\n", "         Sales\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "       </tr>\n", "       <tr>\n", "        <td class=\"pl-4 italic\">\n", "         % Sales y-o-y\n", "        </td>\n", "        <td class=\"text-right italic\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right italic\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right italic\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right italic\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right italic\">\n", "         XX.X\n", "        </td>\n", "       </tr>\n", "       <tr class=\"bg-gray-100\">\n", "        <td>\n", "         Gross Profit\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "       </tr>\n", "       <tr>\n", "        <td class=\"pl-4 italic\">\n", "         % Gross Profit y-o-y\n", "        </td>\n", "        <td class=\"text-right italic\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right italic\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right italic\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right italic\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right italic\">\n", "         XX.X\n", "        </td>\n", "       </tr>\n", "       <tr class=\"bg-gray-100\">\n", "        <td>\n", "         EBITDA\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "       </tr>\n", "       <tr>\n", "        <td class=\"pl-4 italic\">\n", "         % EBITDA y-o-y\n", "        </td>\n", "        <td class=\"text-right italic\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right italic\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right italic\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right italic\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right italic\">\n", "         XX.X\n", "        </td>\n", "       </tr>\n", "       <tr class=\"bg-gray-100\">\n", "        <td>\n", "         Net Profit\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "       </tr>\n", "       <tr>\n", "        <td class=\"pl-4 italic\">\n", "         % Net Profit y-o-y\n", "        </td>\n", "        <td class=\"text-right italic\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right italic\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right italic\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right italic\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right italic\">\n", "         XX.X\n", "        </td>\n", "       </tr>\n", "       <tr class=\"bg-gray-100\">\n", "        <td>\n", "         FCF\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "       </tr>\n", "       <tr>\n", "        <td>\n", "         CAPEX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX\n", "        </td>\n", "       </tr>\n", "       <tr class=\"bg-gray-100\">\n", "        <td>\n", "         EBITDA Margin %\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "       </tr>\n", "       <tr>\n", "        <td>\n", "         Net Margin %\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "       </tr>\n", "       <tr class=\"bg-gray-100\">\n", "        <td>\n", "         ROA (%)\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "       </tr>\n", "       <tr>\n", "        <td>\n", "         ROE (%)\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "       </tr>\n", "       <tr class=\"bg-gray-100\">\n", "        <td>\n", "         Tax Rate %\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "       </tr>\n", "      </tbody>\n", "     </table>\n", "     <div class=\"text-[12px] text-gray-500 mt-1\">\n", "      Source: Visible Alpha\n", "     </div>\n", "    </div>\n", "    <div class=\"mb-8\">\n", "     <h2 class=\"text-gray-500 text-[14px] font-bold mb-2\">\n", "      VALUATION METRICS (USD, M)\n", "     </h2>\n", "     <table class=\"w-full text-[16px]\">\n", "      <thead>\n", "       <tr class=\"border-b-2 border-black\">\n", "        <th class=\"text-left font-normal pb-1\">\n", "         FY Dec\n", "        </th>\n", "        <th class=\"text-right font-normal pb-1\">\n", "         FY2021A\n", "        </th>\n", "        <th class=\"text-right font-normal pb-1\">\n", "         FY2022A\n", "        </th>\n", "        <th class=\"text-right font-normal pb-1\">\n", "         FY2023A\n", "        </th>\n", "        <th class=\"text-right font-normal pb-1\">\n", "         FY2024F\n", "        </th>\n", "        <th class=\"text-right font-normal pb-1\">\n", "         FY2025F\n", "        </th>\n", "       </tr>\n", "      </thead>\n", "      <tbody>\n", "       <tr class=\"bg-gray-100\">\n", "        <td>\n", "         P/E\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "       </tr>\n", "       <tr>\n", "        <td>\n", "         P/B\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "       </tr>\n", "       <tr class=\"bg-gray-100\">\n", "        <td>\n", "         Dividend Yield\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "       </tr>\n", "       <tr>\n", "        <td>\n", "         EV/EBITDA (x)\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "       </tr>\n", "       <tr class=\"bg-gray-100\">\n", "        <td>\n", "         FCF Yield %\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "       </tr>\n", "      </tbody>\n", "     </table>\n", "     <div class=\"text-[12px] text-gray-500 mt-1\">\n", "      Source: Visible Alpha\n", "     </div>\n", "    </div>\n", "    <div>\n", "     <h2 class=\"text-gray-500 text-[14px] font-bold mb-2\">\n", "      CREDIT &amp; CASHFLOW METRICS (USD, M)\n", "     </h2>\n", "     <table class=\"w-full text-[16px]\">\n", "      <thead>\n", "       <tr class=\"border-b-2 border-black\">\n", "        <th class=\"text-left font-normal pb-1\">\n", "         FY Dec\n", "        </th>\n", "        <th class=\"text-right font-normal pb-1\">\n", "         FY2021A\n", "        </th>\n", "        <th class=\"text-right font-normal pb-1\">\n", "         FY2022A\n", "        </th>\n", "        <th class=\"text-right font-normal pb-1\">\n", "         FY2023A\n", "        </th>\n", "        <th class=\"text-right font-normal pb-1\">\n", "         FY2024F\n", "        </th>\n", "        <th class=\"text-right font-normal pb-1\">\n", "         FY2025F\n", "        </th>\n", "       </tr>\n", "      </thead>\n", "      <tbody>\n", "       <tr class=\"bg-gray-100\">\n", "        <td>\n", "         Debt / Equity\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX.X\n", "        </td>\n", "       </tr>\n", "       <tr>\n", "        <td>\n", "         Net Debt / Equity\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "       </tr>\n", "       <tr class=\"bg-gray-100\">\n", "        <td>\n", "         Debt / Assets\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "       </tr>\n", "       <tr>\n", "        <td>\n", "         Net Debt / Assets\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "       </tr>\n", "       <tr class=\"bg-gray-100\">\n", "        <td>\n", "         EBITDA / Int Exp\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX\n", "        </td>\n", "       </tr>\n", "       <tr>\n", "        <td>\n", "         ST Debt / Total Debt\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "       </tr>\n", "       <tr class=\"bg-gray-100\">\n", "        <td>\n", "         Debt / EBITDA\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "       </tr>\n", "       <tr>\n", "        <td>\n", "         [Cash + CFO] / ST Debt\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         X.X\n", "        </td>\n", "       </tr>\n", "       <tr class=\"bg-gray-100\">\n", "        <td>\n", "         Receivables Days\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "       </tr>\n", "       <tr>\n", "        <td>\n", "         Days Payable\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XXX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "       </tr>\n", "       <tr class=\"bg-gray-100\">\n", "        <td>\n", "         Inventory Days\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "        <td class=\"text-right\">\n", "         XX.X\n", "        </td>\n", "       </tr>\n", "      </tbody>\n", "     </table>\n", "     <div class=\"text-[12px] text-gray-500 mt-1\">\n", "      Source: Visible Alpha\n", "     </div>\n", "    </div>\n", "   </div>\n", "  </div>\n", "  <div class=\"mt-8 text-[12px] text-gray-500 max-w-4xl mx-auto\">\n", "   <p>\n", "    Disclaimer: The information contained in this document is intended only for use by the person to whom it has been delivered and should not be disseminated or distributed to third parties without our prior written consent. The information contained herein is for informational purposes only and does not constitute an offer to sell or a solicitation of an offer to buy any securities. Please refer to Disclaimer found at the end of this document.\n", "   </p>\n", "  </div>\n", "  <div class=\"mt-4 flex justify-end max-w-4xl mx-auto\">\n", "   <img alt=\"FinRobot logo\" class=\"h-8\" height=\"30\" src=\"https://replicate.delivery/yhqm/dhv1qjDIYyotNl8spSOijZNet9Y0ekfRDNWnBWlo3V1O00xmA/out-0.png\" width=\"100\"/>\n", "  </div>\n", " </body>\n", "</html>\n", "\"\"\"\n", "# Write the HTML content to a file\n", "with open(\"/content/Equity_Research_Template_secondpage.html\", \"w\") as file:\n", "    file.write(html_content_second_page)\n", "\n", "print(\"HTML file created successfully.\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "VYn3KJXRpBVV", "outputId": "99755a44-6ced-4905-d6a2-c160b922d91b"}, "execution_count": 25, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["HTML file created successfully.\n"]}]}, {"cell_type": "code", "source": ["html_content_third_page = \"\"\"\n", "<html lang=\"en\">\n", " <head>\n", "  <meta charset=\"utf-8\"/>\n", "  <meta content=\"width=device-width, initial-scale=1.0\" name=\"viewport\"/>\n", "  <title>\n", "   Research Report\n", "  </title>\n", "  <script src=\"https://cdn.tailwindcss.com\">\n", "  </script>\n", "  <link href=\"https://fonts.googleapis.com/css2?family=DM+Sans:wght@300&amp;display=swap\" rel=\"stylesheet\"/>\n", "  <style>\n", "   body {\n", "            font-family: '<PERSON><PERSON> Sans', sans-serif;\n", "            font-weight: 300;\n", "        }\n", "        @page {\n", "            size: letter;\n", "        }\n", "  </style>\n", " </head>\n", " <body class=\"bg-white flex items-center justify-center\" style=\"width: 8.5in; height: 11in;\">\n", "  <div class=\"max-w-3xl mx-auto p-8\">\n", "   <div class=\"text-right text-sm text-gray-500 mb-4\">\n", "    FinRobot Research\n", "   </div>\n", "   <h2 class=\"text-lg font-normal text-gray-500 mb-2\">\n", "    Target Price &amp; Ratings History\n", "   </h2>\n", "   <hr class=\"border-gray-300 mb-4\"/>\n", "   <div class=\"bg-gray-100 p-4 mb-4 h-48\">\n", "    <!-- Placeholder for graph -->\n", "   </div>\n", "   <div class=\"text-xs mb-4 text-gray-500\">\n", "    Source: [Redacted]\n", "    <br/>\n", "    Analysts: [Redacted]\n", "   </div>\n", "   <div class=\"border border-black p-4 text-xs mb-4\">\n", "    <p class=\"font-bold mb-2\">\n", "     Research recommendations are based on an Absolute Total Return* Rating system, defined as follows:\n", "    </p>\n", "    <p class=\"mb-1\">\n", "     <span class=\"font-bold\">\n", "      STRONG BUY\n", "     </span>\n", "     &gt;20% total return over the next 3 months, with identifiable share price catalysts within this time frame\n", "    </p>\n", "    <p class=\"mb-1\">\n", "     <span class=\"font-bold\">\n", "      BUY\n", "     </span>\n", "     &gt;15% total return over the next 12 months for small caps, &gt;10% for large caps\n", "    </p>\n", "    <p class=\"mb-1\">\n", "     <span class=\"font-bold\">\n", "      HOLD\n", "     </span>\n", "     -10% to +15% total return over the next 12 months for small caps, -10% to +10% for large caps\n", "    </p>\n", "    <p class=\"mb-1\">\n", "     <span class=\"font-bold\">\n", "      FULLY VALUED\n", "     </span>\n", "     negative total return, i.e., &gt; -10% over the next 12 months\n", "    </p>\n", "    <p>\n", "     <span class=\"font-bold\">\n", "      SELL\n", "     </span>\n", "     negative total return of &gt; -20% over the next 3 months, with identifiable share price catalysts within this time frame\n", "    </p>\n", "   </div>\n", "   <div class=\"text-xs mb-4\">\n", "    Sources for all charts and tables are [Redacted] unless otherwise specified.\n", "   </div>\n", "   <div class=\"text-xs mb-4\">\n", "    <p class=\"font-bold mb-2\">\n", "     GENERAL DISCLOSURE/DISCLAIMER\n", "    </p>\n", "    <p class=\"mb-2\">\n", "     This report is prepared by [Redacted]. This report is solely intended for the clients of [Redacted], its respective connected and associated corporations and affiliates only and no part of this document may be (i) copied, photocopied or duplicated in any form or by any means or (ii) redistributed without the prior written consent of [Redacted].\n", "    </p>\n", "    <p class=\"mb-2\">\n", "     The research set out in this report is based on information obtained from sources believed to be reliable, but we (which collectively refers to [Redacted], its respective connected and associated corporations, affiliates and their respective directors, officers, employees and agents (collectively, the \"Group\") have not conducted due diligence on any of the companies, verified any information or sources or taken into account any other factors which we may consider to be relevant or appropriate in preparing the research. Accordingly, we do not make any representation or warranty as to the accuracy, completeness or correctness of the research set out in this report.\n", "    </p>\n", "    <p>\n", "     Any valuations, opinions, estimates, forecasts, ratings or risk assessments herein constitutes a judgment as of the date of this report, and there can be no assurance that future results or events will be consistent with any such valuations, opinions, estimates, forecasts, ratings or risk assessments. The information in this document is subject to change without notice, its accuracy is not guaranteed, it may be incomplete or condensed, it may not contain all material information concerning the company (or companies) referred to in this report and the Group is under no obligation to update the information in this report.\n", "    </p>\n", "    <p class=\"mb-2\">\n", "     This publication has not been reviewed or authorized by any regulatory authority in Singapore, Hong Kong or elsewhere. There is no planned schedule or frequency for updating research publication relating to any issuer.\n", "    </p>\n", "   </div>\n", "   <div class=\"text-xs\">\n", "    <p class=\"italic\">\n", "     Disclaimer: The information contained in this document is intended solely for use by the person to whom it has been delivered and should not be reproduced or distributed to third parties without our prior written consent. [Redacted] accepts no liability whatsoever with respect to the use of this document or its contents.\n", "    </p>\n", "    <p class=\"italic\">\n", "     Please refer to Disclaimer found at the end of the document.\n", "    </p>\n", "   </div>\n", "   <div class=\"absolute bottom-4 right-4\">\n", "    <div class=\"w-24 h-12 bg-gray-200\">\n", "     <!-- Placeholder for logo -->\n", "    </div>\n", "   </div>\n", "  </div>\n", " </body>\n", "</html>\n", "\"\"\"\n", "# Write the HTML content to a file\n", "with open(\"/content/Equity_Research_Template_thirdpage.html\", \"w\") as file:\n", "    file.write(html_content_third_page)\n", "\n", "print(\"HTML file created successfully.\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "pVpKknVxx0FV", "outputId": "2a978932-e9bc-4aeb-95d2-55bba325a23b"}, "execution_count": 27, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["HTML file created successfully.\n"]}]}, {"cell_type": "code", "source": ["html_content_fourth_page = \"\"\"\n", "<html lang=\"en\">\n", " <head>\n", "  <meta charset=\"utf-8\"/>\n", "  <meta content=\"width=device-width, initial-scale=1.0\" name=\"viewport\"/>\n", "  <title>\n", "   FinRobot Research Disclaimer\n", "  </title>\n", "  <script src=\"https://cdn.tailwindcss.com\">\n", "  </script>\n", "  <link href=\"https://fonts.googleapis.com/css2?family=DM+Sans:wght@300;700&amp;display=swap\" rel=\"stylesheet\"/>\n", "  <style>\n", "   body {\n", "            font-family: '<PERSON><PERSON> Sans', sans-serif;\n", "            font-weight: 300;\n", "        }\n", "        @page {\n", "            size: 8.5in 11in;\n", "        }\n", "  </style>\n", " </head>\n", " <body class=\"bg-white p-8\">\n", "  <div class=\"max-w-[8.5in] mx-auto\">\n", "   <div class=\"text-right text-xs mb-4 text-gray-500\">\n", "    FinRobot Research\n", "   </div>\n", "   <p class=\"text-xs mb-4\">\n", "    The valuations, opinions, estimates, forecasts, ratings or risk assessments described in this report were based upon a number of estimates and\n", "            assumptions and are inherently subject to significant uncertainties and contingencies. It can be expected that one or more of the estimates on\n", "            which the valuations, opinions, estimates, forecasts, ratings or risk assessments were based will not materialize or will vary significantly from actual\n", "            results. Therefore, the inclusion of the valuations, opinions, estimates, forecasts, ratings or risk assessments described herein IS NOT TO BE RELIED\n", "            UPON as a representation and/or warranty by the FinRobot Research and/or any persons associated with the aforesaid entities, that:\n", "   </p>\n", "   <p class=\"text-xs mb-4 pl-4\">\n", "    (a) such valuations, opinions, estimates, forecasts, ratings or risk assessments or their underlying assumptions will be achieved, and\n", "            (b) there is any assurance that future results or events will be consistent with any such valuations, opinions, estimates, forecasts, ratings or risk\n", "            assessments stated therein.\n", "   </p>\n", "   <p class=\"text-xs mb-4\">\n", "    Please contact the primary analyst for valuation methodologies and assumptions associated with the covered companies or price targets.\n", "   </p>\n", "   <p class=\"text-xs mb-4\">\n", "    Any assumptions made in this report that refers to commodities, are for the purposes of making forecasts for the company (or companies)\n", "            mentioned herein. They are not to be construed as recommendations to trade in the physical commodity or in the futures contract relating to the\n", "            commodity referred to in this report.\n", "   </p>\n", "   <p class=\"text-sm font-bold mb-2 uppercase\">\n", "    ANALYST CERTIFICATION\n", "   </p>\n", "   <p class=\"text-xs mb-4\">\n", "    The research analyst(s) primarily responsible for the content of this research report, in part or in whole, certifies that the views about the companies\n", "            and their securities expressed in this report accurately reflect his/her personal views. The analyst(s) also certifies that no part of his/her compensation\n", "            was, is, or will be, directly or indirectly, related to specific recommendations or views expressed in the report. The research analyst\n", "            (s) primarily responsible for the content of this research report, in part or in whole, certifies that he or his associate1 does not serve as an officer\n", "            of the issuer or the new listing applicant (which includes in the case of a real estate investment trust, an officer of the management company of the real estate investment trust;\n", "            and in the case of any other entity, an officer or its equivalent counterparty of the entity who is responsible for\n", "            the management of the issuer or the new listing applicant) and the research analyst(s) primarily responsible for the content of this research report\n", "            or his associate does not have financial interests2 in relation to an issuer or a new listing applicant that the analyst reviews. FinRobot Research has procedures\n", "            in place to eliminate, avoid and manage any potential conflicts of interests that may arise in connection with the production of research reports. The\n", "            research analyst(s) responsible for this report operates as part of a separate and independent team to the investment banking function of the FinRobot Research\n", "            and procedures are in place to ensure that confidential information held by either the research or investment banking function is handled\n", "            appropriately. There is no direct link of FinRobot Research's compensation to any specific investment banking function of the FinRobot Research.\n", "   </p>\n", "   <p class=\"text-sm font-bold mb-2 uppercase\">\n", "    COMPANY-SP<PERSON>IF<PERSON> / REGULATORY DISCLOSURES\n", "   </p>\n", "   <ol class=\"list-decimal list-inside text-xs mb-4\">\n", "    <li class=\"mb-2 pl-4\">\n", "     FinRobot Research, its subsidiaries and/or other affiliates do not have a proprietary position in the securities recommended in this report as of 31 Mar 2024.\n", "    </li>\n", "   </ol>\n", "   <p class=\"text-xs font-bold mb-2\">\n", "    Compensation for investment banking services:\n", "   </p>\n", "   <ol class=\"list-decimal list-inside text-xs mb-4\" start=\"2\">\n", "    <li class=\"pl-4\">\n", "     FinRobot Research does not have its own investment banking or research department, nor has it participated in any public offering of securities as a\n", "                manager or co-manager or in any other investment banking transaction in the past twelve months. Any persons wishing to obtain further\n", "                information, including any clarification on disclosures in this disclaimer, or to effect a transaction in any security discussed in this document\n", "                should contact FinRobot Research exclusively.\n", "    </li>\n", "   </ol>\n", "   <p class=\"text-xs font-bold mb-2\">\n", "    Disclosure of previous investment recommendation produced:\n", "   </p>\n", "   <ol class=\"list-decimal list-inside text-xs mb-4\" start=\"3\">\n", "    <li class=\"pl-4\">\n", "     FinRobot Research, their subsidiaries and/or other affiliates may have published other investment recommendations in respect of the same securities / instruments recommended in this research report during the preceding 12\n", "                months. Please contact the primary analyst listed on page 1 of this report to view previous investment recommendations published by FinRobot Research,\n", "                their subsidiaries and/or other affiliates in the preceding 12 months.\n", "    </li>\n", "   </ol>\n", "   <hr class=\"my-4 border-gray-300\"/>\n", "   <div class=\"text-[10px] mt-4\">\n", "    <p class=\"mb-2\">\n", "     1 An associate is defined as (i) the spouse, or any minor child (natural or adopted) or minor step-child, of the analyst; (ii) the trustee of a trust of which the analyst, his spouse,\n", "                minor child (natural or adopted) or minor step-child, is a beneficiary or discretionary object; or (iii) another person accustomed or obliged to act in accordance\n", "                with the directions or instructions of the analyst.\n", "    </p>\n", "    <p>\n", "     2 Financial interest is defined as interests that are commonly known financial interest, such as investment in the securities in respect of an issuer or a new listing applicant,\n", "                or financial accommodation arrangement between the issuer or the new listing applicant and the firm or analysis. This term does not include commercial lending\n", "                conducted at arm's length, or investments in any collective investment scheme other than an issuer or new listing applicant notwithstanding the fact that the scheme\n", "                has investments in securities in respect of an issuer or a new listing applicant.\n", "    </p>\n", "   </div>\n", "   <p class=\"text-xs mt-8\">\n", "    Disclaimer: The information contained in this document is intended only for use during the presentation and should not be disseminated or distributed to parties outside the presentation.\n", "            FinRobot Research accepts no liability whatsoever with respect to the use of this document or its contents.\n", "   </p>\n", "   <div class=\"mt-8 text-right\">\n", "    <img alt=\"FinRobot Research logo in black and white\" class=\"inline-block\" height=\"50\" src=\"https://replicate.delivery/yhqm/nDCMpKlx297xGRYwD9pwusNeTv8HaPqV2n4XDGotR4w8gdsJA/out-0.png\" width=\"100\"/>\n", "   </div>\n", "  </div>\n", " </body>\n", "</html>\n", "\"\"\"\n", "# Write the HTML content to a file\n", "with open(\"/content/Equity_Research_Template_fourthpage.html\", \"w\") as file:\n", "    file.write(html_content_fourth_page)\n", "\n", "print(\"HTML file created successfully.\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "mAMM7-6fPihw", "outputId": "31ce5f03-0474-4754-eea2-75a4ef1dfa02"}, "execution_count": 28, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["HTML file created successfully.\n"]}]}, {"cell_type": "code", "source": ["html_content_fifth_page = \"\"\"\n", "<html lang=\"en\">\n", " <head>\n", "  <meta charset=\"utf-8\"/>\n", "  <meta content=\"width=device-width, initial-scale=1.0\" name=\"viewport\"/>\n", "  <title>\n", "   FinRobot Research - Restrictions on Distribution\n", "  </title>\n", "  <link href=\"https://fonts.googleapis.com/css2?family=DM+Sans:wght@300;700&amp;display=swap\" rel=\"stylesheet\"/>\n", "  <style>\n", "   body {\n", "            font-family: '<PERSON><PERSON> Sans', sans-serif;\n", "            font-weight: 300;\n", "            line-height: 1.6;\n", "            margin: 0;\n", "            padding: 20px;\n", "            font-size: 12px;\n", "            width: 8.5in;\n", "            height: 11in;\n", "            box-sizing: border-box;\n", "        }\n", "        .header {\n", "            text-align: right;\n", "            color: #808080;\n", "            font-size: 12px;\n", "            margin-bottom: 20px;\n", "        }\n", "        h2 {\n", "            font-size: 14px;\n", "            font-weight: 700;\n", "            margin-bottom: 5px;\n", "        }\n", "        p {\n", "            margin: 0 0 10px 0;\n", "        }\n", "        .section {\n", "            margin-bottom: 15px;\n", "        }\n", "        .footer {\n", "            margin-top: 20px;\n", "            text-align: right;\n", "        }\n", "        .logo {\n", "            width: 50px;\n", "            height: auto;\n", "        }\n", "  </style>\n", " </head>\n", " <body>\n", "  <div class=\"header\">\n", "   FinRobot Research\n", "  </div>\n", "  <div class=\"section\">\n", "   <h2>\n", "    RESTRICTIONS ON DISTRIBUTION\n", "   </h2>\n", "   <p>\n", "    <strong>\n", "     General\n", "    </strong>\n", "    This report is not directed to, or intended for distribution to or use by, any person or entity who is a citizen or resident of or located in any locality, state, country or other jurisdiction where such distribution, publication, availability or use would be contrary to law or regulation.\n", "   </p>\n", "  </div>\n", "  <div class=\"section\">\n", "   <h2>\n", "    Country A\n", "   </h2>\n", "   <p>\n", "    This report is being distributed in Country A by Company X, Company Y (\"CY\") or Company Z. Company X holds Financial Services Licence no. 123456.\n", "   </p>\n", "   <p>\n", "    Company X, CY and Company Z are exempted from the requirement to hold a Financial Services Licence under the Corporation Act 2001 (\"CA\") in respect of financial services provided to the recipients. Both Company X and CY are regulated by the Financial Authority of Country B under the laws of Country B, and Company Z is regulated by the Securities Commission of Country C under the laws of Country C, which differ from Country A laws.\n", "   </p>\n", "   <p>\n", "    Distribution of this report is intended only for \"wholesale investors\" within the meaning of the CA.\n", "   </p>\n", "  </div>\n", "  <div class=\"section\">\n", "   <h2>\n", "    Country C\n", "   </h2>\n", "   <p>\n", "    This report has been prepared by a personnel of Company X, who is not licensed by the Securities and Futures Commission of Country C to carry on the regulated activity of advising on securities in Country C pursuant to the Securities and Futures Ordinance (Chapter 571 of the Laws of Country C). This report is being distributed in Country C and is attributable to Company X (Country C) Limited (CX), a registered institution registered with the Securities and Futures Commission of Country C to carry out the regulated activity of advising on securities pursuant to the Securities and Futures Ordinance (Chapter 571 of the Laws of Country C). Company X, Country C Branch is a limited liability company incorporated in Country B.\n", "   </p>\n", "   <p>\n", "    This report is being distributed in Country C by Company X, Company X (Country C) Limited and Company Y (Country C) Limited, all of which are registered with or licensed by the Securities and Futures Commission of Country C to carry out the regulated activity of advising on securities. Company X, Country C Branch is a limited liability company incorporated in Country B.\n", "   </p>\n", "   <p>\n", "    For any query regarding the materials herein, please contact <PERSON> (Reg No. AB1234) at <EMAIL>\n", "   </p>\n", "  </div>\n", "  <div class=\"section\">\n", "   <h2>\n", "    Country D\n", "   </h2>\n", "   <p>\n", "    This report is being distributed in Country D by PT Company Y Sekuritas Country D.\n", "   </p>\n", "  </div>\n", "  <div class=\"section\">\n", "   <h2>\n", "    Country E\n", "   </h2>\n", "   <p>\n", "    This report is distributed in Country E by AllianceXYZ Research Sdn Bhd (\"AXYZR\"). Recipients of this report, received from AXYZR are to contact the undersigned at 123-456-7890 in respect of any matters arising from or in connection with this report. In addition to the General Disclosure/Disclaimer found at the preceding page, recipients of this report are advised that AXYZR (the preparer of this report), its holding company Alliance Investment Bank Berhad, their respective connected and associated corporations, affiliates, their directors, officers, employees, agents and parties related or associated with any of them may have positions in, and may effect transactions in the securities mentioned herein and may also perform or seek to perform broking, investment banking/corporate advisory and other services for the subject companies. They may also have received compensation and/or seek to obtain compensation for broking, investment banking/corporate advisory and other services from the subject companies.\n", "   </p>\n", "   <p>\n", "    <PERSON>, Executive Director, AXYZR\n", "   </p>\n", "  </div>\n", "  <div class=\"section\">\n", "   <h2>\n", "    Country F\n", "   </h2>\n", "   <p>\n", "    This report is distributed in Country F by Company X (Company Regn. No. 123456789A) or CY (Company Regn No. 987654321B), both of which are Exempt Financial Advisers as defined in the Financial Advisers Act and regulated by the Monetary Authority of Country F. Company X and/or CY, may distribute reports produced by its respective foreign entities, affiliates or other foreign research houses pursuant to an arrangement under Regulation 32C of the Financial Advisers Regulations. Where the report is distributed in Country F to a person who is not an Accredited Investor, Expert Investor or an\n", "   </p>\n", "  </div>\n", "  <div class=\"section\">\n", "   <p>\n", "    Disclaimer: The information contained in this document is intended only for use by the person to whom it has been delivered and should not be disseminated or distributed to third parties without our prior written consent. Company X accepts no liability whatsoever with respect to the use of this document or its contents. Please refer to Disclaimer found at the end of this document.\n", "   </p>\n", "  </div>\n", "  <div class=\"footer\">\n", "   <img alt=\"FinRobot logo\" class=\"logo\" height=\"50\" src=\"https://replicate.delivery/yhqm/sJtrgYOYss4VNJUwU0EanYuI3iicgdFPf5wFqk0Ihm0V4dsJA/out-0.png\" width=\"50\"/>\n", "  </div>\n", " </body>\n", "</html>\n", "\"\"\"\n", "# Write the HTML content to a file\n", "with open(\"/content/Equity_Research_Template_fifthpage.html\", \"w\") as file:\n", "    file.write(html_content_fifth_page)\n", "\n", "print(\"HTML file created successfully.\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "7GCAGryd-c7L", "outputId": "cc855c73-10bf-45b0-f6c5-d496d8171114"}, "execution_count": 31, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["HTML file created successfully.\n"]}]}, {"cell_type": "code", "source": ["html_content_last_page =\"\"\"\n", "<html lang=\"en\">\n", " <head>\n", "  <meta charset=\"utf-8\"/>\n", "  <meta content=\"width=device-width, initial-scale=1.0\" name=\"viewport\"/>\n", "  <title>\n", "   FinRobot Research\n", "  </title>\n", "  <link href=\"https://fonts.googleapis.com/css2?family=DM+Sans:wght@300;700&amp;display=swap\" rel=\"stylesheet\"/>\n", "  <style>\n", "   body {\n", "            font-family: '<PERSON><PERSON> Sans', sans-serif;\n", "            font-weight: 300;\n", "            margin: 0;\n", "            padding: 20px;\n", "            color: #333;\n", "            width: 8.5in;\n", "            height: 11in;\n", "            box-sizing: border-box;\n", "            display: flex;\n", "            flex-direction: column;\n", "        }\n", "        .header {\n", "            text-align: right;\n", "            color: #808080;\n", "            font-size: 12px;\n", "            margin-bottom: 20px;\n", "        }\n", "        h2 {\n", "            font-size: 14px;\n", "            font-weight: 700;\n", "            margin-bottom: 5px;\n", "        }\n", "        p {\n", "            font-size: 12px;\n", "            line-height: 1.4;\n", "            margin-bottom: 20px;\n", "        }\n", "        .offices-header {\n", "            font-size: 14px;\n", "            font-weight: 700;\n", "            color: #808080;\n", "            margin-top: 30px;\n", "            margin-bottom: 5px;\n", "        }\n", "        .offices-line {\n", "            border-top: 1px solid #808080;\n", "            margin-bottom: 20px;\n", "        }\n", "        .offices {\n", "            display: grid;\n", "            grid-template-columns: 1fr 1fr;\n", "            gap: 20px;\n", "        }\n", "        .office {\n", "            font-size: 12px;\n", "        }\n", "        .office h3 {\n", "            font-size: 14px;\n", "            font-weight: 700;\n", "            margin-bottom: 5px;\n", "        }\n", "        .office p {\n", "            margin: 0;\n", "            line-height: 1.3;\n", "        }\n", "        .footer {\n", "            font-size: 10px;\n", "            color: #666;\n", "            margin-top: auto;\n", "            padding-top: 20px;\n", "        }\n", "        .logo {\n", "            text-align: right;\n", "            margin-top: 10px;\n", "        }\n", "  </style>\n", " </head>\n", " <body>\n", "  <div class=\"header\">\n", "   FinRobot Research\n", "  </div>\n", "  <h2>\n", "   United States\n", "  </h2>\n", "  <p>\n", "   This report was prepared by FinRobot Ltd. FinRobot/USA did not participate in its preparation. The research analyst(s) named on this report are not registered as research analysts with FINRA and are not associated persons of FinRobot/USA. The research analyst(s) are not subject to FINRA Rule 2241 restrictions on analyst compensation, communications with a subject company, public appearances and trading securities held by a research analyst. This report is being distributed in the United States by FinRobot/USA, which accepts responsibility for its contents. This report may only be distributed to Major U.S. Institutional Investors and to such other institutional investors and qualified persons as FinRobot/USA may authorize. Any U.S. person receiving this report who wishes to effect transactions in any securities referred to herein should contact FinRobot/USA directly and not its affiliate.\n", "  </p>\n", "  <h2>\n", "   Other Jurisdictions\n", "  </h2>\n", "  <p>\n", "   In any other jurisdictions, except if otherwise restricted by laws or regulations, this report is intended only for qualified, professional, institutional or sophisticated investors as defined in the laws and regulations of such jurisdictions.\n", "  </p>\n", "  <div class=\"offices-header\">\n", "   FINROBOT REGIONAL RESEARCH OFFICES\n", "  </div>\n", "  <div class=\"offices-line\">\n", "  </div>\n", "  <div class=\"offices\">\n", "   <div class=\"office\">\n", "    <h3>\n", "     REGION A\n", "    </h3>\n", "    <p>\n", "     FinRobot (Region A) Ltd\n", "     <br/>\n", "     Contact: <PERSON>\n", "     <br/>\n", "     123 Financial Street,\n", "     <br/>\n", "     Business District,\n", "     <br/>\n", "     City A, Country A\n", "     <br/>\n", "     Tel: ****** 567 8901\n", "     <br/>\n", "     Fax: ****** 567 8902\n", "     <br/>\n", "     e-mail: <EMAIL>\n", "    </p>\n", "   </div>\n", "   <div class=\"office\">\n", "    <h3>\n", "     REGION B\n", "    </h3>\n", "    <p>\n", "     FinRobot Ltd\n", "     <br/>\n", "     Contact: <PERSON>\n", "     <br/>\n", "     456 Market Avenue,\n", "     <br/>\n", "     Financial Hub Tower 2\n", "     <br/>\n", "     City B 54321\n", "     <br/>\n", "     Tel: ****** 543 2100\n", "     <br/>\n", "     e-mail: <EMAIL>\n", "     <br/>\n", "     Company Regn. No. 987654321B\n", "    </p>\n", "   </div>\n", "   <div class=\"office\">\n", "    <h3>\n", "     REGION C\n", "    </h3>\n", "    <p>\n", "     FinRobot Securities (Region C)\n", "     <br/>\n", "     Contact: <PERSON>\n", "     <br/>\n", "     FinRobot Tower\n", "     <br/>\n", "     789 Trader's Lane, 15/F\n", "     <br/>\n", "     City C 13579, Country C\n", "     <br/>\n", "     Tel: ****** 1012 1416\n", "     <br/>\n", "     Fax: ****** 1012 1417\n", "     <br/>\n", "     e-mail: <EMAIL>\n", "    </p>\n", "   </div>\n", "   <div class=\"office\">\n", "    <h3>\n", "     REGION D\n", "    </h3>\n", "    <p>\n", "     FinRobot Securities (Region D) Co Ltd\n", "     <br/>\n", "     Contact: <PERSON>\n", "     <br/>\n", "     101 Investment Building,\n", "     <br/>\n", "     11th-12th Floor\n", "     <br/>\n", "     Finance Road, Business Park,\n", "     <br/>\n", "     City D, Country D 24680\n", "     <br/>\n", "     Tel. ****** 1113 1517\n", "     <br/>\n", "     Fax: ****** 1113 1518\n", "     <br/>\n", "     e-mail: <EMAIL>\n", "     <br/>\n", "     Company Regn. No 135792468D\n", "     <br/>\n", "     Securities and Exchange Commission, Country D\n", "    </p>\n", "   </div>\n", "  </div>\n", "  <div class=\"footer\">\n", "   <p>\n", "    Disclaimer: The information contained in this document is intended only for use by the person to whom it has been delivered and should not be disseminated or distributed to third parties without our prior written consent. FinRobot accepts no liability whatsoever with respect to the use of this document or its contents. Please refer to Disclaimer found at the end of this document.\n", "   </p>\n", "   <div class=\"logo\">\n", "    <img alt=\"FinRobot logo in black with white text\" height=\"20\" src=\"https://replicate.delivery/yhqm/MMaixaZ2CG7SJtcbfOHllDFnq3z6UHn3dZSfU5hr2LOJ47YTA/out-0.png\" width=\"50\"/>\n", "   </div>\n", "  </div>\n", " </body>\n", "</html>\n", "\"\"\"\n", "# Write the HTML content to a file\n", "with open(\"/content/Equity_Research_Template_lastpage.html\", \"w\") as file:\n", "    file.write(html_content_last_page)\n", "\n", "print(\"HTML file created successfully.\")\n", "#"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "N5nUTxl4-leI", "outputId": "2543b3a9-2dda-47d8-aef2-32afa0e8fed1"}, "execution_count": 32, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["HTML file created successfully.\n"]}]}, {"cell_type": "markdown", "source": ["Combine and Transfer HTML to PDF"], "metadata": {"id": "vpAwWoziiyOq"}}, {"cell_type": "code", "source": ["!pip install ironpdf"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "mL2vJTonist5", "outputId": "113e17fe-1cb5-4516-ecc9-8f68daa530c0"}, "execution_count": 1, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting ironpdf\n", "  Downloading IronPdf-2024.8.1.3-py37-none-any.whl.metadata (9.7 kB)\n", "Collecting pythonnet (from ironpdf)\n", "  Downloading pythonnet-3.0.3-py3-none-any.whl.metadata (6.6 kB)\n", "Collecting clr-loader<0.3.0,>=0.2.6 (from pythonnet->ironpdf)\n", "  Downloading clr_loader-0.2.6-py3-none-any.whl.metadata (1.4 kB)\n", "Requirement already satisfied: cffi>=1.13 in /usr/local/lib/python3.10/dist-packages (from clr-loader<0.3.0,>=0.2.6->pythonnet->ironpdf) (1.17.0)\n", "Requirement already satisfied: pycparser in /usr/local/lib/python3.10/dist-packages (from cffi>=1.13->clr-loader<0.3.0,>=0.2.6->pythonnet->ironpdf) (2.22)\n", "Downloading IronPdf-2024.8.1.3-py37-none-any.whl (10.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m10.4/10.4 MB\u001b[0m \u001b[31m45.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading pythonnet-3.0.3-py3-none-any.whl (290 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m291.0/291.0 kB\u001b[0m \u001b[31m17.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading clr_loader-0.2.6-py3-none-any.whl (51 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m51.3/51.3 kB\u001b[0m \u001b[31m3.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: clr-loader, pythonnet, ironpdf\n", "Successfully installed clr-loader-0.2.6 ironpdf-2024.8.1.3 pythonnet-3.0.3\n"]}]}, {"cell_type": "code", "source": ["from ironpdf import *\n", "\n", "# Instantiate the renderer\n", "renderer = ChromePdfRenderer()\n", "\n"], "metadata": {"id": "Ct7DPtWTj3WF"}, "execution_count": null, "outputs": []}]}