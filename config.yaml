# FinRobot-Pro Configuration
# Environment-overrideable via Hydra

# Data Sources Configuration
data_sources:
  alpha_vantage:
    base_url: "https://www.alphavantage.co/query"
    rate_limit: 5  # requests per minute
    timeout: 30
  
  finnhub:
    base_url: "https://finnhub.io/api/v1"
    rate_limit: 60  # requests per minute
    timeout: 30
  
  polygon:
    base_url: "https://api.polygon.io"
    rate_limit: 5  # requests per minute
    timeout: 30
  
  tiingo:
    base_url: "https://api.tiingo.com/tiingo"
    rate_limit: 1000  # requests per hour
    timeout: 30

# Caching Configuration
cache:
  ttl_hours: 6
  max_size_gb: 10
  directory: "./cache"

# Data Processing
data:
  timezone: "UTC"
  missing_data_threshold: 0.05  # 5% missing data tolerance
  outlier_threshold: 5.0  # z-score threshold
  min_history_days: 252  # minimum trading days for analysis

# Feature Engineering
features:
  technical_indicators:
    volatility_windows: [5, 10, 20, 60]
    rsi_period: 14
    macd_fast: 12
    macd_slow: 26
    macd_signal: 9
    atr_period: 14
    bollinger_period: 20
    bollinger_std: 2
  
  fundamental_metrics:
    yoy_periods: [1, 4]  # quarters for YoY comparison
    growth_metrics: ["eps", "revenue", "gross_margin"]
  
  sentiment:
    news_lookback_days: 30
    sentiment_sources: ["news", "social"]

# Model Configuration
models:
  random_seed: 42
  
  arima:
    max_p: 5
    max_d: 2
    max_q: 5
    seasonal: true
    
  prophet:
    seasonality_mode: "multiplicative"
    yearly_seasonality: true
    weekly_seasonality: true
    daily_seasonality: false
    
  lightgbm:
    objective: "regression"
    metric: "rmse"
    boosting_type: "gbdt"
    num_leaves: 31
    learning_rate: 0.05
    feature_fraction: 0.9
    
  xgboost:
    objective: "reg:squarederror"
    eval_metric: "rmse"
    max_depth: 6
    learning_rate: 0.05
    subsample: 0.8
    
  lstm:
    hidden_size: 64
    num_layers: 2
    dropout: 0.2
    sequence_length: 60
    
  tft:
    hidden_size: 64
    lstm_layers: 2
    attention_head_size: 4
    dropout: 0.1
    hidden_continuous_size: 8

# Hyperparameter Optimization
hyperopt:
  algorithm: "tpe"
  max_evals: 100
  early_stopping:
    patience: 10
    min_delta: 0.001
  validation:
    method: "walk_forward"
    n_splits: 5
    test_size: 0.2

# Ensemble Configuration
ensemble:
  method: "ridge"
  alpha: 1.0
  monte_carlo:
    n_simulations: 1000
    distribution: "student_t"
    confidence_intervals: [0.8, 0.95]

# Training Configuration
training:
  train_ratio: 0.8
  val_ratio: 0.1
  test_ratio: 0.1
  min_train_samples: 500
  
  early_stopping:
    patience: 20
    min_delta: 0.001
    monitor: "val_loss"
    
  overfitting_detection:
    max_train_val_ratio: 1.2
    min_directional_accuracy: 0.52

# Forecasting
forecasting:
  horizons: [1, 5, 10, 20]  # days
  confidence_levels: [0.8, 0.95]
  min_confidence_score: 0.3

# Workflows
workflows:
  daily_analysis:
    schedule: "0 6 * * 1-5"  # 6 AM weekdays
    retry_attempts: 3
    retry_delay: 300  # seconds
    
  monthly_forecast:
    schedule: "0 8 1 * *"  # 8 AM first day of month
    retry_attempts: 3
    retry_delay: 600

# Reporting
reporting:
  output_directory: "./reports"
  formats: ["json", "html", "pdf"]
  include_charts: true
  disclaimer: |
    This analysis is for informational purposes only and should not be 
    construed as financial advice. Past performance does not guarantee 
    future results. Please consult with qualified financial professionals 
    before making investment decisions.

# Logging
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "./logs/finrobot.log"
  max_bytes: 10485760  # 10MB
  backup_count: 5

# MLflow Tracking
mlflow:
  tracking_uri: "./mlruns"
  experiment_name: "finrobot_forecasting"
  
# Risk Management
risk:
  max_position_size: 0.1  # 10% of portfolio
  stop_loss: 0.05  # 5%
  take_profit: 0.15  # 15%
  max_drawdown: 0.2  # 20%

# Performance Monitoring
monitoring:
  drift_detection:
    threshold: 5.0  # z-score
    window_size: 30  # days
  
  model_performance:
    retraining_threshold: 0.1  # 10% performance degradation
    evaluation_frequency: 7  # days
