{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# FinGPT-Forecaster Re-implemented with FinRobot"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this demo, we set up an agent to emulate the behavior of model in the fingpt-forecaster project with AutoGen, which takes a company's ticker symbol, recent basic financials and market news as input and predict its stock movements.\n", "\n", "For detail of the original project, check out  [FinGPT-Forecaster](https://github.com/AI4Finance-Foundation/FinGPT/tree/master/fingpt/FinGPT_Forecaster)!  🔥[Demo](https://huggingface.co/spaces/FinGPT/FinGPT-Forecaster), [Medium Blog](https://medium.datadriveninvestor.com/introducing-fingpt-forecaster-the-future-of-robo-advisory-services-50add34e3d3c) & [Model](https://huggingface.co/FinGPT/fingpt-forecaster_dow30_llama2-7b_lora) on Huggingface🤗!"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import autogen\n", "from autogen.cache import Cache\n", "\n", "from finrobot.utils import get_current_date, register_keys_from_json\n", "from finrobot.data_source import FinnHubUtils, YFinanceUtils"]}, {"cell_type": "markdown", "metadata": {}, "source": ["After importing all the necessary packages and functions, we first instantiate a market analysis assistant and a user proxy using the agent classes defined by AutoGen. We also need the config for OpenAI & Finnhub here. \n", "- for openai configuration, rename OAI_CONFIG_LIST_sample to OAI_CONFIG_LIST and replace the api keys\n", "- for finnhub configuration, rename config_api_keys_sample to config_api_keys and replace the api keys"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Read OpenAI API keys from a JSON file\n", "config_list = autogen.config_list_from_json(\n", "    \"../OAI_CONFIG_LIST\",\n", "    filter_dict={\"model\": [\"gpt-4-0125-preview\"]},\n", ")\n", "llm_config = {\"config_list\": config_list, \"timeout\": 120, \"temperature\": 0}\n", "\n", "# Register FINNHUB API keys\n", "register_keys_from_json(\"../config_api_keys\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["analyst = autogen.AssistantAgent(\n", "    name=\"Market_Analyst\",\n", "    system_message=\"As a Market Analyst, one must possess strong analytical and problem-solving abilities, collect necessary financial information and aggregate them based on client's requirement.\"\n", "    \"For coding tasks, only use the functions you have been provided with. Reply TERMINATE when the task is done.\",\n", "    llm_config=llm_config,\n", ")\n", "\n", "user_proxy = autogen.UserProxyAgent(\n", "    name=\"User_Proxy\",\n", "    is_termination_msg=lambda x: x.get(\"content\", \"\") and x.get(\n", "        \"content\", \"\").endswith(\"TERMINATE\"),\n", "    human_input_mode=\"NEVER\",\n", "    max_consecutive_auto_reply=10,\n", "    code_execution_config={\n", "        \"work_dir\": \"coding\",\n", "        \"use_docker\": <PERSON><PERSON><PERSON>,\n", "    },  # Please set use_docker=True if docker is available to run the generated code. Using docker is safer than running the generated code directly.\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Then we register our predefined functions with the userproxy.\n", "\n", "Following our implementation with the fingpt-forecaster, we combined news/financials calls to the Finnhub API and stock data calls to the YFinance API."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from finrobot.toolkits import register_toolkits\n", "\n", "tools = [\n", "    {\n", "        \"function\": FinnHubUtils.get_company_profile,\n", "        \"name\": \"get_company_profile\",\n", "        \"description\": \"get a company's profile information\"\n", "    },\n", "    {\n", "        \"function\": FinnHubUtils.get_company_news,\n", "        \"name\": \"get_company_news\",\n", "        \"description\": \"retrieve market news related to designated company\"\n", "    },\n", "    {\n", "        \"function\": FinnHubUtils.get_basic_financials,\n", "        \"name\": \"get_financial_basics\",\n", "        \"description\": \"get latest financial basics for a designated company\"\n", "    },\n", "    {\n", "        \"function\": YFinanceUtils.get_stock_data,\n", "        \"name\": \"get_stock_data\",\n", "        \"description\": \"retrieve stock price data for designated ticker symbol\"\n", "    }\n", "]\n", "register_toolkits(tools, analyst, user_proxy)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Start the conversation, asking the agent to call all the functions, and see how it aggregates all the information and leads to the conclusion."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33mUser_Proxy\u001b[0m (to Market_Analyst):\n", "\n", "Use all the tools provided to retrieve information available for APPLE upon 2024-04-11. Analyze the positive developments and potential concerns of APPLE with 2-4 most important factors respectively and keep them concise. Most factors should be inferred from company related news. Then make a rough prediction (e.g. up/down by 2-3%) of the APPLE stock price movement for next week. Provide a summary analysis to support your prediction.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mMarket_Analyst\u001b[0m (to User_Proxy):\n", "\n", "\u001b[32m***** Suggested tool call (call_74K9pDrP0tgi2Su0GD4e0t1E): get_company_profile *****\u001b[0m\n", "Arguments: \n", "{\"symbol\": \"AAPL\"}\n", "\u001b[32m************************************************************************************\u001b[0m\n", "\u001b[32m***** Suggested tool call (call_BKKuzs9lNxWpwBNMkIOtTm8z): get_company_news *****\u001b[0m\n", "Arguments: \n", "{\"symbol\": \"AAPL\", \"start_date\": \"2024-04-04\", \"end_date\": \"2024-04-11\"}\n", "\u001b[32m*********************************************************************************\u001b[0m\n", "\u001b[32m***** Suggested tool call (call_mQTuLkVVcabsqBeOFkSQ83Oe): get_financial_basics *****\u001b[0m\n", "Arguments: \n", "{\"symbol\": \"AAPL\"}\n", "\u001b[32m*************************************************************************************\u001b[0m\n", "\u001b[32m***** Suggested tool call (call_KMaKxoAo0XalrinyToyj8Tss): get_stock_data *****\u001b[0m\n", "Arguments: \n", "{\"symbol\": \"AAPL\", \"start_date\": \"2024-03-11\", \"end_date\": \"2024-04-11\"}\n", "\u001b[32m*******************************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION get_company_profile...\u001b[0m\n", "Finnhub client initialized\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION get_company_news...\u001b[0m\n", "Finnhub client initialized\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION get_financial_basics...\u001b[0m\n", "Finnhub client initialized\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION get_stock_data...\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[*********************100%%**********************]  1 of 1 completed"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[33mUser_Proxy\u001b[0m (to Market_Analyst):\n", "\n", "\u001b[33mUser_Proxy\u001b[0m (to Market_Analyst):\n", "\n", "\u001b[32m***** Response from calling tool (call_74K9pDrP0tgi2Su0GD4e0t1E) *****\u001b[0m\n", "[Company Introduction]:\n", "\n", "Apple Inc is a leading entity in the Technology sector. Incorporated and publicly traded since 1980-12-12, the company has established its reputation as one of the key players in the market. As of today, Apple Inc has a market capitalization of 2590838.74 in USD, with 15441.88 shares outstanding.\n", "\n", "Apple Inc operates primarily in the US, trading under the ticker AAPL on the NASDAQ NMS - GLOBAL MARKET. As a dominant force in the Technology space, the company continues to innovate and drive progress within the industry.\n", "\u001b[32m**********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mUser_Proxy\u001b[0m (to Market_Analyst):\n", "\n", "\u001b[32m***** Response from calling tool (call_BKKuzs9lNxWpwBNMkIOtTm8z) *****\u001b[0m\n", "             date                                                                                              headline                                                                                                                                                        summary\n", "0  20240405122000                      Europe is More Eager than U.S. to Cut Rates: This Could Hurt U.S. Multinationals          Looking for stock market analysis and research with proves results? Zacks.com offers in-depth financial research with over 30years of proven results.\n", "1  20240405122700                   Europe Is More Eager Than the U.S. to Cut Rates: This Threatens U.S. Multinationals          Looking for stock market analysis and research with proves results? Zacks.com offers in-depth financial research with over 30years of proven results.\n", "2  20240405165700                                       Apple Layoffs 2024: What to Know About the Latest AAPL Job Cuts          Looking for stock market analysis and research with proves results? Zacks.com offers in-depth financial research with over 30years of proven results.\n", "3  20240405175913                                              Apple: Antitrust Case Could Prove Positive For Investors  Apple is a world-class investor with one of the strongest brands whose stock is temporarily under pressure. Find out if now is a good time to buy AAPL stock.\n", "4  20240405214500  1 Wall Street Analyst Thinks This Magnificent Seven Stock Is Going to $220. Is It a buy Around $169?                                                                       The pundit feels that the company is doing a solid job of boosting its services revenue.\n", "5  20240408193000                              Microchip Technology expands TSMC partnership to strengthen supply chain          Looking for stock market analysis and research with proves results? Zacks.com offers in-depth financial research with over 30years of proven results.\n", "6  20240409090200                                     Apple changes strategy to allow retro game emulators on App Store          Looking for stock market analysis and research with proves results? Zacks.com offers in-depth financial research with over 30years of proven results.\n", "7  20240409150200              Analysts Offer Insights on Technology Companies: AmpliTech Group (AMPG) and Apple (AAPL)          Looking for stock market analysis and research with proves results? Zacks.com offers in-depth financial research with over 30years of proven results.\n", "8  20240409181500                                               1 Unstoppable Growth Stock to Buy With $1,000 Right Now                                                                                                                          This tech giant has more room to run.\n", "9  20240410174500                                     Technology’s profits are “skewed to the downside” - Goldman Sachs          Looking for stock market analysis and research with proves results? Zacks.com offers in-depth financial research with over 30years of proven results.\n", "\u001b[32m**********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mUser_Proxy\u001b[0m (to Market_Analyst):\n", "\n", "\u001b[32m***** Response from calling tool (call_mQTuLkVVcabsqBeOFkSQ83Oe) *****\u001b[0m\n", "{\n", "  \"10DayAverageTradingVolume\": 17.57643,\n", "  \"13WeekPriceReturnDaily\": -9.5818,\n", "  \"26WeekPriceReturnDaily\": -3.3859,\n", "  \"3MonthADReturnStd\": 19.35961,\n", "  \"3MonthAverageTradingVolume\": 18.83497,\n", "  \"52WeekHigh\": 199.615,\n", "  \"52WeekHighDate\": \"2023-12-14\",\n", "  \"52WeekLow\": 159.78,\n", "  \"52WeekLowDate\": \"2023-04-12\",\n", "  \"52WeekPriceReturnDaily\": 4.3408,\n", "  \"5DayPriceReturnDaily\": -0.616,\n", "  \"assetTurnoverAnnual\": 1.0871,\n", "  \"assetTurnoverTTM\": 1.1234,\n", "  \"beta\": 1.1331,\n", "  \"bookValuePerShareAnnual\": 3.9965,\n", "  \"bookValuePerShareQuarterly\": 4.7929,\n", "  \"bookValueShareGrowth5Y\": -6.64,\n", "  \"capexCagr5Y\": -3.82,\n", "  \"cashFlowPerShareAnnual\": 6.4041,\n", "  \"cashFlowPerShareQuarterly\": 6.9125,\n", "  \"cashFlowPerShareTTM\": 6.86253,\n", "  \"cashPerSharePerShareAnnual\": 3.9585,\n", "  \"cashPerSharePerShareQuarterly\": 4.7283,\n", "  \"currentDividendYieldTTM\": 0.5819,\n", "  \"currentEv/freeCashFlowAnnual\": 26.7046,\n", "  \"currentEv/freeCashFlowTTM\": 24.8842,\n", "  \"currentRatioAnnual\": 0.988,\n", "  \"currentRatioQuarterly\": 1.0725,\n", "  \"dividendGrowthRate5Y\": 6.58,\n", "  \"dividendPerShareAnnual\": 0.9542,\n", "  \"dividendPerShareTTM\": 0.9636,\n", "  \"dividendYieldIndicatedAnnual\": 0.56580424,\n", "  \"ebitdPerShareAnnual\": 7.9326,\n", "  \"ebitdPerShareTTM\": 8.2668,\n", "  \"ebitdaCagr5Y\": 9.04,\n", "  \"ebitdaInterimCagr5Y\": 9.94,\n", "  \"enterpriseValue\": 2659354,\n", "  \"epsAnnual\": 6.1339,\n", "  \"epsBasicExclExtraItemsAnnual\": 6.1339,\n", "  \"epsBasicExclExtraItemsTTM\": 6.427099999999999,\n", "  \"epsExclExtraItemsAnnual\": 6.1339,\n", "  \"epsExclExtraItemsTTM\": 6.427099999999999,\n", "  \"epsGrowth3Y\": 23.26,\n", "  \"epsGrowth5Y\": 15.56,\n", "  \"epsGrowthQuarterlyYoy\": 15.82,\n", "  \"epsGrowthTTMYoy\": 9.2,\n", "  \"epsInclExtraItemsAnnual\": 6.1339,\n", "  \"epsInclExtraItemsTTM\": 6.427099999999999,\n", "  \"epsNormalizedAnnual\": 6.1339,\n", "  \"epsTTM\": 6.427099999999999,\n", "  \"focfCagr5Y\": 9.2,\n", "  \"grossMargin5Y\": 41.05,\n", "  \"grossMarginAnnual\": 44.13,\n", "  \"grossMarginTTM\": 45.87,\n", "  \"inventoryTurnoverAnnual\": 37.9777,\n", "  \"inventoryTurnoverTTM\": 31.8108,\n", "  \"longTermDebt/equityAnnual\": 1.5332,\n", "  \"longTermDebt/equityQuarterly\": 1.2832,\n", "  \"marketCapitalization\": 2592074,\n", "  \"monthToDatePriceReturnDaily\": -2.1577,\n", "  \"netIncomeEmployeeAnnual\": 0.6025,\n", "  \"netIncomeEmployeeTTM\": 0.6268,\n", "  \"netInterestCoverageAnnual\": 622.5082,\n", "  \"netInterestCoverageTTM\": 622.5082,\n", "  \"netMarginGrowth5Y\": 2.46,\n", "  \"netProfitMargin5Y\": 23.73,\n", "  \"netProfitMarginAnnual\": 25.31,\n", "  \"netProfitMarginTTM\": 28.36,\n", "  \"operatingMargin5Y\": 27.72,\n", "  \"operatingMarginAnnual\": 29.82,\n", "  \"operatingMarginTTM\": 33.76,\n", "  \"payoutRatioAnnual\": 15.49,\n", "  \"payoutRatioTTM\": 0.1495,\n", "  \"pbAnnual\": 43.4754,\n", "  \"pbQuarterly\": 40.4099,\n", "  \"pcfShareAnnual\": 23.4486,\n", "  \"pcfShareTTM\": 22.2624,\n", "  \"peAnnual\": 26.7238,\n", "  \"peBasicExclExtraTTM\": 25.6862,\n", "  \"peExclExtraAnnual\": 30.96975,\n", "  \"peExclExtraTTM\": 25.6862,\n", "  \"peInclExtraTTM\": 25.6862,\n", "  \"peNormalizedAnnual\": 26.7238,\n", "  \"peTTM\": 29.6728,\n", "  \"pfcfShareAnnual\": 26.029,\n", "  \"pfcfShareTTM\": 24.2547,\n", "  \"pretaxMargin5Y\": 27.89,\n", "  \"pretaxMarginAnnual\": 29.67,\n", "  \"pretaxMarginTTM\": 33.72,\n", "  \"priceRelativeToS&P50013Week\": -17.9088,\n", "  \"priceRelativeToS&P50026Week\": -24.4522,\n", "  \"priceRelativeToS&P5004Week\": -1.5991,\n", "  \"priceRelativeToS&P50052Week\": -21.14,\n", "  \"priceRelativeToS&P500Ytd\": -21.0203,\n", "  \"psAnnual\": 6.7628,\n", "  \"psTTM\": 7.7634,\n", "  \"ptbvAnnual\": 4.8643,\n", "  \"ptbvQuarterly\": 6.4066,\n", "  \"quickRatioAnnual\": 0.9444,\n", "  \"quickRatioQuarterly\": 1.0239,\n", "  \"receivablesTurnoverAnnual\": 13.2873,\n", "  \"receivablesTurnoverTTM\": 16.4319,\n", "  \"revenueEmployeeAnnual\": 2.3807,\n", "  \"revenueEmployeeTTM\": 2.3957,\n", "  \"revenueGrowth3Y\": 11.77,\n", "  \"revenueGrowth5Y\": 7.61,\n", "  \"revenueGrowthQuarterlyYoy\": 2.07,\n", "  \"revenueGrowthTTMYoy\": -0.47,\n", "  \"revenuePerShareAnnual\": 24.2386,\n", "  \"revenuePerShareTTM\": 24.7618,\n", "  \"revenueShareGrowth5Y\": 12.79,\n", "  \"roa5Y\": 23.36,\n", "  \"roaRfy\": 27.51,\n", "  \"roaTTM\": 0.2939,\n", "  \"roe5Y\": 130.41,\n", "  \"roeRfy\": 156.07999999999998,\n", "  \"roeTTM\": 1.5604,\n", "  \"roi5Y\": 44.8,\n", "  \"roiAnnual\": 55.66,\n", "  \"roiTTM\": 57.940000000000005,\n", "  \"tangibleBookValuePerShareAnnual\": 5.8582,\n", "  \"tangibleBookValuePerShareQuarterly\": 6.7915,\n", "  \"tbvCagr5Y\": 11.34,\n", "  \"totalDebt/totalEquityAnnual\": 1.804,\n", "  \"totalDebt/totalEquityQuarterly\": 1.458,\n", "  \"yearToDatePriceReturnDaily\": -12.8551,\n", "  \"bookValue\": 74100,\n", "  \"cashRatio\": 0.30424040664910096,\n", "  \"currentRatio\": 1.0725,\n", "  \"ebitPerShare\": 2.5919,\n", "  \"eps\": 2.1774,\n", "  \"ev\": 3061651.2,\n", "  \"fcfMargin\": 0.3136,\n", "  \"fcfPerShareTTM\": 6.9125,\n", "  \"grossMargin\": 0.4587,\n", "  \"longtermDebtTotalAsset\": 0.269,\n", "  \"longtermDebtTotalCapital\": 0.5221,\n", "  \"longtermDebtTotalEquity\": 1.2832,\n", "  \"netDebtToTotalCapital\": 0.3694,\n", "  \"netDebtToTotalEquity\": 0.908,\n", "  \"netMargin\": 0.2836,\n", "  \"operatingMargin\": 0.3376,\n", "  \"pb\": 40.4099,\n", "  \"pfcfTTM\": 28.0191,\n", "  \"pretaxMargin\": 0.3372,\n", "  \"ptbv\": 6.4066,\n", "  \"quickRatio\": 1.0239,\n", "  \"roicTTM\": 0.5794,\n", "  \"rotcTTM\": 0.6813,\n", "  \"salesPerShare\": 7.6766,\n", "  \"sgaToSale\": 0.5413,\n", "  \"tangibleBookValue\": 138050,\n", "  \"totalDebtToEquity\": 1.458,\n", "  \"totalDebtToTotalAsset\": 0.3056,\n", "  \"totalDebtToTotalCapital\": 0.5932,\n", "  \"totalRatio\": 1.2652\n", "}\n", "\u001b[32m**********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mUser_Proxy\u001b[0m (to Market_Analyst):\n", "\n", "\u001b[32m***** Response from calling tool (call_KMaKxoAo0XalrinyToyj8Tss) *****\u001b[0m\n", "                  Open        High         Low       Close   Adj Close     Volume\n", "Date                                                                             \n", "2024-03-11  172.940002  174.380005  172.050003  172.750000  172.750000   60139500\n", "2024-03-12  173.149994  174.029999  171.009995  173.229996  173.229996   59825400\n", "2024-03-13  172.770004  173.190002  170.759995  171.130005  171.130005   52488700\n", "2024-03-14  172.910004  174.309998  172.050003  173.000000  173.000000   72913500\n", "2024-03-15  171.169998  172.619995  170.289993  172.619995  172.619995  121664700\n", "2024-03-18  175.570007  177.710007  173.520004  173.720001  173.720001   75604200\n", "2024-03-19  174.339996  176.610001  173.029999  176.080002  176.080002   55215200\n", "2024-03-20  175.720001  178.669998  175.089996  178.669998  178.669998   53423100\n", "2024-03-21  177.050003  177.490005  170.839996  171.369995  171.369995  106181300\n", "2024-03-22  171.759995  173.050003  170.059998  172.279999  172.279999   71106600\n", "2024-03-25  170.570007  171.940002  169.449997  170.850006  170.850006   54288300\n", "2024-03-26  170.000000  171.419998  169.580002  169.710007  169.710007   57388400\n", "2024-03-27  170.410004  173.600006  170.110001  173.309998  173.309998   60273300\n", "2024-03-28  171.750000  172.229996  170.509995  171.479996  171.479996   65672700\n", "2024-04-01  171.190002  171.250000  169.479996  170.029999  170.029999   46240500\n", "2024-04-02  169.080002  169.339996  168.229996  168.839996  168.839996   49329500\n", "2024-04-03  168.789993  170.679993  168.580002  169.649994  169.649994   47691700\n", "2024-04-04  170.289993  171.919998  168.820007  168.820007  168.820007   53704400\n", "2024-04-05  169.589996  170.389999  168.949997  169.580002  169.580002   42055200\n", "2024-04-08  169.029999  169.199997  168.240005  168.449997  168.449997   37425500\n", "2024-04-09  168.699997  170.080002  168.350006  169.669998  169.669998   42451200\n", "2024-04-10  168.800003  169.089996  167.110001  167.779999  167.779999   49587200\n", "\u001b[32m**********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[33mMarket_Analyst\u001b[0m (to User_Proxy):\n", "\n", "### Positive Developments for Apple\n", "\n", "1. **Expansion of Services Revenue**: A Wall Street analyst highlighted Apple's effective strategy in boosting its services revenue, indicating a solid performance in diversifying income sources beyond hardware sales. This is a positive sign for Apple's long-term growth prospects, as it shows the company's ability to innovate and expand in high-margin areas.\n", "\n", "2. **Strategic Changes in App Store Policy**: Apple's decision to allow retro game emulators on the App Store represents a strategic shift that could open up new revenue streams and enhance user engagement. This move could attract a niche but passionate segment of gamers, potentially increasing app sales and subscriptions.\n", "\n", "### Potential Concerns for Apple\n", "\n", "1. **Job Cuts**: The news about Apple layoffs in 2024 raises concerns about the company's operational efficiency and future growth prospects. Layoffs could indicate underlying issues such as cost pressures or a need to streamline operations in response to changing market dynamics.\n", "\n", "2. **Antitrust Case**: While the antitrust case against Apple could potentially prove positive for investors, as suggested by some analysts, it also poses a significant risk. Legal challenges can lead to uncertainties, potential financial penalties, and could force changes in Apple's business practices, impacting its profitability and market position.\n", "\n", "### Stock Price Movement Prediction for Next Week\n", "\n", "Given the mixed developments, with positive aspects like revenue diversification and strategic policy changes, against concerns such as layoffs and legal challenges, the prediction for Apple's stock price movement next week is cautiously optimistic. The company's strong brand and ability to innovate could outweigh the negatives in the short term.\n", "\n", "**Prediction**: Apple's stock price might see a slight uptick, potentially **up by 1-2%** over the next week. This prediction is supported by the company's solid job in boosting its services revenue and strategic adjustments, which could instill investor confidence despite the potential concerns.\n", "\n", "### Summary Analysis\n", "\n", "Apple's recent developments present a mixed bag of opportunities and challenges. The company's efforts to diversify its revenue streams and adapt to market demands are commendable and indicative of its robust strategic planning. However, operational challenges such as layoffs and ongoing legal battles could dampen investor sentiment. Considering the company's historical resilience and strategic moves to bolster revenue, a slight positive movement in stock price is anticipated. Nonetheless, investors should keep an eye on how Apple navigates its challenges, as these could influence its financial performance and stock valuation in the longer term.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mUser_Proxy\u001b[0m (to Market_Analyst):\n", "\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mMarket_Analyst\u001b[0m (to User_Proxy):\n", "\n", "TERMINATE\n", "\n", "--------------------------------------------------------------------------------\n"]}], "source": ["# company = \"Tesla\"\n", "company = \"APPLE\"\n", "\n", "with Cache.disk() as cache:\n", "    # start the conversation\n", "    user_proxy.initiate_chat(\n", "        analyst,\n", "        message=f\"Use all the tools provided to retrieve information available for {company} upon {get_current_date()}. Analyze the positive developments and potential concerns of {company} \"\n", "        \"with 2-4 most important factors respectively and keep them concise. Most factors should be inferred from company related news. \"\n", "        f\"Then make a rough prediction (e.g. up/down by 2-3%) of the {company} stock price movement for next week. Provide a summary analysis to support your prediction.\",\n", "        cache=cache,\n", "    )"]}], "metadata": {"kernelspec": {"display_name": "finrobot", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}