{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Multimodal FinRobot with MplFinance plotting"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this demo, we set up an multimodal agent with GPT-4V using the **MultimodalConversableAgent** provided in AutoGen.\n", "\n", "With the vision capability introduced by multimodal agents, we can analyse stock's performance through With the visual capabilities provided by the multimodal agent, we can, like regular investors, display the stock price fluctuations and trading volume of a stock over a certain period using the plotting capabilities provided by mplfinance, all on one image. Then we analyze its subsequent trend."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import autogen\n", "from autogen import AssistantAgent, UserProxyAgent\n", "from autogen.agentchat.contrib.multimodal_conversable_agent import MultimodalConversableAgent\n", "from autogen.cache import Cache\n", "\n", "from finrobot.utils import get_current_date, register_keys_from_json\n", "from finrobot.data_source.finnhub_utils import FinnHubUtils\n", "from finrobot.functional.charting import MplFinanceUtils\n", "\n", "from textwrap import dedent\n", "from matplotlib import pyplot as plt\n", "from PIL import Image"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["config_list_4v = autogen.config_list_from_json(\n", "    \"../OAI_CONFIG_LIST\",\n", "    filter_dict={\n", "        \"model\": [\"gpt-4-1106-vision-preview\"],\n", "    },\n", ")\n", "config_list_gpt4 = autogen.config_list_from_json(\n", "    \"../OAI_CONFIG_LIST\",\n", "    filter_dict={\n", "        \"model\": [\"gpt-4-0125-preview\"],\n", "    },\n", ")\n", "\n", "# Register FINNHUB API keys for later use\n", "register_keys_from_json(\"../config_api_keys\")\n", "\n", "# Intermediate results/charts will be saved in this directory\n", "working_dir = \"../coding\"\n", "os.makedirs(working_dir, exist_ok=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For this task, we need:\n", "- A normal llm agent as data provider: Call charting functions and provide instructions for multimodal agent\n", "- A multimodal agent as market analyst: Extract the necessary information from the chart and analyze the future trend of this stock.\n", "- A user proxy to execute python functions and control the conversations."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["market_analyst = MultimodalConversableAgent(\n", "    name=\"Market_Analyst\",\n", "    max_consecutive_auto_reply=10,\n", "    llm_config={\"config_list\": config_list_4v, \"temperature\": 0},\n", "    system_message=dedent(\"\"\"\n", "        Your are a Market Analyst. Your task is to analyze the financial data and market news.\n", "        Reply \"TERMINATE\" in the end when everything is done.\n", "        \"\"\")\n", ")\n", "data_provider = AssistantAgent(\n", "    name=\"Data_Provider\",\n", "    llm_config={\"config_list\": config_list_gpt4, \"temperature\": 0},\n", "    system_message=dedent(\"\"\"\n", "        You are a Data Provider. Your task is to provide charts and necessary market information.\n", "        Use the functions you have been provided with.\n", "        Reply \"TERMINATE\" in the end when everything is done.\n", "        \"\"\")\n", ")\n", "user_proxy = UserProxyAgent(\n", "    name=\"User_proxy\",\n", "    human_input_mode=\"NEVER\",\n", "    max_consecutive_auto_reply=10,\n", "    is_termination_msg=lambda x: x.get(\"content\", \"\") and x.get(\n", "        \"content\", \"\").endswith(\"TERMINATE\"),\n", "    code_execution_config={\n", "        \"work_dir\": working_dir,\n", "        \"use_docker\": False\n", "    },  # Please set use_docker=True if docker is available to run the generated code. Using docker is safer than running the generated code directly.\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To improve operational efficiency, we equip the data provider with our predefined tools for plotting using the `mplfinance` library and for retrieving recent market news using `Finnhub`. This approach prevents the agent from consuming extra tokens to write the functions they need."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from finrobot.toolkits import register_toolkits\n", "\n", "tools = [\n", "    {\n", "        \"function\": FinnHubUtils.get_company_news,\n", "        \"name\": \"get_company_news\",\n", "        \"description\": \"retrieve market news related to designated company\"\n", "    },\n", "    {\n", "        \"function\": MplFinanceUtils.plot_stock_price_chart,\n", "        \"name\": \"plot_stock_price_chart\",\n", "        \"description\": \"plot stock price chart of designated company\"\n", "    }\n", "]\n", "register_toolkits(tools, data_provider, user_proxy)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To prevent unexpected chat sequence arrangements in group chats, we opt for manual orchestration for this task. After the data provider supplies the data, the user proxy summarizes it and then presents it to the Multimodal market analyst for analysis."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[34m\n", "********************************************************************************\u001b[0m\n", "\u001b[34mStarting a new chat....\u001b[0m\n", "\u001b[34m\n", "********************************************************************************\u001b[0m\n", "\u001b[33mUser_proxy\u001b[0m (to Data_Provider):\n", "\n", "\n", "Gather information available upon 2024-04-11 for Tesla, \n", "including its recent market news and a candlestick chart of the stock \n", "price trend. Save the chart in `../coding/result.jpg`\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mData_Provider\u001b[0m (to User_proxy):\n", "\n", "\u001b[32m***** Suggested tool call (call_HsyrI5FHSQ36qhDM71177C6Q): get_company_news *****\u001b[0m\n", "Arguments: \n", "{\"symbol\": \"TSLA\", \"start_date\": \"2024-03-11\", \"end_date\": \"2024-04-11\"}\n", "\u001b[32m*********************************************************************************\u001b[0m\n", "\u001b[32m***** Suggested tool call (call_0J5Q3HvTxOdYRkySEn2Hccar): plot_stock_price_chart *****\u001b[0m\n", "Arguments: \n", "{\"ticker_symbol\": \"TSLA\", \"start_date\": \"2024-03-11\", \"end_date\": \"2024-04-11\", \"save_path\": \"../coding/result.jpg\", \"type\": \"candle\"}\n", "\u001b[32m***************************************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION get_company_news...\u001b[0m\n", "Finnhub client initialized\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION plot_stock_price_chart...\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[*********************100%%**********************]  1 of 1 completed\n"]}, {"name": "stdout", "output_type": "stream", "text": ["                  Open        High         Low       Close   Adj Close     Volume\n", "Date                                                                             \n", "2024-03-11  175.449997  182.869995  174.800003  177.770004  177.770004   85391500\n", "2024-03-12  177.770004  179.429993  172.410004  177.539993  177.539993   87391700\n", "2024-03-13  173.050003  176.050003  169.149994  169.479996  169.479996  *********\n", "2024-03-14  167.770004  171.169998  160.509995  162.500000  162.500000  *********\n", "2024-03-15  163.160004  165.179993  160.759995  163.570007  163.570007   96971900\n", "2024-03-18  170.020004  174.720001  165.899994  173.800003  173.800003  108214400\n", "2024-03-19  172.360001  172.820007  167.419998  171.320007  171.320007   77271400\n", "2024-03-20  173.000000  176.250000  170.820007  175.660004  175.660004   83846700\n", "2024-03-21  176.389999  178.179993  171.800003  172.820007  172.820007   73178000\n", "2024-03-22  166.690002  171.199997  166.300003  170.830002  170.830002   75454700\n", "2024-03-25  168.759995  175.240005  168.729996  172.630005  172.630005   74228600\n", "2024-03-26  178.580002  184.250000  177.380005  177.669998  177.669998  113186200\n", "2024-03-27  181.410004  181.910004  176.000000  179.830002  179.830002   81804000\n", "2024-03-28  177.449997  179.570007  175.300003  175.789993  175.789993   77654800\n", "2024-04-01  176.169998  176.750000  170.210007  175.220001  175.220001   81562100\n", "2024-04-02  164.750000  167.690002  163.429993  166.630005  166.630005  116650600\n", "2024-04-03  164.020004  168.820007  163.279999  168.380005  168.380005   82950100\n", "2024-04-04  170.070007  177.190002  168.009995  171.110001  171.110001  123162000\n", "2024-04-05  169.080002  170.860001  160.509995  164.899994  164.899994  141250700\n", "2024-04-08  169.339996  174.500000  167.789993  172.979996  172.979996  104423300\n", "2024-04-09  172.910004  179.220001  171.919998  176.880005  176.880005  103232700\n", "2024-04-10  173.039993  174.929993  170.009995  171.759995  171.759995   84300500\n", "\u001b[33mUser_proxy\u001b[0m (to Data_Provider):\n", "\n", "\u001b[33mUser_proxy\u001b[0m (to Data_Provider):\n", "\n", "\u001b[32m***** Response from calling tool (call_HsyrI5FHSQ36qhDM71177C6Q) *****\u001b[0m\n", "             date                                                                                                            headline                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                summary\n", "0  20240408160700                                                             Tesla Is Unveiling a Robotaxi. This Is What It’s Worth.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     Last week was wild for  Tesla  investors.  This week has even more drama in store thanks to <PERSON><PERSON>.  The chief executive of the electric-vehicle company tweeted Friday evening that  Tesla  would unveil a robotaxi on Aug. 8.\n", "1  20240408235953                                                     Tesla: Analyst explains retail investors' devotion to the stock  Shares of Tesla (TSLA) are moving higher as CEO <PERSON><PERSON> recently announced the company will unveil its robotaxi on August 8 of this year. While the company and its leader have faced many hiccups, including stock declines of up to 30% year-to-date, the EV maker remains popular amongst retail investors. Roth MKM Senior Research Analyst <PERSON> joins Yahoo Finance to discuss Tesla's latest robotaxi announcement and how the company stands out amongst the rest of his picks. \"<PERSON><PERSON> is a very, very special stock, number one for the charismatic leader, the fact that <PERSON><PERSON>sk is really a pretty amazing guy, and has executed. He's not executed to his dreams. But he's greatly outstripped... of what was expected of him originally,\" <PERSON> states, adding: \"Overall sustainability and cleantech has been sort of slow to gain traction, but it's seeing really good traction... across the board in different areas now. So retail loves Tesla. Retail does not love sustainability right now. And... Tesla is a special company for that.\" For a more bullish take on <PERSON><PERSON>, check out what ARK Invest Analyst <PERSON><PERSON> has to say. For more expert insight and the latest market action, click here to watch this full episode of Yahoo Finance Live. Editor's note: This article was written by <PERSON>\n", "2  20240409023542                                                       US STOCKS-Wall St bides time as investors await CPI, earnings                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  All three major U.S. stock indexes were last modestly higher, with gains held in check by the highest benchmark U.S. Treasury yields since November in the wake of Friday's blowout employment report.  That report heightened chances that the Federal Reserve could delay implementing its first interest rate cut at its monthly Federal Open Market Committee meetings longer than previously expected.  \"Wall Street is adjusting expectations to reflect the fact that the Fed could be slower to lower interest rates and that now the greatest likelihood is for a rate cut to occur at the July FOMC meeting, rather than June,\" said <PERSON>, chief investment strategist of CFRA Research in New York.\n", "3  20240409140700                                               General Motors' Cruise unit is expected to start robotaxi tests again                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  Looking for stock market analysis and research with proves results? Zacks.com offers in-depth financial research with over 30years of proven results.\n", "4  20240409152100                                         Tesla's vehicle unit growth could be challenged over coming years, says UBS                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  Looking for stock market analysis and research with proves results? Zacks.com offers in-depth financial research with over 30years of proven results.\n", "5  20240409172000                            Slowing North American EV sales triggers downgrade for Sensata and ChargePoint - analyst                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  Looking for stock market analysis and research with proves results? Zacks.com offers in-depth financial research with over 30years of proven results.\n", "6  20240409190200                                        Chinese electric vehicle stocks gain as double-digit volume growth continues                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  Looking for stock market analysis and research with proves results? Zacks.com offers in-depth financial research with over 30years of proven results.\n", "7  20240410104600                                                                         Tesla (TSLA) Receives a Hold from Jefferies                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  Looking for stock market analysis and research with proves results? Zacks.com offers in-depth financial research with over 30years of proven results.\n", "8  20240410130000  Top-End Tesla Cybertruck Fetches Over Double Sticker Price At Auction Amid Resale Market Jitters, Quality Concerns                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  Looking for stock market analysis and research with proves results? Zacks.com offers in-depth financial research with over 30years of proven results.\n", "9  20240410153300                                               Tesla is on watch after analysts warn deliveries could fall this year                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  Looking for stock market analysis and research with proves results? Zacks.com offers in-depth financial research with over 30years of proven results.\n", "\u001b[32m**********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mUser_proxy\u001b[0m (to Data_Provider):\n", "\n", "\u001b[32m***** Response from calling tool (call_0J5Q3HvTxOdYRkySEn2Hccar) *****\u001b[0m\n", "candle chart saved to <img ../coding/result.jpg>\n", "\u001b[32m**********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mData_Provider\u001b[0m (to User_proxy):\n", "\n", "The recent market news for Tesla includes a variety of updates, ranging from the announcement of a robotaxi unveiling to analyst opinions and market movements. Here are some highlights:\n", "\n", "1. **Tesla's Robotaxi Unveiling**: Tesla plans to unveil a robotaxi on August 8, generating significant interest among investors and the public. This announcement has added to the drama surrounding Tesla, with CEO <PERSON><PERSON> at the center of the excitement.\n", "\n", "2. **Retail Investors' Devotion to Tesla**: Despite facing challenges, including stock declines of up to 30% year-to-date, Tesla remains popular among retail investors. Analysts highlight Elon Musk's charisma and the company's execution as key factors driving this devotion.\n", "\n", "3. **Wall Street's Anticipation**: The market is adjusting expectations around the Federal Reserve's interest rate decisions, affecting stock movements. Tesla, along with other stocks, is influenced by these macroeconomic factors.\n", "\n", "4. **Competition and Challenges**: General Motors' Cruise unit is expected to resume robotaxi tests, indicating increasing competition in the autonomous vehicle space. Additionally, analysts from UBS suggest that Tesla's vehicle unit growth could face challenges in the coming years.\n", "\n", "5. **Market Analysis and Downgrades**: Various market analysis reports and downgrades, such as those for Sensata and ChargePoint, reflect broader industry trends that could impact Tesla indirectly.\n", "\n", "6. **Tesla's Stock Rating and Auction News**: Tesla received a \"Hold\" rating from Jefferies, and a top-end Tesla Cybertruck fetched over double its sticker price at auction, highlighting the brand's strong market presence despite concerns.\n", "\n", "7. **Delivery Concerns**: Analysts warn that Tesla's deliveries could fall this year, putting the company on watch.\n", "\n", "The candlestick chart of Tesla's stock price trend has been saved and provides a visual representation of the stock's performance over the specified period. This chart can offer insights into market sentiment and potential future movements based on historical price actions.\n", "\n", "![Tesla Stock Price Candlestick Chart](../coding/result.jpg)\n", "\n", "These updates and the chart together offer a comprehensive view of Tesla's current market position and outlook.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mUser_proxy\u001b[0m (to Data_Provider):\n", "\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mData_Provider\u001b[0m (to User_proxy):\n", "\n", "TERMINATE\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[34m\n", "********************************************************************************\u001b[0m\n", "\u001b[34mStarting a new chat....\u001b[0m\n", "\u001b[34m\n", "********************************************************************************\u001b[0m\n", "\u001b[33mUser_proxy\u001b[0m (to Market_Analyst):\n", "\n", "\n", "With the stock price chart provided, along with recent market news of Tesla, \n", "analyze the recent fluctuations of the stock and the potential relationship with \n", "market news. Provide your predictive analysis for the stock's trend in the coming \n", "week. Reply TERMINATE when the task is done.\n", "\n", "Context: \n", "<image>\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[33mMarket_Analyst\u001b[0m (to User_proxy):\n", "\n", "Based on the provided candlestick chart for Tesla (TSLA), we can observe several patterns and trends over the period displayed. The chart shows a series of alternating bullish (white) and bearish (black) candles, indicating a period of volatility and indecision in the market.\n", "\n", "The candlesticks from around March 11 to March 25 show a mix of bullish and bearish sessions, with the price fluctuating between approximately $165 and $180. The presence of long wicks on some of the candles suggests that there were intraday price reversals, where prices moved significantly away from the open but returned closer to the opening price by the close of the trading session.\n", "\n", "The volume histogram at the bottom of the chart shows that trading volume has been relatively stable, with no significant spikes that might indicate a strong buying or selling pressure.\n", "\n", "To provide a predictive analysis, we would need to consider recent market news related to Tesla. Without specific news items, we can only speculate on general factors that might influence the stock price:\n", "\n", "1. Production and delivery numbers: Tesla's quarterly production and delivery reports can significantly impact its stock price. If recent news suggests that Tesla has met or exceeded production targets, this could lead to a bullish trend.\n", "\n", "2. Regulatory news: Any regulatory changes affecting electric vehicles, such as subsidies or tax incentives, could positively or negatively impact Tesla's stock.\n", "\n", "3. Technological advancements: Announcements about new technology or updates to existing models could drive investor sentiment.\n", "\n", "4. Economic indicators: Broader market trends and economic indicators, such as interest rates or inflation data, can also affect Tesla's stock price.\n", "\n", "Given the observed volatility and the lack of a clear trend in the chart, predicting the stock's direction in the coming week would be speculative. However, if recent news has been positive and the market sentiment is bullish, we might expect the stock to test the upper range of the recent price fluctuations. Conversely, if the news has been negative or if broader market trends are bearish, the stock could retest the lower range around $165.\n", "\n", "Investors should monitor upcoming news and market conditions closely, as these will likely drive Tesla's stock price in the short term. It's also important to consider the overall market environment, as stocks often move in correlation with the broader indices.\n", "\n", "Please note that this analysis is based on historical price patterns and potential market news impacts. Actual future stock performance may vary due to a multitude of unpredictable factors.\n", "\n", "TERMINATE\n", "\n", "--------------------------------------------------------------------------------\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["company = \"Tesla\"\n", "# company = \"APPLE\"\n", "\n", "with Cache.disk() as cache:  # image cannot be cached\n", "    autogen.initiate_chats(\n", "        [\n", "            {\n", "                \"sender\": user_proxy,\n", "                \"recipient\": data_provider,\n", "                \"message\": dedent(f\"\"\"\n", "                Gather information available upon {get_current_date()} for {company}, \n", "                including its recent market news and a candlestick chart of the stock \n", "                price trend. Save the chart in `{working_dir}/result.jpg`\n", "                \"\"\"),           # As currently AutoGen has the bug of not respecting `work_dir` when using function call, we have to specify the directory\n", "                \"clear_history\": True,\n", "                \"silent\": <PERSON><PERSON><PERSON>,\n", "                \"summary_method\": \"last_msg\",\n", "            },\n", "            {\n", "                \"sender\": user_proxy,\n", "                \"recipient\": market_analyst,\n", "                \"message\": dedent(f\"\"\"\n", "                With the stock price chart provided, along with recent market news of {company}, \n", "                analyze the recent fluctuations of the stock and the potential relationship with \n", "                market news. Provide your predictive analysis for the stock's trend in the coming \n", "                week. Reply TERMINATE when the task is done.\n", "                \"\"\"),\n", "                \"max_turns\": 1,  # max number of turns for the conversation\n", "                \"summary_method\": \"last_msg\",\n", "                # cheated here for stability\n", "                \"carryover\": f\"<img {working_dir}/result.jpg>\"\n", "            }\n", "        ]\n", "    )\n", "\n", "img = Image.open(f\"{working_dir}/result.jpg\")\n", "plt.imshow(img)\n", "plt.axis(\"off\")  # Hide the axes\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "finrobot", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}