#!/usr/bin/env python3
"""
Test ensemble and forecasting system functionality.
"""

import logging
import sys
from pathlib import Path
import numpy as np

# Add finrobot to path
sys.path.append(str(Path(__file__).parent))

from finrobot.data.loaders.csv_loader import CSVLoader
from finrobot.ml.features.feature_pipeline import FeaturePipeline
from finrobot.ml.models.model_factory import ModelFactory
from finrobot.ml.ensemble.stacking_ensemble import StackingEnsemble
from finrobot.ml.ensemble.monte_carlo_forecaster import MonteCarloForecaster
from finrobot.ml.ensemble.prediction_intervals import PredictionIntervals
from finrobot.ml.ensemble.confidence_scorer import ConfidenceScorer

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Test ensemble and forecasting system."""
    logger.info("=== Testing Ensemble & Forecasting System ===")
    
    try:
        # Load data
        loader = CSVLoader(".")
        symbols = loader.get_available_symbols()[:1]  # Use first symbol only
        
        if not symbols:
            logger.error("No symbols available")
            return
        
        symbol = symbols[0]
        logger.info(f"Testing ensemble on {symbol}")
        
        # Load and prepare data
        data = loader.load_data(symbol)
        
        # Build features
        feature_pipeline = FeaturePipeline()
        features_data = feature_pipeline.build_features(data, symbol)
        
        # Prepare for ML
        X, y = feature_pipeline.prepare_for_ml(features_data, target_horizon=1)
        
        if len(X) < 100:
            logger.warning(f"Insufficient data for ensemble: {len(X)} samples")
            return
        
        # Split data
        train_size = int(len(X) * 0.6)
        val_size = int(len(X) * 0.2)
        
        X_train = X[:train_size]
        y_train = y[:train_size]
        X_val = X[train_size:train_size+val_size]
        y_val = y[train_size:train_size+val_size]
        X_test = X[train_size+val_size:]
        y_test = y[train_size+val_size:]
        
        logger.info(f"Train: {len(X_train)}, Val: {len(X_val)}, Test: {len(X_test)}")
        
        # Create base models
        base_models = [
            ModelFactory.create_model('lightgbm', n_estimators=50, learning_rate=0.1),
            ModelFactory.create_model('xgboost', n_estimators=50, learning_rate=0.1),
        ]
        
        # Test 1: Stacking Ensemble
        logger.info("=== Testing Stacking Ensemble ===")
        ensemble = StackingEnsemble(base_models)
        ensemble.fit(X_train, y_train, X_val, y_val)
        
        ensemble_pred = ensemble.predict(X_test)
        ensemble_metrics = ensemble.evaluate(X_test, y_test)
        
        logger.info("Ensemble Results:")
        for metric, value in ensemble_metrics.items():
            logger.info(f"  {metric}: {value:.4f}")
        
        model_weights = ensemble.get_model_weights()
        if model_weights is not None:
            logger.info(f"Model weights: {model_weights}")
        
        # Test 2: Monte Carlo Forecasting
        logger.info("=== Testing Monte Carlo Forecasting ===")
        mc_forecaster = MonteCarloForecaster(n_simulations=100)  # Reduced for testing
        mc_forecaster.fit(ensemble, X_train, y_train, X_val, y_val)
        
        forecasts = mc_forecaster.forecast(X_test[:5])  # Forecast first 5 test samples
        
        logger.info("Monte Carlo Forecast Results:")
        logger.info(f"Point forecasts: {forecasts['point_forecast'][:3]}")
        logger.info(f"Mean forecasts: {forecasts['mean_forecast'][:3]}")
        logger.info(f"Forecast std: {forecasts['forecast_std'][:3]}")
        
        for confidence_level, intervals in forecasts['prediction_intervals'].items():
            logger.info(f"{confidence_level} intervals - width: {np.mean(intervals['width']):.4f}")
        
        # Evaluate forecast accuracy
        forecast_metrics = mc_forecaster.evaluate_forecast_accuracy(forecasts, y_test[:5])
        logger.info("Forecast Accuracy:")
        for metric, value in forecast_metrics.items():
            logger.info(f"  {metric}: {value:.4f}")
        
        # Test 3: Prediction Intervals
        logger.info("=== Testing Prediction Intervals ===")
        interval_calculator = PredictionIntervals()
        
        # Get residuals for fitting
        train_pred = ensemble.predict(X_train)
        residuals = y_train - train_pred
        
        interval_calculator.fit(residuals.values)
        intervals = interval_calculator.calculate_intervals(ensemble_pred)
        
        logger.info("Prediction Intervals:")
        for confidence_level, interval_data in intervals.items():
            logger.info(f"{confidence_level} - avg width: {np.mean(interval_data['width']):.4f}")
        
        # Validate coverage
        coverage_stats = interval_calculator.validate_coverage(intervals, y_test.values)
        logger.info("Coverage Statistics:")
        for confidence_level, stats in coverage_stats.items():
            logger.info(f"  {confidence_level}: {stats['empirical_coverage']:.3f} "
                       f"(expected: {stats['expected_coverage']:.3f})")
        
        # Test 4: Confidence Scoring
        logger.info("=== Testing Confidence Scoring ===")
        confidence_scorer = ConfidenceScorer()
        confidence_scorer.fit(y_val.values, ensemble.predict(X_val))
        
        # Get ensemble predictions for confidence scoring
        base_predictions = np.column_stack([model.predict(X_test) for model in base_models])
        
        confidence_scores = confidence_scorer.score_predictions(
            ensemble_pred,
            prediction_intervals=intervals,
            ensemble_predictions=base_predictions
        )
        
        confidence_summary = confidence_scorer.get_confidence_summary(confidence_scores, ensemble_pred)
        logger.info("Confidence Summary:")
        for key, value in confidence_summary.items():
            if isinstance(value, dict):
                logger.info(f"  {key}: {value}")
            else:
                logger.info(f"  {key}: {value:.4f}")
        
        logger.info("✅ Ensemble & Forecasting system test completed successfully!")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        raise

if __name__ == "__main__":
    main()
