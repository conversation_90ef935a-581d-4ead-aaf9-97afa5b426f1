#!/usr/bin/env python3
"""
Quick test script for FinRobot-Pro functionality.
Tests data loading, feature engineering, and basic model training.
"""

import logging
import sys
from pathlib import Path

# Add finrobot to path
sys.path.append(str(Path(__file__).parent))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_data_loading():
    """Test data loading functionality."""
    logger.info("Testing data loading...")
    
    from finrobot.data.loaders.csv_loader import CSVLoader
    
    # Initialize CSV loader
    loader = CSVLoader(".")
    
    # Get available symbols
    symbols = loader.get_available_symbols()
    logger.info(f"Found {len(symbols)} symbols: {symbols[:5]}...")
    
    if not symbols:
        logger.error("No CSV files found!")
        return False
    
    # Test loading data for first symbol
    test_symbol = symbols[0]
    logger.info(f"Loading data for {test_symbol}")
    
    data = loader.get_ohlcv(test_symbol)
    logger.info(f"Loaded {len(data)} records with columns: {list(data.columns)}")
    
    # Show basic info
    logger.info(f"Date range: {data.index.min()} to {data.index.max()}")
    logger.info(f"Price range: ${data['Close'].min():.2f} - ${data['Close'].max():.2f}")
    
    return True

def test_feature_engineering():
    """Test feature engineering."""
    logger.info("Testing feature engineering...")
    
    from finrobot.data.loaders.csv_loader import CSVLoader
    from finrobot.ml.features.technical_features import TechnicalFeatureEngineer
    
    # Load data
    loader = CSVLoader(".")
    symbols = loader.get_available_symbols()
    
    if not symbols:
        logger.error("No symbols available for testing")
        return False
    
    test_symbol = symbols[0]
    data = loader.get_ohlcv(test_symbol)
    
    # Build features
    feature_engineer = TechnicalFeatureEngineer()
    features = feature_engineer.build_features(data)
    
    logger.info(f"Generated {len(features.columns)} features")
    logger.info(f"Feature columns: {list(features.columns)[:10]}...")
    
    # Check for NaN values
    nan_counts = features.isnull().sum()
    logger.info(f"Features with NaN values: {(nan_counts > 0).sum()}")
    
    return True

def test_model_training():
    """Test basic model training."""
    logger.info("Testing model training...")
    
    from finrobot.data.loaders.csv_loader import CSVLoader
    from finrobot.ml.features.feature_pipeline import FeaturePipeline
    from finrobot.ml.models.model_factory import ModelFactory
    
    # Load and prepare data
    loader = CSVLoader(".")
    symbols = loader.get_available_symbols()
    
    if not symbols:
        logger.error("No symbols available for testing")
        return False
    
    test_symbol = symbols[0]
    data = loader.get_ohlcv(test_symbol)
    
    # Build features
    pipeline = FeaturePipeline()
    features = pipeline.build_features(data, test_symbol)
    
    # Prepare for ML
    X, y = pipeline.prepare_features_for_ml(features, target_column='target_1d')
    
    if len(X) == 0:
        logger.error("No valid training data")
        return False
    
    logger.info(f"Training data shape: X={X.shape}, y={y.shape}")
    
    # Split data
    split_idx = int(len(X) * 0.8)
    X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
    y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]
    
    # Test LightGBM model
    logger.info("Training LightGBM model...")
    model = ModelFactory.create_model('lightgbm', n_estimators=100)
    model.fit(X_train, y_train)
    
    # Make predictions
    result = model.predict(X_test)
    predictions = result.predictions
    
    # Calculate simple metrics
    from sklearn.metrics import mean_squared_error
    import numpy as np
    
    rmse = np.sqrt(mean_squared_error(y_test, predictions))
    directional_accuracy = np.mean(np.sign(y_test) == np.sign(predictions))
    
    logger.info(f"Test RMSE: {rmse:.4f}")
    logger.info(f"Directional Accuracy: {directional_accuracy:.3f}")
    
    return True

def test_data_analysis():
    """Test data analysis functionality."""
    logger.info("Testing data analysis...")
    
    from finrobot.analysis.data_analyzer import DataAnalyzer
    
    # Initialize analyzer
    analyzer = DataAnalyzer(".")
    
    # Get available symbols
    symbols = analyzer.csv_loader.get_available_symbols()
    
    if not symbols:
        logger.error("No symbols available for analysis")
        return False
    
    # Analyze first symbol only for quick test
    test_symbol = symbols[0]
    logger.info(f"Analyzing {test_symbol}")
    
    # Load and clean data
    raw_data = analyzer.csv_loader.get_ohlcv(test_symbol)
    cleaned_data = analyzer.processor.clean_ohlcv_data(raw_data, test_symbol)
    
    # Validate data
    validation_result = analyzer.validator.validate_ohlcv_data(cleaned_data, test_symbol)
    
    logger.info(f"Validation result: {'PASSED' if validation_result.is_valid else 'FAILED'}")
    if validation_result.errors:
        logger.warning(f"Validation errors: {validation_result.errors}")
    
    # Perform analysis
    analysis = analyzer._analyze_single_symbol(cleaned_data, test_symbol)
    
    # Show some results
    if 'basic_stats' in analysis:
        stats = analysis['basic_stats']
        if 'price_stats' in stats:
            price_stats = stats['price_stats']
            logger.info(f"Price stats - Mean: ${price_stats['mean']:.2f}, Std: ${price_stats['std']:.2f}")
    
    if 'returns' in analysis:
        returns = analysis['returns']
        if 'annualized_metrics' in returns:
            ann_metrics = returns['annualized_metrics']
            logger.info(f"Annualized return: {ann_metrics['return']:.2%}, Volatility: {ann_metrics['volatility']:.2%}")
    
    return True

def main():
    """Run all tests."""
    logger.info("=== FinRobot-Pro Quick Test ===")
    
    tests = [
        ("Data Loading", test_data_loading),
        ("Feature Engineering", test_feature_engineering),
        ("Model Training", test_model_training),
        ("Data Analysis", test_data_analysis),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} Test ---")
        try:
            success = test_func()
            results[test_name] = "PASSED" if success else "FAILED"
            logger.info(f"{test_name} test: {results[test_name]}")
        except Exception as e:
            results[test_name] = f"ERROR: {str(e)}"
            logger.error(f"{test_name} test failed with error: {e}")
    
    # Summary
    logger.info("\n=== Test Summary ===")
    for test_name, result in results.items():
        logger.info(f"{test_name}: {result}")
    
    passed_tests = sum(1 for r in results.values() if r == "PASSED")
    total_tests = len(results)
    
    logger.info(f"\nOverall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        logger.info("🎉 All tests passed! FinRobot-Pro is ready for training.")
    else:
        logger.warning("⚠️  Some tests failed. Please check the logs above.")

if __name__ == "__main__":
    main()
