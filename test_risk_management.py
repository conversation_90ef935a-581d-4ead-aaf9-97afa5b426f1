#!/usr/bin/env python3
"""
Test risk management system functionality.
"""

import logging
import sys
from pathlib import Path
import numpy as np
import pandas as pd

# Add finrobot to path
sys.path.append(str(Path(__file__).parent))

from finrobot.data.loaders.csv_loader import CSVLoader
from finrobot.risk.var_calculator import VaRCalculator
from finrobot.risk.portfolio_optimizer import PortfolioOptimizer
from finrobot.risk.position_sizer import PositionSizer
from finrobot.risk.risk_metrics import RiskMetrics

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Test risk management system."""
    logger.info("=== Testing Risk Management System ===")
    
    try:
        # Load data for multiple symbols
        loader = CSVLoader(".")
        symbols = loader.get_available_symbols()[:5]  # Use first 5 symbols
        
        if len(symbols) < 2:
            logger.error("Need at least 2 symbols for portfolio optimization")
            return
        
        logger.info(f"Testing risk management on {symbols}")
        
        # Load returns data
        returns_data = {}
        for symbol in symbols:
            data = loader.load_data(symbol)
            returns = data['close'].pct_change().dropna()
            returns_data[symbol] = returns
        
        # Create returns DataFrame
        returns_df = pd.DataFrame(returns_data).dropna()
        
        if len(returns_df) < 50:
            logger.warning(f"Insufficient data for risk analysis: {len(returns_df)} samples")
            return
        
        logger.info(f"Loaded returns data: {returns_df.shape}")
        
        # Test 1: VaR Calculator
        logger.info("=== Testing VaR Calculator ===")
        
        # Use first symbol for VaR testing
        test_returns = returns_df.iloc[:, 0]
        
        var_calculator = VaRCalculator()
        var_calculator.fit(test_returns, method='historical')
        
        var_results = var_calculator.calculate_var(portfolio_value=100000)
        
        logger.info("VaR Results:")
        for confidence_level, var_data in var_results.items():
            logger.info(f"  {confidence_level}: ${var_data['var_dollar']:.2f} ({var_data['var_percent']:.4f})")
        
        # Expected Shortfall
        es_results = var_calculator.calculate_expected_shortfall(portfolio_value=100000)
        
        logger.info("Expected Shortfall Results:")
        for confidence_level, es_data in es_results.items():
            logger.info(f"  {confidence_level}: ${es_data['es_dollar']:.2f} ({es_data['es_percent']:.4f})")
        
        # Test 2: Portfolio Optimizer
        logger.info("=== Testing Portfolio Optimizer ===")
        
        optimizer = PortfolioOptimizer(risk_aversion=2.0, max_weight=0.5)
        optimizer.fit(returns_df)
        
        # Mean-variance optimization
        mv_result = optimizer.optimize_portfolio(objective='mean_variance')
        
        logger.info("Mean-Variance Optimization:")
        logger.info(f"  Expected Return: {mv_result['expected_return']:.4f}")
        logger.info(f"  Volatility: {mv_result['volatility']:.4f}")
        logger.info(f"  Sharpe Ratio: {mv_result['sharpe_ratio']:.4f}")
        logger.info("  Weights:")
        for asset, weight in mv_result['weights'].items():
            logger.info(f"    {asset}: {weight:.3f}")
        
        # Risk parity optimization
        rp_result = optimizer.optimize_portfolio(objective='risk_parity')
        
        logger.info("Risk Parity Optimization:")
        logger.info(f"  Expected Return: {rp_result['expected_return']:.4f}")
        logger.info(f"  Volatility: {rp_result['volatility']:.4f}")
        logger.info(f"  Sharpe Ratio: {rp_result['sharpe_ratio']:.4f}")
        
        # Test 3: Position Sizer
        logger.info("=== Testing Position Sizer ===")
        
        position_sizer = PositionSizer(max_position_size=0.2)
        
        # Create signals DataFrame
        signals_data = []
        for symbol in symbols:
            symbol_returns = returns_df[symbol]
            expected_return = symbol_returns.mean() * 252  # Annualized
            volatility = symbol_returns.std() * np.sqrt(252)  # Annualized
            
            signals_data.append({
                'expected_return': expected_return,
                'volatility': volatility,
                'signal_strength': np.random.uniform(0.5, 1.0)  # Random signal strength
            })
        
        signals_df = pd.DataFrame(signals_data, index=symbols)
        
        # Kelly criterion sizing
        kelly_sizes = position_sizer.calculate_position_sizes(signals_df, method='kelly')
        
        logger.info("Kelly Criterion Position Sizes:")
        for _, row in kelly_sizes.iterrows():
            logger.info(f"  {row['asset']}: {row['position_size']:.3f} (signal: {row['signal_strength']:.2f})")
        
        # Fixed fractional sizing
        ff_sizes = position_sizer.calculate_position_sizes(
            signals_df, 
            method='fixed_fractional',
            risk_per_trade=0.02,
            stop_loss_distance=0.05
        )
        
        logger.info("Fixed Fractional Position Sizes:")
        for _, row in ff_sizes.iterrows():
            logger.info(f"  {row['asset']}: {row['position_size']:.3f}")
        
        # Test 4: Risk Metrics
        logger.info("=== Testing Risk Metrics ===")
        
        risk_metrics = RiskMetrics()
        
        # Use portfolio returns (equal weighted for simplicity)
        portfolio_returns = returns_df.mean(axis=1)
        
        # Comprehensive risk report
        risk_report = risk_metrics.calculate_comprehensive_risk_report(portfolio_returns)
        
        logger.info("Risk Report Summary:")
        
        # Basic stats
        basic_stats = risk_report['basic_stats']
        logger.info(f"  Observations: {basic_stats['n_observations']}")
        logger.info(f"  Mean Return: {basic_stats['mean_return']:.6f}")
        logger.info(f"  Std Return: {basic_stats['std_return']:.6f}")
        
        # Performance metrics
        perf_metrics = risk_report['performance_metrics']
        logger.info(f"  Annualized Return: {perf_metrics['annualized_return']:.4f}")
        logger.info(f"  Volatility: {perf_metrics['volatility']:.4f}")
        logger.info(f"  Sharpe Ratio: {perf_metrics['sharpe_ratio']:.4f}")
        logger.info(f"  Win Rate: {perf_metrics['win_rate']:.3f}")
        
        # Drawdown metrics
        dd_metrics = risk_report['drawdown_metrics']
        logger.info(f"  Max Drawdown: {dd_metrics['max_drawdown']:.4f}")
        logger.info(f"  Avg Drawdown: {dd_metrics['average_drawdown']:.4f}")
        
        # Tail risk metrics
        tail_metrics = risk_report['tail_risk_metrics']
        logger.info(f"  VaR 95%: {tail_metrics['var_95%']:.6f}")
        logger.info(f"  Expected Shortfall 95%: {tail_metrics['expected_shortfall_95%']:.6f}")
        logger.info(f"  Skewness: {tail_metrics['skewness']:.3f}")
        logger.info(f"  Kurtosis: {tail_metrics['kurtosis']:.3f}")
        
        # Risk summary
        risk_summary = risk_metrics.get_risk_summary(risk_report)
        
        logger.info("Risk Assessment:")
        logger.info(f"  Risk Level: {risk_summary['risk_assessment']['risk_level']}")
        logger.info(f"  Risk Score: {risk_summary['risk_assessment']['risk_score']:.1f}/100")
        
        # Test 5: Integration Example
        logger.info("=== Integration Example ===")
        
        # Calculate optimal portfolio
        optimal_weights = mv_result['weights']
        
        # Calculate position sizes based on optimal weights
        position_sizes = {}
        for asset in optimal_weights.index:
            if asset in signals_df.index:
                expected_ret = signals_df.loc[asset, 'expected_return']
                vol = signals_df.loc[asset, 'volatility']
                
                kelly_size = position_sizer.kelly_criterion(expected_ret, vol)
                optimal_weight = optimal_weights[asset]
                
                # Combine optimal weight with Kelly sizing
                final_size = min(optimal_weight, kelly_size)
                position_sizes[asset] = final_size
        
        logger.info("Integrated Position Sizes (Optimal + Kelly):")
        for asset, size in position_sizes.items():
            logger.info(f"  {asset}: {size:.3f}")
        
        # Calculate portfolio VaR with optimal weights
        portfolio_returns_weighted = (returns_df * optimal_weights).sum(axis=1)
        
        var_calc_portfolio = VaRCalculator()
        var_calc_portfolio.fit(portfolio_returns_weighted, method='historical')
        portfolio_var = var_calc_portfolio.calculate_var(portfolio_value=100000)
        
        logger.info("Portfolio VaR (Optimized Weights):")
        for confidence_level, var_data in portfolio_var.items():
            logger.info(f"  {confidence_level}: ${var_data['var_dollar']:.2f}")
        
        logger.info("✅ Risk Management system test completed successfully!")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        raise

if __name__ == "__main__":
    main()
