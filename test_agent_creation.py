#!/usr/bin/env python3
"""
Test script for FinRobot agent creation and basic functionality
"""

import sys
import traceback

def test_agent_creation():
    """Test basic agent creation without API calls"""
    print("=== Testing Agent Creation ===")
    
    try:
        from finrobot.agents.workflow import FinRobot
        print("✅ FinRobot class imported successfully")
        
        # Test agent creation with minimal config
        test_config = {
            "name": "Test_Agent",
            "profile": "A test agent for validation purposes"
        }
        
        # Mock LLM config (won't be used for actual calls)
        mock_llm_config = {
            "config_list": [{"model": "gpt-4", "api_key": "test_key"}],
            "timeout": 120,
            "temperature": 0
        }
        
        agent = FinRobot(
            agent_config=test_config,
            llm_config=mock_llm_config
        )
        
        print(f"✅ Agent created successfully: {agent.name}")
        print(f"   - System message length: {len(agent.system_message) if agent.system_message else 0}")
        print(f"   - Toolkits count: {len(agent.toolkits)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent creation failed: {e}")
        traceback.print_exc()
        return False

def test_predefined_agents():
    """Test predefined agent configurations"""
    print("\n=== Testing Predefined Agents ===")
    
    try:
        from finrobot.agents.agent_library import library
        
        # Test Market_Analyst agent
        market_analyst_config = library.get("Market_Analyst")
        if market_analyst_config:
            print("✅ Market_Analyst configuration found")
            print(f"   - Profile length: {len(market_analyst_config['profile'])}")
            print(f"   - Toolkits: {len(market_analyst_config.get('toolkits', []))}")
        else:
            print("❌ Market_Analyst configuration not found")
            
        # Test Expert_Investor agent
        expert_investor_config = library.get("Expert_Investor")
        if expert_investor_config:
            print("✅ Expert_Investor configuration found")
            print(f"   - Profile length: {len(expert_investor_config['profile'])}")
            print(f"   - Toolkits: {len(expert_investor_config.get('toolkits', []))}")
        else:
            print("❌ Expert_Investor configuration not found")
            
        return True
        
    except Exception as e:
        print(f"❌ Predefined agent test failed: {e}")
        traceback.print_exc()
        return False

def test_data_source_basic():
    """Test basic data source functionality without API keys"""
    print("\n=== Testing Data Sources (Basic) ===")
    
    try:
        from finrobot.data_source import YFinanceUtils
        
        # Test method availability
        methods = [method for method in dir(YFinanceUtils) if not method.startswith('_')]
        print(f"✅ YFinanceUtils has {len(methods)} methods: {methods}")
        
        # Test basic yfinance functionality (should work without API key)
        import yfinance as yf
        ticker = yf.Ticker("AAPL")
        info = ticker.info
        
        if info and 'longName' in info:
            print(f"✅ Basic YFinance test successful: {info['longName']}")
        else:
            print("⚠️ YFinance returned limited data (may be rate limited)")
            
        return True
        
    except Exception as e:
        print(f"❌ Data source test failed: {e}")
        traceback.print_exc()
        return False

def test_functional_modules():
    """Test functional module imports"""
    print("\n=== Testing Functional Modules ===")
    
    try:
        from finrobot.functional import (
            ReportAnalysisUtils, 
            MplFinanceUtils, 
            ReportChartUtils,
            CodingUtils, 
            IPythonUtils,
            BackTraderUtils,
            ReportLabUtils,
            TextUtils
        )
        
        print("✅ All functional modules imported successfully")
        
        # Test TextUtils basic functionality
        test_text = "This is a test string for length checking."
        # Note: We can't actually call the method without proper setup
        print(f"✅ TextUtils available for text processing")
        
        return True
        
    except Exception as e:
        print(f"❌ Functional modules test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("FinRobot Integration Testing")
    print("=" * 50)
    
    tests = [
        test_agent_creation,
        test_predefined_agents,
        test_data_source_basic,
        test_functional_modules
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("SUMMARY:")
    passed = sum(results)
    total = len(results)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("⚠️ Some tests failed - see details above")
        return 1

if __name__ == "__main__":
    sys.exit(main())
