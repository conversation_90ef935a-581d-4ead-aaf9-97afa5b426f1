groups:
  - name: finrobot_alerts
    rules:
      - alert: FinRobotAPIDown
        expr: up{job="finrobot-api"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "FinRobot API is down"
          description: "FinRobot API has been down for more than 1 minute."

      - alert: FinRobotHighErrorRate
        expr: rate(finrobot_http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High error rate in FinRobot API"
          description: "Error rate is {{ $value }} errors per second."

      - alert: FinRobotHighLatency
        expr: histogram_quantile(0.95, rate(finrobot_http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High latency in FinRobot API"
          description: "95th percentile latency is {{ $value }} seconds."

      - alert: FinRobotHighMemoryUsage
        expr: (container_memory_usage_bytes{name="finrobot-api"} / container_spec_memory_limit_bytes{name="finrobot-api"}) > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage in FinRobot API"
          description: "Memory usage is {{ $value | humanizePercentage }}."

      - alert: FinRobotHighCPUUsage
        expr: rate(container_cpu_usage_seconds_total{name="finrobot-api"}[5m]) > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage in FinRobot API"
          description: "CPU usage is {{ $value | humanizePercentage }}."

      - alert: PostgreSQLDown
        expr: up{job="postgres"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "PostgreSQL is down"
          description: "PostgreSQL database has been down for more than 1 minute."

      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis is down"
          description: "Redis cache has been down for more than 1 minute."

      - alert: FinRobotModelTrainingFailed
        expr: increase(finrobot_model_training_failures_total[1h]) > 3
        for: 0m
        labels:
          severity: warning
        annotations:
          summary: "Multiple model training failures"
          description: "{{ $value }} model training failures in the last hour."

      - alert: FinRobotDataIngestionStalled
        expr: increase(finrobot_data_points_ingested_total[10m]) == 0
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Data ingestion has stalled"
          description: "No new data points ingested in the last 10 minutes."

      - alert: FinRobotPredictionAccuracyLow
        expr: finrobot_model_accuracy < 0.5
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "Model prediction accuracy is low"
          description: "Model accuracy is {{ $value | humanizePercentage }}."

  - name: infrastructure_alerts
    rules:
      - alert: DiskSpaceHigh
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) < 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Disk space is running low"
          description: "Disk space usage is above 90% on {{ $labels.instance }}."

      - alert: HighNetworkTraffic
        expr: rate(node_network_receive_bytes_total[5m]) > 100000000  # 100MB/s
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High network traffic"
          description: "Network traffic is {{ $value | humanize }}B/s on {{ $labels.instance }}."
