#!/usr/bin/env python3
"""
Focused training script for FinRobot-Pro with relaxed criteria.
"""

import logging
import sys
from pathlib import Path

# Add finrobot to path
sys.path.append(str(Path(__file__).parent))

from finrobot.analysis.data_analyzer import DataAnalyzer
from finrobot.analysis.model_trainer import ModelTrainer

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Run focused training with relaxed criteria."""
    logger.info("=== FinRobot-Pro Focused Training ===")
    
    # Model configurations (simplified for demo)
    model_configs = [
        {
            'name': 'lightgbm',
            'n_estimators': 100,  # Reduced for faster training
            'learning_rate': 0.1,
            'num_leaves': 31
        },
        {
            'name': 'xgboost', 
            'n_estimators': 100,
            'learning_rate': 0.1,
            'max_depth': 6
        }
    ]
    
    # Prediction horizons
    target_horizons = [1, 5]  # Reduced for demo
    
    try:
        # Step 1: Get symbols with relaxed criteria
        analyzer = DataAnalyzer(".")
        
        # Use relaxed criteria
        suitable_symbols = analyzer.get_symbols_for_training(
            min_quality_score=0.6,  # Lowered from 0.7
            min_records=200         # Lowered from 1000
        )
        
        if not suitable_symbols:
            # If still no symbols, just pick the best ones manually
            logger.info("No symbols meet criteria, selecting best available...")
            suitable_symbols = ['ETSY', 'PCSA', 'ALGOUSD', 'XRPUSD', 'SOLUSD']
        
        logger.info(f"Training on symbols: {suitable_symbols[:3]}")  # Limit to 3 for demo
        
        # Step 2: Train models
        trainer = ModelTrainer(".")
        
        training_results = trainer.train_ensemble_models(
            symbols=suitable_symbols[:3],  # Train on first 3 symbols
            model_configs=model_configs,
            target_horizons=target_horizons
        )
        
        # Step 3: Show results
        logger.info("=== TRAINING RESULTS ===")
        
        for symbol, symbol_results in training_results.items():
            if symbol.startswith('_'):
                continue
                
            logger.info(f"\n{symbol} Results:")
            for horizon, horizon_results in symbol_results.items():
                logger.info(f"  {horizon} horizon:")
                for model_name, model_results in horizon_results.items():
                    if 'error' in model_results:
                        logger.info(f"    {model_name}: ERROR - {model_results['error']}")
                        continue
                    
                    test_metrics = model_results.get('metrics', {}).get('test', {})
                    if test_metrics:
                        da = test_metrics.get('directional_accuracy', 0)
                        rmse = test_metrics.get('rmse', 0)
                        logger.info(f"    {model_name}: DA={da:.3f}, RMSE={rmse:.4f}")
        
        # Show ensemble summary if available
        if '_ensemble_summary' in training_results:
            summary = training_results['_ensemble_summary']
            logger.info(f"\nEnsemble Summary:")
            logger.info(f"  Successful symbols: {summary['successful_symbols']}")
            logger.info(f"  Failed symbols: {summary['failed_symbols']}")
            
            if 'best_models' in summary:
                logger.info(f"  Best models by horizon:")
                for horizon, best_info in summary['best_models'].items():
                    model_name = best_info.get('model', 'N/A')
                    accuracy = best_info.get('directional_accuracy', 0)
                    logger.info(f"    {horizon}: {model_name} (DA: {accuracy:.3f})")
        
        logger.info("\n=== Training Completed Successfully ===")
        
    except Exception as e:
        logger.error(f"Training failed: {e}")
        raise

if __name__ == "__main__":
    main()
