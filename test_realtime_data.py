#!/usr/bin/env python3
"""
Test real-time data integration functionality.
"""

import asyncio
import logging
import sys
from pathlib import Path
from datetime import datetime, timedelta
import numpy as np

# Add finrobot to path
sys.path.append(str(Path(__file__).parent))

from finrobot.data.streaming.data_buffer import DataBuffer
from finrobot.data.streaming.stream_processor import StreamProcessor
from finrobot.data.streaming.market_data_feed import MarketDataFeed

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_data_buffer():
    """Test data buffer functionality."""
    logger.info("=== Testing Data Buffer ===")
    
    buffer = DataBuffer(max_size=1000, feature_window=50, update_frequency=5)
    
    # Add feature calculator
    def calculate_features(df):
        if 'price' not in df.columns or len(df) < 5:
            return {}
        
        prices = df['price']
        return {
            'sma_5': prices.tail(5).mean(),
            'volatility': prices.pct_change().std(),
            'price_trend': 1 if prices.iloc[-1] > prices.iloc[-5] else -1
        }
    
    buffer.add_feature_calculator(calculate_features)
    
    # Simulate real-time data
    symbol = "TEST_SYMBOL"
    base_price = 100.0
    
    for i in range(20):
        # Simulate price movement
        price_change = np.random.normal(0, 0.01)
        price = base_price * (1 + price_change)
        volume = np.random.uniform(1000, 5000)
        
        timestamp = datetime.now() + timedelta(seconds=i)
        
        buffer.add_trade_data(
            symbol=symbol,
            timestamp=timestamp,
            price=price,
            volume=volume
        )
        
        base_price = price
        
        # Check features every few iterations
        if i % 5 == 0 and i > 0:
            features = buffer.get_latest_features(symbol)
            logger.info(f"Iteration {i} - Features: {len(features)} calculated")
            if features:
                logger.info(f"  SMA_5: {features.get('sma_5', 0):.2f}")
                logger.info(f"  Volatility: {features.get('volatility', 0):.4f}")
    
    # Get recent data
    recent_data = buffer.get_recent_data(symbol, n_points=10)
    logger.info(f"Recent data shape: {recent_data.shape}")
    
    # Get buffer status
    status = buffer.get_buffer_status()
    logger.info(f"Buffer status: {status}")
    
    logger.info("✅ Data Buffer test completed")

async def test_stream_processor():
    """Test stream processor functionality."""
    logger.info("=== Testing Stream Processor ===")
    
    processor = StreamProcessor(prediction_interval=5, min_data_points=10)
    
    # Mock model class
    class MockModel:
        def predict(self, X):
            return np.random.normal(0, 0.01, size=1)
        
        def predict_proba(self, X):
            return np.array([[0.3, 0.7]])
    
    # Add mock model
    mock_model = MockModel()
    processor.add_model("TEST_SYMBOL", mock_model)
    
    # Add callbacks
    predictions_received = []
    
    async def prediction_callback(prediction_result):
        predictions_received.append(prediction_result)
        logger.info(f"Received prediction: {prediction_result['prediction']:.4f} "
                   f"(confidence: {prediction_result['confidence']:.2f})")
    
    processor.add_prediction_callback(prediction_callback)
    
    # Start processor (run for a short time)
    processor_task = asyncio.create_task(processor.start_processing())
    
    # Simulate some data processing
    for i in range(5):
        await processor.process_data_point(
            "TEST_SYMBOL",
            {
                'price': 100 + np.random.normal(0, 1),
                'volume': 1000 + np.random.uniform(-100, 100),
                'timestamp': datetime.now()
            }
        )
        await asyncio.sleep(1)
    
    # Stop processor
    await processor.stop_processing()
    processor_task.cancel()
    
    # Check results
    stats = processor.get_processing_stats()
    logger.info(f"Processing stats: {stats}")
    logger.info(f"Predictions received: {len(predictions_received)}")
    
    logger.info("✅ Stream Processor test completed")

async def test_market_data_feed():
    """Test market data feed functionality."""
    logger.info("=== Testing Market Data Feed ===")
    
    # Mock provider configuration (no real API keys)
    providers = {
        'mock_provider': {
            'api_key': 'mock_key',
            'url': 'wss://mock.example.com/ws'
        }
    }
    
    feed = MarketDataFeed(providers, buffer_size=1000, enable_processing=False)
    
    # Initialize (will fail to connect but that's expected)
    await feed.initialize()
    
    # Test data callbacks
    data_received = []
    
    async def data_callback(data_type, data):
        data_received.append((data_type, data))
        logger.info(f"Received {data_type} data: {data.get('symbol', 'unknown')}")
    
    feed.add_data_callback(data_callback)
    
    # Simulate some data processing directly
    test_trade_message = {
        'symbol': 'AAPL',
        'timestamp': int(datetime.now().timestamp() * 1000),
        'price': 150.25,
        'volume': 1000,
        'provider': 'mock_provider'
    }
    
    test_quote_message = {
        'symbol': 'AAPL',
        'timestamp': int(datetime.now().timestamp() * 1000),
        'bid_price': 150.20,
        'ask_price': 150.30,
        'provider': 'mock_provider'
    }
    
    # Test message handling
    await feed._handle_trade_message(test_trade_message)
    await feed._handle_quote_message(test_quote_message)
    
    # Check buffer
    buffer_status = feed.data_buffer.get_buffer_status()
    logger.info(f"Buffer status after messages: {buffer_status}")
    
    # Get recent data
    recent_data = feed.data_buffer.get_recent_data('AAPL')
    logger.info(f"Recent data for AAPL: {len(recent_data)} points")
    
    # Get feed status
    status = feed.get_status()
    logger.info(f"Feed status: {status}")
    
    logger.info("✅ Market Data Feed test completed")

async def test_integration():
    """Test integration of all components."""
    logger.info("=== Testing Integration ===")
    
    # Create integrated system
    providers = {
        'mock_provider': {
            'api_key': 'mock_key',
            'url': 'wss://mock.example.com/ws'
        }
    }
    
    feed = MarketDataFeed(providers, buffer_size=1000, enable_processing=True)
    await feed.initialize()
    
    # Add a mock model to the stream processor
    class MockModel:
        def predict(self, X):
            # Simple trend following model
            if len(X[0]) > 0:
                return np.array([X[0][0] * 0.1])  # Predict 10% of first feature
            return np.array([0.0])
    
    if feed.stream_processor:
        mock_model = MockModel()
        feed.stream_processor.add_model("AAPL", mock_model)
        
        # Add prediction callback
        predictions = []
        
        async def prediction_callback(prediction_result):
            predictions.append(prediction_result)
            logger.info(f"Integration prediction: {prediction_result['prediction']:.4f}")
        
        feed.stream_processor.add_prediction_callback(prediction_callback)
    
    # Simulate market data stream
    symbols = ['AAPL', 'GOOGL', 'MSFT']
    base_prices = {'AAPL': 150.0, 'GOOGL': 2800.0, 'MSFT': 300.0}
    
    for i in range(10):
        for symbol in symbols:
            # Simulate price movement
            price_change = np.random.normal(0, 0.01)
            new_price = base_prices[symbol] * (1 + price_change)
            base_prices[symbol] = new_price
            
            # Create trade message
            trade_message = {
                'symbol': symbol,
                'timestamp': int(datetime.now().timestamp() * 1000),
                'price': new_price,
                'volume': np.random.uniform(500, 2000),
                'provider': 'mock_provider'
            }
            
            # Create quote message
            spread = new_price * 0.001  # 0.1% spread
            quote_message = {
                'symbol': symbol,
                'timestamp': int(datetime.now().timestamp() * 1000),
                'bid_price': new_price - spread/2,
                'ask_price': new_price + spread/2,
                'provider': 'mock_provider'
            }
            
            # Process messages
            await feed._handle_trade_message(trade_message)
            await feed._handle_quote_message(quote_message)
        
        await asyncio.sleep(0.1)  # Small delay
    
    # Wait a bit for processing
    await asyncio.sleep(2)
    
    # Check results
    final_status = feed.get_status()
    logger.info(f"Final integration status: {final_status}")
    
    if feed.stream_processor:
        processor_stats = feed.stream_processor.get_processing_stats()
        logger.info(f"Processor stats: {processor_stats}")
    
    logger.info("✅ Integration test completed")

async def main():
    """Run all real-time data tests."""
    logger.info("=== Testing Real-time Data Integration ===")
    
    try:
        # Run individual component tests
        await test_data_buffer()
        await asyncio.sleep(1)
        
        await test_stream_processor()
        await asyncio.sleep(1)
        
        await test_market_data_feed()
        await asyncio.sleep(1)
        
        await test_integration()
        
        logger.info("✅ All real-time data integration tests completed successfully!")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
