#!/usr/bin/env python3
"""
Comprehensive model training script for FinRobot-Pro.

This script performs:
1. Data loading and analysis
2. Feature engineering
3. Model training with multiple algorithms
4. Walk-forward validation
5. Performance evaluation and comparison
6. Results saving and reporting
"""

import logging
import sys
from pathlib import Path
from typing import Dict, List

import pandas as pd
import numpy as np

# Add finrobot to path
sys.path.append(str(Path(__file__).parent))

from finrobot.analysis.data_analyzer import DataAnalyzer
from finrobot.analysis.model_trainer import ModelTrainer
from finrobot.config import get_config

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('training.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


def main():
    """Main training pipeline."""
    logger.info("=== FinRobot-Pro Model Training Pipeline ===")
    
    # Configuration
    config = get_config()
    
    # Model configurations to test
    model_configs = [
        {
            'name': 'lightgbm',
            'objective': 'regression',
            'metric': 'rmse',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.9,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'n_estimators': 1000,
            'early_stopping_rounds': 50,
            'reg_alpha': 0.1,
            'reg_lambda': 0.1
        },
        {
            'name': 'xgboost',
            'objective': 'reg:squarederror',
            'eval_metric': 'rmse',
            'max_depth': 6,
            'learning_rate': 0.05,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'n_estimators': 1000,
            'early_stopping_rounds': 50,
            'reg_alpha': 0.1,
            'reg_lambda': 0.1
        },
        {
            'name': 'lstm',
            'hidden_size': 64,
            'num_layers': 2,
            'dropout': 0.2,
            'sequence_length': 60,
            'batch_size': 32,
            'learning_rate': 0.001,
            'epochs': 100,
            'patience': 10
        }
    ]
    
    # Prediction horizons to test
    target_horizons = [1, 5, 10, 20]
    
    try:
        # Step 1: Data Analysis
        logger.info("Step 1: Analyzing data...")
        analyzer = DataAnalyzer(".")
        analysis_results = analyzer.analyze_all_data(save_results=True)
        
        # Get symbols suitable for training
        suitable_symbols = analyzer.get_symbols_for_training(
            min_quality_score=0.6,
            min_records=500
        )
        
        if not suitable_symbols:
            logger.error("No suitable symbols found for training!")
            return
        
        logger.info(f"Found {len(suitable_symbols)} suitable symbols: {suitable_symbols}")
        
        # Step 2: Model Training
        logger.info("Step 2: Training models...")
        trainer = ModelTrainer(".")
        
        # Train on a subset of symbols for demonstration
        # In production, you might want to train on all suitable symbols
        training_symbols = suitable_symbols[:5]  # Limit to first 5 for demo
        
        logger.info(f"Training on symbols: {training_symbols}")
        
        training_results = trainer.train_ensemble_models(
            symbols=training_symbols,
            model_configs=model_configs,
            target_horizons=target_horizons
        )
        
        # Step 3: Save Results
        logger.info("Step 3: Saving results...")
        results_path = trainer.save_training_results()
        logger.info(f"Training results saved to: {results_path}")
        
        # Step 4: Generate Summary Report
        logger.info("Step 4: Generating summary report...")
        generate_summary_report(training_results, analysis_results)
        
        logger.info("=== Training Pipeline Completed Successfully ===")
        
    except Exception as e:
        logger.error(f"Training pipeline failed: {e}")
        raise


def generate_summary_report(training_results: Dict, analysis_results: Dict):
    """Generate a comprehensive summary report.
    
    Args:
        training_results: Results from model training
        analysis_results: Results from data analysis
    """
    logger.info("Generating comprehensive summary report...")
    
    report_lines = []
    report_lines.append("=" * 80)
    report_lines.append("FINROBOT-PRO TRAINING SUMMARY REPORT")
    report_lines.append("=" * 80)
    report_lines.append("")
    
    # Data Analysis Summary
    report_lines.append("DATA ANALYSIS SUMMARY")
    report_lines.append("-" * 40)
    
    total_symbols = len([k for k in analysis_results.keys() if not k.startswith('_')])
    valid_symbols = sum(1 for r in analysis_results.values() if 'error' not in r)
    
    report_lines.append(f"Total symbols analyzed: {total_symbols}")
    report_lines.append(f"Successfully analyzed: {valid_symbols}")
    report_lines.append(f"Failed analysis: {total_symbols - valid_symbols}")
    
    if valid_symbols > 0:
        quality_scores = [r.get('quality_score', 0) for r in analysis_results.values() if 'quality_score' in r]
        if quality_scores:
            avg_quality = np.mean(quality_scores)
            report_lines.append(f"Average data quality score: {avg_quality:.3f}")
    
    report_lines.append("")
    
    # Training Results Summary
    if '_ensemble_summary' in training_results:
        ensemble_summary = training_results['_ensemble_summary']
        
        report_lines.append("MODEL TRAINING SUMMARY")
        report_lines.append("-" * 40)
        report_lines.append(f"Symbols trained: {ensemble_summary['successful_symbols']}")
        report_lines.append(f"Failed training: {ensemble_summary['failed_symbols']}")
        
        # Best models by horizon
        if 'best_models' in ensemble_summary:
            report_lines.append("")
            report_lines.append("BEST MODELS BY HORIZON:")
            for horizon, best_info in ensemble_summary['best_models'].items():
                model_name = best_info.get('model', 'N/A')
                accuracy = best_info.get('directional_accuracy', 0)
                report_lines.append(f"  {horizon}: {model_name} (DA: {accuracy:.3f})")
        
        # Model Performance Summary
        if 'model_performance' in ensemble_summary:
            report_lines.append("")
            report_lines.append("AVERAGE MODEL PERFORMANCE:")
            
            for horizon, models in ensemble_summary['model_performance'].items():
                report_lines.append(f"\n{horizon} Horizon:")
                for model_name, metrics in models.items():
                    da = metrics.get('avg_directional_accuracy', 0)
                    rmse = metrics.get('avg_rmse', 0)
                    report_lines.append(f"  {model_name:12} - DA: {da:.3f}, RMSE: {rmse:.4f}")
        
        # Overfitting Analysis
        if 'overfitting_analysis' in ensemble_summary:
            overfitting = ensemble_summary['overfitting_analysis']
            report_lines.append("")
            report_lines.append("OVERFITTING ANALYSIS:")
            report_lines.append(f"  Total overfitted combinations: {overfitting['total_overfitted']}")
            report_lines.append(f"  Overfitting rate: {overfitting['overfitting_rate']:.2%}")
    
    # Individual Symbol Performance
    report_lines.append("")
    report_lines.append("INDIVIDUAL SYMBOL PERFORMANCE:")
    report_lines.append("-" * 40)
    
    for symbol, symbol_results in training_results.items():
        if symbol.startswith('_') or 'error' in symbol_results:
            continue
        
        report_lines.append(f"\n{symbol}:")
        
        for horizon, horizon_results in symbol_results.items():
            best_model = None
            best_da = 0
            
            for model_name, model_results in horizon_results.items():
                if 'error' in model_results:
                    continue
                
                test_metrics = model_results.get('metrics', {}).get('test', {})
                da = test_metrics.get('directional_accuracy', 0)
                
                if da > best_da:
                    best_da = da
                    best_model = model_name
            
            if best_model:
                report_lines.append(f"  {horizon}: {best_model} (DA: {best_da:.3f})")
    
    # Recommendations
    report_lines.append("")
    report_lines.append("RECOMMENDATIONS:")
    report_lines.append("-" * 40)
    
    if '_ensemble_summary' in training_results:
        ensemble_summary = training_results['_ensemble_summary']
        
        # Overall best performing model
        best_overall = None
        best_overall_score = 0
        
        for horizon, models in ensemble_summary.get('model_performance', {}).items():
            for model_name, metrics in models.items():
                da = metrics.get('avg_directional_accuracy', 0)
                if da > best_overall_score:
                    best_overall_score = da
                    best_overall = model_name
        
        if best_overall:
            report_lines.append(f"• Best overall model: {best_overall} (avg DA: {best_overall_score:.3f})")
        
        # Overfitting warnings
        overfitting_rate = ensemble_summary.get('overfitting_analysis', {}).get('overfitting_rate', 0)
        if overfitting_rate > 0.3:
            report_lines.append("• HIGH OVERFITTING DETECTED - Consider:")
            report_lines.append("  - Reducing model complexity")
            report_lines.append("  - Adding more regularization")
            report_lines.append("  - Increasing training data")
        
        # Performance warnings
        if best_overall_score < 0.55:
            report_lines.append("• LOW DIRECTIONAL ACCURACY - Consider:")
            report_lines.append("  - Adding more features")
            report_lines.append("  - Trying different model architectures")
            report_lines.append("  - Improving data quality")
    
    report_lines.append("")
    report_lines.append("=" * 80)
    
    # Print report
    report_text = "\n".join(report_lines)
    print(report_text)
    
    # Save report to file
    try:
        with open("training_summary_report.txt", "w") as f:
            f.write(report_text)
        logger.info("Summary report saved to training_summary_report.txt")
    except Exception as e:
        logger.error(f"Error saving summary report: {e}")


if __name__ == "__main__":
    main()
