version: '3.8'

services:
  finrobot-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: finrobot-api
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - LOG_LEVEL=INFO
      - CACHE_TTL_HOURS=6
      - DATABASE_URL=postgresql://finrobot:${POSTGRES_PASSWORD}@postgres:5432/finrobot
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./models:/app/models
      - ./cache:/app/cache
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - finrobot-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  finrobot-worker:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: finrobot-worker
    command: ["python", "-m", "finrobot.worker.main"]
    environment:
      - ENVIRONMENT=production
      - LOG_LEVEL=INFO
      - CACHE_TTL_HOURS=6
      - DATABASE_URL=postgresql://finrobot:${POSTGRES_PASSWORD}@postgres:5432/finrobot
      - REDIS_URL=redis://redis:6379/0
      - WORKER_TYPE=training
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./models:/app/models
      - ./cache:/app/cache
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - finrobot-network

  finrobot-scheduler:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: finrobot-scheduler
    command: ["python", "-m", "finrobot.scheduler.main"]
    environment:
      - ENVIRONMENT=production
      - LOG_LEVEL=INFO
      - DATABASE_URL=postgresql://finrobot:${POSTGRES_PASSWORD}@postgres:5432/finrobot
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - finrobot-network

  postgres:
    image: postgres:15-alpine
    container_name: finrobot-postgres
    environment:
      - POSTGRES_DB=finrobot
      - POSTGRES_USER=finrobot
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - finrobot-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U finrobot -d finrobot"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    container_name: finrobot-redis
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped
    networks:
      - finrobot-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    container_name: finrobot-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - finrobot-api
    restart: unless-stopped
    networks:
      - finrobot-network

  prometheus:
    image: prom/prometheus:latest
    container_name: finrobot-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - finrobot-network

  grafana:
    image: grafana/grafana:latest
    container_name: finrobot-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - finrobot-network

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  finrobot-network:
    driver: bridge
