Collecting pyautogen
  Downloading pyautogen-0.9.0-py3-none-any.whl.metadata (34 kB)
Requirement already satisfied: anyio<5.0.0,>=3.0.0 in /opt/homebrew/lib/python3.11/site-packages (from pyautogen) (4.9.0)
Collecting asyncer==0.0.8 (from pyautogen)
  Downloading asyncer-0.0.8-py3-none-any.whl.metadata (6.7 kB)
Requirement already satisfied: diskcache in /opt/homebrew/lib/python3.11/site-packages (from pyautogen) (5.6.3)
Collecting docker (from pyautogen)
  Downloading docker-7.1.0-py3-none-any.whl.metadata (3.8 kB)
Collecting httpx<1,>=0.28.1 (from pyautogen)
  Using cached httpx-0.28.1-py3-none-any.whl.metadata (7.1 kB)
Requirement already satisfied: packaging in /opt/homebrew/lib/python3.11/site-packages (from pyautogen) (23.2)
Collecting pydantic<3,>=2.6.1 (from pyautogen)
  Using cached pydantic-2.11.7-py3-none-any.whl.metadata (67 kB)
Requirement already satisfied: python-dotenv in /opt/homebrew/lib/python3.11/site-packages (from pyautogen) (1.0.0)
Requirement already satisfied: termcolor in /opt/homebrew/lib/python3.11/site-packages (from pyautogen) (3.1.0)
Requirement already satisfied: tiktoken in /opt/homebrew/lib/python3.11/site-packages (from pyautogen) (0.9.0)
Requirement already satisfied: idna>=2.8 in /opt/homebrew/lib/python3.11/site-packages (from anyio<5.0.0,>=3.0.0->pyautogen) (3.10)
Requirement already satisfied: sniffio>=1.1 in /opt/homebrew/lib/python3.11/site-packages (from anyio<5.0.0,>=3.0.0->pyautogen) (1.3.1)
Requirement already satisfied: typing_extensions>=4.5 in /opt/homebrew/lib/python3.11/site-packages (from anyio<5.0.0,>=3.0.0->pyautogen) (4.14.0)
Requirement already satisfied: certifi in /opt/homebrew/lib/python3.11/site-packages (from httpx<1,>=0.28.1->pyautogen) (2025.6.15)
Collecting httpcore==1.* (from httpx<1,>=0.28.1->pyautogen)
  Using cached httpcore-1.0.9-py3-none-any.whl.metadata (21 kB)
Collecting h11>=0.16 (from httpcore==1.*->httpx<1,>=0.28.1->pyautogen)
  Using cached h11-0.16.0-py3-none-any.whl.metadata (8.3 kB)
Requirement already satisfied: annotated-types>=0.6.0 in /opt/homebrew/lib/python3.11/site-packages (from pydantic<3,>=2.6.1->pyautogen) (0.7.0)
Collecting pydantic-core==2.33.2 (from pydantic<3,>=2.6.1->pyautogen)
  Using cached pydantic_core-2.33.2-cp311-cp311-macosx_11_0_arm64.whl.metadata (6.8 kB)
Requirement already satisfied: typing-inspection>=0.4.0 in /opt/homebrew/lib/python3.11/site-packages (from pydantic<3,>=2.6.1->pyautogen) (0.4.1)
Requirement already satisfied: requests>=2.26.0 in /opt/homebrew/lib/python3.11/site-packages (from docker->pyautogen) (2.31.0)
Requirement already satisfied: urllib3>=1.26.0 in /opt/homebrew/lib/python3.11/site-packages (from docker->pyautogen) (1.26.20)
Requirement already satisfied: charset-normalizer<4,>=2 in /opt/homebrew/lib/python3.11/site-packages (from requests>=2.26.0->docker->pyautogen) (3.4.2)
Requirement already satisfied: regex>=2022.1.18 in /opt/homebrew/lib/python3.11/site-packages (from tiktoken->pyautogen) (2024.11.6)
Downloading pyautogen-0.9.0-py3-none-any.whl (781 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 781.7/781.7 kB 1.8 MB/s eta 0:00:00
Downloading asyncer-0.0.8-py3-none-any.whl (9.2 kB)
Using cached httpx-0.28.1-py3-none-any.whl (73 kB)
Using cached httpcore-1.0.9-py3-none-any.whl (78 kB)
Using cached pydantic-2.11.7-py3-none-any.whl (444 kB)
Using cached pydantic_core-2.33.2-cp311-cp311-macosx_11_0_arm64.whl (1.9 MB)
Using cached h11-0.16.0-py3-none-any.whl (37 kB)
Downloading docker-7.1.0-py3-none-any.whl (147 kB)
Installing collected packages: pydantic-core, h11, pydantic, httpcore, docker, asyncer, httpx, pyautogen
  Attempting uninstall: pydantic-core
    Found existing installation: pydantic_core 2.10.0
    Uninstalling pydantic_core-2.10.0:
      Successfully uninstalled pydantic_core-2.10.0
  Attempting uninstall: h11
    Found existing installation: h11 0.14.0
    Uninstalling h11-0.14.0:
      Successfully uninstalled h11-0.14.0
  Attempting uninstall: pydantic
    Found existing installation: pydantic 2.4.0
    Uninstalling pydantic-2.4.0:
      Successfully uninstalled pydantic-2.4.0
  Attempting uninstall: httpcore
    Found existing installation: httpcore 0.18.0
    Uninstalling httpcore-0.18.0:
      Successfully uninstalled httpcore-0.18.0
  Attempting uninstall: httpx
    Found existing installation: httpx 0.25.0
    Uninstalling httpx-0.25.0:
      Successfully uninstalled httpx-0.25.0

Successfully installed asyncer-0.0.8 docker-7.1.0 h11-0.16.0 httpcore-1.0.9 httpx-0.28.1 pyautogen-0.9.0 pydantic-2.11.7 pydantic-core-2.33.2
