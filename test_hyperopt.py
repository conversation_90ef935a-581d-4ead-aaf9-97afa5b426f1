#!/usr/bin/env python3
"""
Test hyperparameter optimization functionality.
"""

import logging
import sys
from pathlib import Path

# Add finrobot to path
sys.path.append(str(Path(__file__).parent))

from finrobot.data.loaders.csv_loader import CSV<PERSON>oader
from finrobot.ml.features.feature_pipeline import FeaturePipeline
from finrobot.ml.optimization.hyperopt_optimizer import optimize_model_hyperparameters

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Test hyperparameter optimization."""
    logger.info("=== Testing Hyperparameter Optimization ===")
    
    try:
        # Load data
        loader = CSVLoader(".")
        symbols = loader.get_available_symbols()[:1]  # Use first symbol only
        
        if not symbols:
            logger.error("No symbols available")
            return
        
        symbol = symbols[0]
        logger.info(f"Testing optimization on {symbol}")
        
        # Load and prepare data
        data = loader.load_data(symbol)
        
        # Build features
        feature_pipeline = FeaturePipeline()
        features_data = feature_pipeline.build_features(data, symbol)
        
        # Prepare for ML
        X, y = feature_pipeline.prepare_for_ml(features_data, target_horizon=1)
        
        if len(X) < 100:
            logger.warning(f"Insufficient data for optimization: {len(X)} samples")
            return
        
        # Split data
        split_idx = int(len(X) * 0.8)
        X_train, X_val = X[:split_idx], X[split_idx:]
        y_train, y_val = y[:split_idx], y[split_idx:]
        
        logger.info(f"Train: {len(X_train)}, Val: {len(X_val)}")
        
        # Test optimization
        result = optimize_model_hyperparameters(
            model_name='lightgbm',
            X_train=X_train,
            y_train=y_train,
            X_val=X_val,
            y_val=y_val,
            max_evals=5,  # Small number for testing
            objective_metric='rmse'
        )
        
        logger.info("=== Optimization Results ===")
        logger.info(f"Status: {result['status']}")
        logger.info(f"Best score: {result['best_score']}")
        logger.info(f"Best params: {result['best_params']}")
        logger.info(f"History length: {len(result['optimization_history'])}")
        
        logger.info("✅ Hyperparameter optimization test completed successfully!")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        raise

if __name__ == "__main__":
    main()
