"""Time series data processor for FinRobot-Pro."""

import logging
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
from scipy import stats

from ...config import get_config

logger = logging.getLogger(__name__)


class TimeSeriesProcessor:
    """Advanced time series data processor with financial market focus."""
    
    def __init__(self):
        """Initialize time series processor."""
        self.config = get_config()
        self.data_config = self.config.data
        
        # Configuration parameters
        self.missing_threshold = self.data_config.get("missing_data_threshold", 0.05)
        self.outlier_threshold = self.data_config.get("outlier_threshold", 5.0)
        self.min_history_days = self.data_config.get("min_history_days", 252)
    
    def clean_ohlcv_data(self, data: pd.DataFrame, symbol: str = "") -> pd.DataFrame:
        """Clean OHLCV data with financial market-specific logic.
        
        Args:
            data: OHLCV DataFrame
            symbol: Symbol name for logging
            
        Returns:
            Cleaned DataFrame
        """
        logger.info(f"Cleaning OHLCV data for {symbol}")
        original_length = len(data)
        
        if data.empty:
            logger.warning(f"Empty dataset for {symbol}")
            return data
        
        # Make a copy to avoid modifying original
        cleaned_data = data.copy()
        
        # 1. Remove duplicate dates
        cleaned_data = cleaned_data[~cleaned_data.index.duplicated(keep='first')]
        
        # 2. Sort by date
        cleaned_data = cleaned_data.sort_index()
        
        # 3. Validate OHLC relationships
        cleaned_data = self._validate_ohlc_relationships(cleaned_data)
        
        # 4. Handle missing values
        cleaned_data = self._handle_missing_values(cleaned_data)
        
        # 5. Detect and handle outliers
        cleaned_data = self._handle_outliers(cleaned_data)
        
        # 6. Ensure minimum data requirements
        if len(cleaned_data) < self.min_history_days:
            logger.warning(f"Insufficient data for {symbol}: {len(cleaned_data)} < {self.min_history_days} days")
        
        # 7. Add data quality metrics
        cleaned_data = self._add_quality_metrics(cleaned_data, original_length)
        
        logger.info(f"Cleaned data for {symbol}: {original_length} -> {len(cleaned_data)} records")
        return cleaned_data
    
    def _validate_ohlc_relationships(self, data: pd.DataFrame) -> pd.DataFrame:
        """Validate OHLC price relationships.
        
        Args:
            data: OHLCV DataFrame
            
        Returns:
            DataFrame with invalid OHLC relationships fixed
        """
        if not all(col in data.columns for col in ['Open', 'High', 'Low', 'Close']):
            return data
        
        # Check for invalid OHLC relationships
        invalid_high = data['High'] < data[['Open', 'Close']].max(axis=1)
        invalid_low = data['Low'] > data[['Open', 'Close']].min(axis=1)
        
        if invalid_high.any():
            logger.warning(f"Found {invalid_high.sum()} records with High < max(Open, Close)")
            # Fix by setting High to max of Open, Close
            data.loc[invalid_high, 'High'] = data.loc[invalid_high, ['Open', 'Close']].max(axis=1)
        
        if invalid_low.any():
            logger.warning(f"Found {invalid_low.sum()} records with Low > min(Open, Close)")
            # Fix by setting Low to min of Open, Close
            data.loc[invalid_low, 'Low'] = data.loc[invalid_low, ['Open', 'Close']].min(axis=1)
        
        return data
    
    def _handle_missing_values(self, data: pd.DataFrame) -> pd.DataFrame:
        """Handle missing values in time series data.
        
        Args:
            data: DataFrame with potential missing values
            
        Returns:
            DataFrame with missing values handled
        """
        # Calculate missing value percentage
        missing_pct = data.isnull().sum() / len(data)
        
        for column, pct in missing_pct.items():
            if pct > self.missing_threshold:
                logger.warning(f"High missing values in {column}: {pct:.2%}")
        
        # Forward fill for price data (common in financial time series)
        price_columns = ['Open', 'High', 'Low', 'Close']
        for col in price_columns:
            if col in data.columns:
                data[col] = data[col].fillna(method='ffill')
        
        # Handle volume separately (can be zero)
        if 'Volume' in data.columns:
            data['Volume'] = data['Volume'].fillna(0)
        
        # Drop rows with remaining missing values in critical columns
        critical_columns = ['Close']  # Close price is most critical
        data = data.dropna(subset=[col for col in critical_columns if col in data.columns])
        
        return data
    
    def _handle_outliers(self, data: pd.DataFrame) -> pd.DataFrame:
        """Detect and handle outliers using financial market context.
        
        Args:
            data: DataFrame to process
            
        Returns:
            DataFrame with outliers handled
        """
        # Calculate returns for outlier detection
        if 'Close' in data.columns:
            returns = data['Close'].pct_change().dropna()
            
            # Use z-score for outlier detection
            z_scores = np.abs(stats.zscore(returns))
            outliers = z_scores > self.outlier_threshold
            
            if outliers.any():
                logger.warning(f"Found {outliers.sum()} outlier returns (z-score > {self.outlier_threshold})")
                
                # Cap extreme returns (winsorization)
                percentile_99 = returns.quantile(0.99)
                percentile_1 = returns.quantile(0.01)
                
                # Apply winsorization to returns, then reconstruct prices
                capped_returns = returns.clip(lower=percentile_1, upper=percentile_99)
                
                # Reconstruct price series from capped returns
                initial_price = data['Close'].iloc[0]
                reconstructed_prices = initial_price * (1 + capped_returns).cumprod()
                
                # Update the Close prices
                data.loc[returns.index, 'Close'] = reconstructed_prices
                
                # Adjust other OHLC prices proportionally
                if all(col in data.columns for col in ['Open', 'High', 'Low']):
                    price_ratio = reconstructed_prices / data.loc[returns.index, 'Close']
                    data.loc[returns.index, 'Open'] *= price_ratio
                    data.loc[returns.index, 'High'] *= price_ratio
                    data.loc[returns.index, 'Low'] *= price_ratio
        
        return data
    
    def _add_quality_metrics(self, data: pd.DataFrame, original_length: int) -> pd.DataFrame:
        """Add data quality metrics as metadata.
        
        Args:
            data: Processed DataFrame
            original_length: Original data length
            
        Returns:
            DataFrame with quality metrics added as attributes
        """
        if hasattr(data, 'attrs'):
            data.attrs['quality_metrics'] = {
                'original_length': original_length,
                'final_length': len(data),
                'data_retention': len(data) / original_length if original_length > 0 else 0,
                'missing_data_pct': data.isnull().sum().sum() / (len(data) * len(data.columns)),
                'date_range_days': (data.index.max() - data.index.min()).days if len(data) > 0 else 0
            }
        
        return data
    
    def resample_data(self, data: pd.DataFrame, frequency: str = 'D') -> pd.DataFrame:
        """Resample time series data to different frequency.
        
        Args:
            data: OHLCV DataFrame
            frequency: Target frequency ('D', 'W', 'M', etc.)
            
        Returns:
            Resampled DataFrame
        """
        if data.empty:
            return data
        
        # Define aggregation rules for OHLCV data
        agg_rules = {
            'Open': 'first',
            'High': 'max',
            'Low': 'min',
            'Close': 'last',
            'Volume': 'sum'
        }
        
        # Apply aggregation rules only to existing columns
        available_rules = {k: v for k, v in agg_rules.items() if k in data.columns}
        
        if not available_rules:
            logger.warning("No OHLCV columns found for resampling")
            return data
        
        resampled = data.resample(frequency).agg(available_rules)
        
        # Remove rows with NaN values (weekends, holidays)
        resampled = resampled.dropna()
        
        logger.info(f"Resampled data from {len(data)} to {len(resampled)} records at {frequency} frequency")
        return resampled
    
    def align_multiple_series(self, data_dict: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """Align multiple time series to common date range.
        
        Args:
            data_dict: Dictionary of symbol -> DataFrame
            
        Returns:
            Dictionary with aligned DataFrames
        """
        if not data_dict:
            return data_dict
        
        # Find common date range
        start_dates = []
        end_dates = []
        
        for symbol, df in data_dict.items():
            if not df.empty:
                start_dates.append(df.index.min())
                end_dates.append(df.index.max())
        
        if not start_dates:
            logger.warning("No valid data found for alignment")
            return data_dict
        
        common_start = max(start_dates)
        common_end = min(end_dates)
        
        logger.info(f"Aligning data to common range: {common_start} to {common_end}")
        
        # Align all series to common range
        aligned_data = {}
        for symbol, df in data_dict.items():
            if not df.empty:
                aligned_df = df[(df.index >= common_start) & (df.index <= common_end)]
                aligned_data[symbol] = aligned_df
                logger.debug(f"Aligned {symbol}: {len(df)} -> {len(aligned_df)} records")
            else:
                aligned_data[symbol] = df
        
        return aligned_data
    
    def calculate_returns(self, data: pd.DataFrame, 
                         method: str = 'simple',
                         periods: int = 1) -> pd.DataFrame:
        """Calculate returns for price data.
        
        Args:
            data: OHLCV DataFrame
            method: 'simple' or 'log' returns
            periods: Number of periods for return calculation
            
        Returns:
            DataFrame with returns
        """
        if 'Close' not in data.columns:
            raise ValueError("Close price column required for return calculation")
        
        if method == 'simple':
            returns = data['Close'].pct_change(periods=periods)
        elif method == 'log':
            returns = np.log(data['Close'] / data['Close'].shift(periods))
        else:
            raise ValueError("Method must be 'simple' or 'log'")
        
        return returns.dropna()
