"""Cache management system for FinRobot-Pro."""

import hashlib
import json
import logging
import pickle
from pathlib import Path
from typing import Any, Optional, Union

import diskcache as dc
import pandas as pd

from ..config import get_config, get_settings

logger = logging.getLogger(__name__)


class CacheManager:
    """Disk-based cache manager with TTL support."""
    
    def __init__(self, cache_dir: Optional[str] = None, ttl_hours: Optional[int] = None):
        """Initialize cache manager.
        
        Args:
            cache_dir: Cache directory path
            ttl_hours: Time-to-live in hours
        """
        settings = get_settings()
        config = get_config()
        
        self.cache_dir = Path(cache_dir or settings.cache_dir)
        self.ttl_hours = ttl_hours or config.cache.ttl_hours
        self.ttl_seconds = self.ttl_hours * 3600
        
        # Create cache directory
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize cache
        max_size_bytes = config.cache.get("max_size_gb", 10) * 1024**3
        self.cache = dc.Cache(
            str(self.cache_dir),
            size_limit=max_size_bytes,
            eviction_policy="least-recently-used"
        )
        
        logger.info(f"Cache initialized at {self.cache_dir} with TTL {self.ttl_hours}h")
    
    def _generate_key(self, *args, **kwargs) -> str:
        """Generate cache key from arguments."""
        key_data = {
            "args": args,
            "kwargs": sorted(kwargs.items()) if kwargs else {}
        }
        key_str = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cached value or None if not found/expired
        """
        try:
            return self.cache.get(key)
        except Exception as e:
            logger.warning(f"Cache get error for key {key}: {e}")
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time-to-live in seconds (overrides default)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            expire_time = ttl or self.ttl_seconds
            self.cache.set(key, value, expire=expire_time)
            return True
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """Delete value from cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if successful, False otherwise
        """
        try:
            return self.cache.delete(key)
        except Exception as e:
            logger.warning(f"Cache delete error for key {key}: {e}")
            return False
    
    def clear(self) -> bool:
        """Clear all cache entries.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            self.cache.clear()
            logger.info("Cache cleared successfully")
            return True
        except Exception as e:
            logger.error(f"Cache clear error: {e}")
            return False
    
    def get_stats(self) -> dict:
        """Get cache statistics.
        
        Returns:
            Dictionary with cache statistics
        """
        try:
            return {
                "size": len(self.cache),
                "volume": self.cache.volume(),
                "hits": getattr(self.cache, "hits", 0),
                "misses": getattr(self.cache, "misses", 0),
                "directory": str(self.cache_dir),
                "ttl_hours": self.ttl_hours
            }
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {}
    
    def cached_call(self, func, *args, **kwargs):
        """Decorator-like method for caching function calls.
        
        Args:
            func: Function to cache
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Function result (cached or fresh)
        """
        # Generate cache key
        cache_key = self._generate_key(func.__name__, *args, **kwargs)
        
        # Try to get from cache
        result = self.get(cache_key)
        if result is not None:
            logger.debug(f"Cache hit for {func.__name__}")
            return result
        
        # Execute function and cache result
        logger.debug(f"Cache miss for {func.__name__}, executing...")
        try:
            result = func(*args, **kwargs)
            self.set(cache_key, result)
            return result
        except Exception as e:
            logger.error(f"Error executing cached function {func.__name__}: {e}")
            raise
    
    def cache_dataframe(self, df: pd.DataFrame, key: str, ttl: Optional[int] = None) -> bool:
        """Cache a pandas DataFrame.
        
        Args:
            df: DataFrame to cache
            key: Cache key
            ttl: Time-to-live in seconds
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Convert DataFrame to parquet bytes for efficient storage
            parquet_bytes = df.to_parquet()
            return self.set(f"df_{key}", parquet_bytes, ttl)
        except Exception as e:
            logger.error(f"Error caching DataFrame {key}: {e}")
            return False
    
    def get_dataframe(self, key: str) -> Optional[pd.DataFrame]:
        """Get cached DataFrame.
        
        Args:
            key: Cache key
            
        Returns:
            DataFrame or None if not found
        """
        try:
            parquet_bytes = self.get(f"df_{key}")
            if parquet_bytes is not None:
                return pd.read_parquet(parquet_bytes)
            return None
        except Exception as e:
            logger.error(f"Error retrieving cached DataFrame {key}: {e}")
            return None
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.cache.close()


# Global cache instance
_cache_manager = CacheManager()


def get_cache() -> CacheManager:
    """Get the global cache manager instance."""
    return _cache_manager
