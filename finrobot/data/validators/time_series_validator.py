"""Time series data validator for FinRobot-Pro."""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
from scipy import stats

from ...config import get_config

logger = logging.getLogger(__name__)


class ValidationResult:
    """Container for validation results."""
    
    def __init__(self):
        self.is_valid = True
        self.errors = []
        self.warnings = []
        self.metrics = {}
    
    def add_error(self, message: str):
        """Add validation error."""
        self.errors.append(message)
        self.is_valid = False
    
    def add_warning(self, message: str):
        """Add validation warning."""
        self.warnings.append(message)
    
    def add_metric(self, name: str, value):
        """Add validation metric."""
        self.metrics[name] = value


class TimeSeriesValidator:
    """Comprehensive time series data validator for financial data."""
    
    def __init__(self):
        """Initialize validator."""
        self.config = get_config()
        self.data_config = self.config.data
        
        # Validation thresholds
        self.missing_threshold = self.data_config.get("missing_data_threshold", 0.05)
        self.outlier_threshold = self.data_config.get("outlier_threshold", 5.0)
        self.min_history_days = self.data_config.get("min_history_days", 252)
    
    def validate_ohlcv_data(self, data: pd.DataFrame, symbol: str = "") -> ValidationResult:
        """Comprehensive validation of OHLCV data.
        
        Args:
            data: OHLCV DataFrame to validate
            symbol: Symbol name for logging
            
        Returns:
            ValidationResult object
        """
        result = ValidationResult()
        
        logger.info(f"Validating OHLCV data for {symbol}")
        
        # Basic structure validation
        self._validate_structure(data, result)
        
        if not result.is_valid:
            return result
        
        # Data quality validation
        self._validate_data_quality(data, result)
        
        # Financial data specific validation
        self._validate_ohlc_relationships(data, result)
        self._validate_price_continuity(data, result)
        self._validate_volume_data(data, result)
        
        # Statistical validation
        self._validate_returns_distribution(data, result)
        self._detect_outliers(data, result)
        
        # Temporal validation
        self._validate_temporal_consistency(data, result)
        
        # Add summary metrics
        self._add_summary_metrics(data, result)
        
        logger.info(f"Validation complete for {symbol}: "
                   f"{'PASSED' if result.is_valid else 'FAILED'} "
                   f"({len(result.errors)} errors, {len(result.warnings)} warnings)")
        
        return result
    
    def _validate_structure(self, data: pd.DataFrame, result: ValidationResult):
        """Validate basic data structure."""
        if data.empty:
            result.add_error("Dataset is empty")
            return
        
        # Check for required columns
        required_columns = ['Open', 'High', 'Low', 'Close']
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            result.add_error(f"Missing required columns: {missing_columns}")
        
        # Check index type
        if not isinstance(data.index, pd.DatetimeIndex):
            result.add_error("Index must be DatetimeIndex")
        
        # Check for minimum data length
        if len(data) < self.min_history_days:
            result.add_warning(f"Insufficient data: {len(data)} < {self.min_history_days} days")
        
        result.add_metric("total_records", len(data))
        result.add_metric("columns", list(data.columns))
    
    def _validate_data_quality(self, data: pd.DataFrame, result: ValidationResult):
        """Validate data quality metrics."""
        # Check for missing values
        missing_counts = data.isnull().sum()
        total_cells = len(data) * len(data.columns)
        missing_pct = missing_counts.sum() / total_cells
        
        result.add_metric("missing_data_pct", missing_pct)
        
        if missing_pct > self.missing_threshold:
            result.add_error(f"High missing data: {missing_pct:.2%} > {self.missing_threshold:.2%}")
        
        # Check for duplicate dates
        duplicate_dates = data.index.duplicated().sum()
        if duplicate_dates > 0:
            result.add_error(f"Found {duplicate_dates} duplicate dates")
        
        result.add_metric("duplicate_dates", duplicate_dates)
        
        # Check for non-positive prices
        price_columns = ['Open', 'High', 'Low', 'Close']
        for col in price_columns:
            if col in data.columns:
                non_positive = (data[col] <= 0).sum()
                if non_positive > 0:
                    result.add_error(f"Found {non_positive} non-positive values in {col}")
    
    def _validate_ohlc_relationships(self, data: pd.DataFrame, result: ValidationResult):
        """Validate OHLC price relationships."""
        if not all(col in data.columns for col in ['Open', 'High', 'Low', 'Close']):
            return
        
        # High should be >= max(Open, Close)
        invalid_high = data['High'] < data[['Open', 'Close']].max(axis=1)
        high_violations = invalid_high.sum()
        
        if high_violations > 0:
            result.add_error(f"Found {high_violations} records where High < max(Open, Close)")
        
        # Low should be <= min(Open, Close)
        invalid_low = data['Low'] > data[['Open', 'Close']].min(axis=1)
        low_violations = invalid_low.sum()
        
        if low_violations > 0:
            result.add_error(f"Found {low_violations} records where Low > min(Open, Close)")
        
        result.add_metric("ohlc_high_violations", high_violations)
        result.add_metric("ohlc_low_violations", low_violations)
    
    def _validate_price_continuity(self, data: pd.DataFrame, result: ValidationResult):
        """Validate price continuity and detect gaps."""
        if 'Close' not in data.columns or len(data) < 2:
            return
        
        # Calculate price gaps (overnight gaps)
        price_gaps = (data['Open'] - data['Close'].shift(1)) / data['Close'].shift(1)
        price_gaps = price_gaps.dropna()
        
        # Detect large gaps (> 20%)
        large_gaps = (abs(price_gaps) > 0.20).sum()
        if large_gaps > 0:
            result.add_warning(f"Found {large_gaps} large price gaps (>20%)")
        
        result.add_metric("large_price_gaps", large_gaps)
        result.add_metric("max_price_gap", abs(price_gaps).max() if len(price_gaps) > 0 else 0)
    
    def _validate_volume_data(self, data: pd.DataFrame, result: ValidationResult):
        """Validate volume data."""
        if 'Volume' not in data.columns:
            result.add_warning("Volume data not available")
            return
        
        # Check for negative volume
        negative_volume = (data['Volume'] < 0).sum()
        if negative_volume > 0:
            result.add_error(f"Found {negative_volume} negative volume values")
        
        # Check for zero volume days
        zero_volume = (data['Volume'] == 0).sum()
        zero_volume_pct = zero_volume / len(data)
        
        if zero_volume_pct > 0.1:  # More than 10% zero volume days
            result.add_warning(f"High zero volume days: {zero_volume_pct:.2%}")
        
        result.add_metric("zero_volume_days", zero_volume)
        result.add_metric("zero_volume_pct", zero_volume_pct)
    
    def _validate_returns_distribution(self, data: pd.DataFrame, result: ValidationResult):
        """Validate returns distribution."""
        if 'Close' not in data.columns or len(data) < 30:
            return
        
        # Calculate daily returns
        returns = data['Close'].pct_change().dropna()
        
        if len(returns) == 0:
            return
        
        # Basic statistics
        mean_return = returns.mean()
        std_return = returns.std()
        skewness = stats.skew(returns)
        kurtosis = stats.kurtosis(returns)
        
        # Check for extreme statistics
        if abs(skewness) > 3:
            result.add_warning(f"High skewness in returns: {skewness:.2f}")
        
        if kurtosis > 10:
            result.add_warning(f"High kurtosis in returns: {kurtosis:.2f}")
        
        result.add_metric("returns_mean", mean_return)
        result.add_metric("returns_std", std_return)
        result.add_metric("returns_skewness", skewness)
        result.add_metric("returns_kurtosis", kurtosis)
    
    def _detect_outliers(self, data: pd.DataFrame, result: ValidationResult):
        """Detect outliers in the data."""
        if 'Close' not in data.columns or len(data) < 30:
            return
        
        returns = data['Close'].pct_change().dropna()
        
        if len(returns) == 0:
            return
        
        # Z-score based outlier detection
        z_scores = np.abs(stats.zscore(returns))
        outliers = (z_scores > self.outlier_threshold).sum()
        outlier_pct = outliers / len(returns)
        
        if outlier_pct > 0.05:  # More than 5% outliers
            result.add_warning(f"High outlier percentage: {outlier_pct:.2%}")
        
        result.add_metric("outliers_count", outliers)
        result.add_metric("outliers_pct", outlier_pct)
        result.add_metric("max_z_score", z_scores.max())
    
    def _validate_temporal_consistency(self, data: pd.DataFrame, result: ValidationResult):
        """Validate temporal consistency."""
        if len(data) < 2:
            return
        
        # Check for proper date ordering
        if not data.index.is_monotonic_increasing:
            result.add_error("Dates are not in ascending order")
        
        # Check for reasonable date range
        date_range = data.index.max() - data.index.min()
        if date_range.days < 30:
            result.add_warning(f"Short date range: {date_range.days} days")
        
        # Check for future dates
        future_dates = (data.index > datetime.now()).sum()
        if future_dates > 0:
            result.add_warning(f"Found {future_dates} future dates")
        
        result.add_metric("date_range_days", date_range.days)
        result.add_metric("start_date", data.index.min())
        result.add_metric("end_date", data.index.max())
    
    def _add_summary_metrics(self, data: pd.DataFrame, result: ValidationResult):
        """Add summary metrics to validation result."""
        if 'Close' in data.columns:
            result.add_metric("price_range", {
                "min": data['Close'].min(),
                "max": data['Close'].max(),
                "mean": data['Close'].mean()
            })
        
        if 'Volume' in data.columns:
            result.add_metric("volume_stats", {
                "min": data['Volume'].min(),
                "max": data['Volume'].max(),
                "mean": data['Volume'].mean()
            })
    
    def validate_multiple_series(self, data_dict: Dict[str, pd.DataFrame]) -> Dict[str, ValidationResult]:
        """Validate multiple time series.
        
        Args:
            data_dict: Dictionary of symbol -> DataFrame
            
        Returns:
            Dictionary of symbol -> ValidationResult
        """
        results = {}
        
        for symbol, data in data_dict.items():
            results[symbol] = self.validate_ohlcv_data(data, symbol)
        
        return results
    
    def generate_validation_report(self, results: Dict[str, ValidationResult]) -> str:
        """Generate a comprehensive validation report.
        
        Args:
            results: Dictionary of validation results
            
        Returns:
            Formatted validation report
        """
        report_lines = ["=== Data Validation Report ===\n"]
        
        total_symbols = len(results)
        valid_symbols = sum(1 for r in results.values() if r.is_valid)
        
        report_lines.append(f"Total Symbols: {total_symbols}")
        report_lines.append(f"Valid Symbols: {valid_symbols}")
        report_lines.append(f"Invalid Symbols: {total_symbols - valid_symbols}\n")
        
        for symbol, result in results.items():
            status = "✓ VALID" if result.is_valid else "✗ INVALID"
            report_lines.append(f"{symbol}: {status}")
            
            if result.errors:
                report_lines.append(f"  Errors: {len(result.errors)}")
                for error in result.errors[:3]:  # Show first 3 errors
                    report_lines.append(f"    - {error}")
            
            if result.warnings:
                report_lines.append(f"  Warnings: {len(result.warnings)}")
                for warning in result.warnings[:2]:  # Show first 2 warnings
                    report_lines.append(f"    - {warning}")
            
            report_lines.append("")
        
        return "\n".join(report_lines)
