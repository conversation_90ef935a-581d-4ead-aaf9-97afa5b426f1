"""Base data loader class for FinRobot-Pro."""

import logging
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union

import pandas as pd
import requests
from ratelimit import limits, sleep_and_retry
from tenacity import retry, stop_after_attempt, wait_exponential

from ...config import get_config, get_settings
from ..cache_manager import get_cache

logger = logging.getLogger(__name__)


class BaseDataLoader(ABC):
    """Abstract base class for financial data loaders."""
    
    def __init__(self, provider_name: str):
        """Initialize base data loader.
        
        Args:
            provider_name: Name of the data provider
        """
        self.provider_name = provider_name
        self.settings = get_settings()
        self.config = get_config()
        self.cache = get_cache()
        
        # Get provider-specific configuration
        self.provider_config = self.config.data_sources.get(provider_name, {})
        self.rate_limit = self.provider_config.get("rate_limit", 60)  # per minute
        self.timeout = self.provider_config.get("timeout", 30)
        self.base_url = self.provider_config.get("base_url", "")
        
        # Get API key
        self.api_key = self.settings.get_api_key(provider_name)
        if not self.api_key:
            logger.warning(f"No API key found for {provider_name}")
        
        # Setup session
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": "FinRobot-Pro/1.0.0",
            "Accept": "application/json"
        })
    
    @sleep_and_retry
    @limits(calls=60, period=60)  # Default rate limit
    def _rate_limited_request(self, *args, **kwargs) -> requests.Response:
        """Make rate-limited HTTP request."""
        return self.session.get(*args, **kwargs)
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    def _make_request(self, url: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """Make HTTP request with retry logic.
        
        Args:
            url: Request URL
            params: Query parameters
            
        Returns:
            JSON response data
            
        Raises:
            requests.RequestException: If request fails after retries
        """
        try:
            response = self._rate_limited_request(
                url, 
                params=params, 
                timeout=self.timeout
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Request failed for {url}: {e}")
            raise
    
    def _generate_cache_key(self, symbol: str, start_date: str, end_date: str, 
                          data_type: str = "ohlcv") -> str:
        """Generate cache key for data request.
        
        Args:
            symbol: Stock symbol
            start_date: Start date
            end_date: End date
            data_type: Type of data
            
        Returns:
            Cache key string
        """
        return f"{self.provider_name}_{data_type}_{symbol}_{start_date}_{end_date}"
    
    def _validate_symbol(self, symbol: str) -> str:
        """Validate and normalize symbol.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Normalized symbol
            
        Raises:
            ValueError: If symbol is invalid
        """
        if not symbol or not isinstance(symbol, str):
            raise ValueError("Symbol must be a non-empty string")
        
        return symbol.upper().strip()
    
    def _validate_date_range(self, start_date: Optional[str], 
                           end_date: Optional[str]) -> tuple[str, str]:
        """Validate and normalize date range.
        
        Args:
            start_date: Start date string
            end_date: End date string
            
        Returns:
            Tuple of normalized start and end dates
        """
        if end_date is None:
            end_date = datetime.now().strftime("%Y-%m-%d")
        
        if start_date is None:
            # Default to 1 year ago
            start_dt = datetime.now() - timedelta(days=365)
            start_date = start_dt.strftime("%Y-%m-%d")
        
        # Validate date format
        try:
            datetime.strptime(start_date, "%Y-%m-%d")
            datetime.strptime(end_date, "%Y-%m-%d")
        except ValueError as e:
            raise ValueError(f"Invalid date format. Use YYYY-MM-DD: {e}")
        
        return start_date, end_date
    
    def _normalize_ohlcv_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Normalize OHLCV data format.
        
        Args:
            data: Raw OHLCV data
            
        Returns:
            Normalized DataFrame with standard columns
        """
        # Ensure required columns exist
        required_cols = ["Date", "Open", "High", "Low", "Close", "Volume"]
        
        # Rename columns to standard format
        column_mapping = {
            "date": "Date",
            "timestamp": "Date",
            "time": "Date",
            "open": "Open",
            "high": "High", 
            "low": "Low",
            "close": "Close",
            "adj_close": "Close",
            "adjusted_close": "Close",
            "volume": "Volume",
            "vol": "Volume"
        }
        
        # Apply column mapping
        data = data.rename(columns={k: v for k, v in column_mapping.items() 
                                  if k in data.columns})
        
        # Check for missing required columns
        missing_cols = [col for col in required_cols if col not in data.columns]
        if missing_cols:
            logger.warning(f"Missing columns: {missing_cols}")
        
        # Convert Date column to datetime
        if "Date" in data.columns:
            data["Date"] = pd.to_datetime(data["Date"], utc=True)
            data = data.set_index("Date")
        
        # Ensure numeric columns
        numeric_cols = ["Open", "High", "Low", "Close", "Volume"]
        for col in numeric_cols:
            if col in data.columns:
                data[col] = pd.to_numeric(data[col], errors="coerce")
        
        # Sort by date
        data = data.sort_index()
        
        return data
    
    @abstractmethod
    def get_ohlcv(self, symbol: str, start_date: Optional[str] = None,
                  end_date: Optional[str] = None, interval: str = "1d") -> pd.DataFrame:
        """Get OHLCV data for a symbol.
        
        Args:
            symbol: Stock symbol
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            interval: Data interval
            
        Returns:
            DataFrame with OHLCV data
        """
        pass
    
    @abstractmethod
    def get_fundamentals(self, symbol: str) -> Dict[str, Any]:
        """Get fundamental data for a symbol.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Dictionary with fundamental data
        """
        pass
    
    def get_news(self, symbol: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get news data for a symbol.
        
        Args:
            symbol: Stock symbol
            limit: Maximum number of news items
            
        Returns:
            List of news items
        """
        logger.warning(f"News data not implemented for {self.provider_name}")
        return []
    
    def __del__(self):
        """Cleanup resources."""
        if hasattr(self, "session"):
            self.session.close()
