"""CSV data loader for FinRobot-Pro."""

import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import pandas as pd

from .base_loader import BaseDataLoader

logger = logging.getLogger(__name__)


class CSVLoader(BaseDataLoader):
    """Data loader for CSV files."""
    
    def __init__(self, data_directory: Optional[str] = None):
        """Initialize CSV loader.
        
        Args:
            data_directory: Directory containing CSV files
        """
        super().__init__("csv")
        self.data_directory = Path(data_directory or ".")
        
        # Scan for available CSV files
        self._scan_csv_files()
    
    def _scan_csv_files(self) -> None:
        """Scan directory for CSV files and extract symbols."""
        self.available_files = {}
        
        # Look for CSV files in the data directory
        csv_files = list(self.data_directory.glob("*.csv"))
        
        for csv_file in csv_files:
            # Extract symbol from filename
            filename = csv_file.stem
            
            # Common patterns for symbol extraction
            if "_5Y_FROM_" in filename:
                symbol = filename.split("_5Y_FROM_")[0]
            elif "_" in filename:
                symbol = filename.split("_")[0]
            else:
                symbol = filename
            
            self.available_files[symbol.upper()] = csv_file
        
        logger.info(f"Found {len(self.available_files)} CSV files: {list(self.available_files.keys())}")
    
    def get_available_symbols(self) -> List[str]:
        """Get list of available symbols.
        
        Returns:
            List of available symbols
        """
        return list(self.available_files.keys())
    
    def get_ohlcv(self, symbol: str, start_date: Optional[str] = None,
                  end_date: Optional[str] = None, interval: str = "1d") -> pd.DataFrame:
        """Get OHLCV data from CSV file.
        
        Args:
            symbol: Stock symbol
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            interval: Data interval (ignored for CSV)
            
        Returns:
            DataFrame with OHLCV data
        """
        symbol = self._validate_symbol(symbol)
        start_date, end_date = self._validate_date_range(start_date, end_date)
        
        # Check cache first
        cache_key = self._generate_cache_key(symbol, start_date, end_date)
        cached_data = self.cache.get_dataframe(cache_key)
        if cached_data is not None:
            logger.debug(f"Cache hit for {symbol} OHLCV data")
            return cached_data
        
        # Check if symbol is available
        if symbol not in self.available_files:
            raise ValueError(f"Symbol {symbol} not found in CSV files. Available: {list(self.available_files.keys())}")
        
        csv_file = self.available_files[symbol]
        
        try:
            # Read CSV file
            logger.info(f"Loading OHLCV data for {symbol} from {csv_file}")
            data = pd.read_csv(csv_file)
            
            # Normalize data format
            data = self._normalize_ohlcv_data(data)
            
            # Filter by date range
            if start_date and end_date:
                start_dt = pd.to_datetime(start_date, utc=True)
                end_dt = pd.to_datetime(end_date, utc=True)
                data = data[(data.index >= start_dt) & (data.index <= end_dt)]
            
            # Cache the result
            self.cache.cache_dataframe(data, cache_key)
            
            logger.info(f"Loaded {len(data)} records for {symbol}")
            return data
            
        except Exception as e:
            logger.error(f"Error loading CSV data for {symbol}: {e}")
            raise
    
    def get_fundamentals(self, symbol: str) -> Dict[str, Any]:
        """Get fundamental data (not available for CSV files).
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Empty dictionary (fundamentals not available in CSV)
        """
        logger.warning("Fundamental data not available for CSV files")
        return {}
    
    def get_multiple_symbols(self, symbols: List[str], 
                           start_date: Optional[str] = None,
                           end_date: Optional[str] = None) -> Dict[str, pd.DataFrame]:
        """Get OHLCV data for multiple symbols.
        
        Args:
            symbols: List of stock symbols
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            
        Returns:
            Dictionary mapping symbols to DataFrames
        """
        results = {}
        
        for symbol in symbols:
            try:
                data = self.get_ohlcv(symbol, start_date, end_date)
                results[symbol] = data
                logger.info(f"Successfully loaded data for {symbol}")
            except Exception as e:
                logger.error(f"Failed to load data for {symbol}: {e}")
                results[symbol] = pd.DataFrame()
        
        return results
    
    def get_combined_data(self, symbols: List[str], 
                         start_date: Optional[str] = None,
                         end_date: Optional[str] = None,
                         column: str = "Close") -> pd.DataFrame:
        """Get combined data for multiple symbols.
        
        Args:
            symbols: List of stock symbols
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            column: Column to extract (default: Close)
            
        Returns:
            DataFrame with symbols as columns
        """
        combined_data = pd.DataFrame()
        
        for symbol in symbols:
            try:
                data = self.get_ohlcv(symbol, start_date, end_date)
                if not data.empty and column in data.columns:
                    combined_data[symbol] = data[column]
                    logger.debug(f"Added {symbol} to combined dataset")
            except Exception as e:
                logger.error(f"Failed to add {symbol} to combined dataset: {e}")
        
        return combined_data
    
    def get_data_summary(self) -> Dict[str, Dict[str, Any]]:
        """Get summary of available data.
        
        Returns:
            Dictionary with data summary for each symbol
        """
        summary = {}
        
        for symbol in self.available_files.keys():
            try:
                data = self.get_ohlcv(symbol)
                summary[symbol] = {
                    "records": len(data),
                    "start_date": data.index.min().strftime("%Y-%m-%d") if not data.empty else None,
                    "end_date": data.index.max().strftime("%Y-%m-%d") if not data.empty else None,
                    "columns": list(data.columns),
                    "file_path": str(self.available_files[symbol])
                }
            except Exception as e:
                logger.error(f"Error getting summary for {symbol}: {e}")
                summary[symbol] = {"error": str(e)}
        
        return summary
