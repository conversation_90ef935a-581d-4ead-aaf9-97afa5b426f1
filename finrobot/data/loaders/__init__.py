"""Data loaders for various financial data sources."""

from .base_loader import BaseDataLoader
from .csv_loader import CSVLoader

# Import other loaders if they exist
try:
    from .alpha_vantage_loader import AlphaVantageLoader
except ImportError:
    AlphaVantageLoader = None

try:
    from .finnhub_loader import FinnhubLoader
except ImportError:
    FinnhubLoader = None

try:
    from .polygon_loader import PolygonLoader
except ImportError:
    PolygonLoader = None

try:
    from .tiingo_loader import TiingoLoader
except ImportError:
    TiingoLoader = None

try:
    from .multi_source_loader import MultiSourceLoader
except ImportError:
    MultiSourceLoader = None

__all__ = [
    "BaseDataLoader",
    "CSVLoader",
]

# Add to __all__ only if imported successfully
if AlphaVantageLoader:
    __all__.append("AlphaVantageLoader")
if FinnhubLoader:
    __all__.append("FinnhubLoader")
if PolygonLoader:
    __all__.append("PolygonLoader")
if TiingoLoader:
    __all__.append("TiingoLoader")
if MultiSourceLoader:
    __all__.append("MultiSourceLoader")
