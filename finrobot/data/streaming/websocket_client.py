"""WebSocket client for real-time data streaming."""

import asyncio
import json
import logging
from typing import Dict, Any, Optional, Callable, List
import websockets
from datetime import datetime
import pandas as pd

logger = logging.getLogger(__name__)


class WebSocketClient:
    """
    Generic WebSocket client for real-time financial data streaming.
    
    Supports multiple data providers with automatic reconnection,
    heartbeat monitoring, and message queuing.
    """
    
    def __init__(
        self,
        url: str,
        api_key: Optional[str] = None,
        reconnect_interval: int = 5,
        max_reconnect_attempts: int = 10,
        heartbeat_interval: int = 30
    ):
        """
        Initialize WebSocket client.
        
        Args:
            url: WebSocket URL
            api_key: API key for authentication
            reconnect_interval: Seconds between reconnection attempts
            max_reconnect_attempts: Maximum reconnection attempts
            heartbeat_interval: Heartbeat interval in seconds
        """
        self.url = url
        self.api_key = api_key
        self.reconnect_interval = reconnect_interval
        self.max_reconnect_attempts = max_reconnect_attempts
        self.heartbeat_interval = heartbeat_interval
        
        self.websocket = None
        self.is_connected = False
        self.reconnect_attempts = 0
        self.message_handlers = {}
        self.subscriptions = set()
        
        # Message queue for buffering
        self.message_queue = asyncio.Queue()
        self.last_heartbeat = None
        
    async def connect(self) -> bool:
        """
        Connect to WebSocket server.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            logger.info(f"Connecting to WebSocket: {self.url}")
            
            # Add authentication headers if API key provided
            headers = {}
            if self.api_key:
                headers['Authorization'] = f'Bearer {self.api_key}'
            
            self.websocket = await websockets.connect(
                self.url,
                extra_headers=headers,
                ping_interval=self.heartbeat_interval,
                ping_timeout=10
            )
            
            self.is_connected = True
            self.reconnect_attempts = 0
            self.last_heartbeat = datetime.now()
            
            logger.info("WebSocket connected successfully")
            
            # Start message handling tasks
            asyncio.create_task(self._message_handler())
            asyncio.create_task(self._heartbeat_monitor())
            
            return True
            
        except Exception as e:
            logger.error(f"WebSocket connection failed: {e}")
            self.is_connected = False
            return False
    
    async def disconnect(self):
        """Disconnect from WebSocket server."""
        if self.websocket:
            await self.websocket.close()
            self.is_connected = False
            logger.info("WebSocket disconnected")
    
    async def send_message(self, message: Dict[str, Any]):
        """
        Send message to WebSocket server.
        
        Args:
            message: Message to send
        """
        if not self.is_connected or not self.websocket:
            logger.warning("Cannot send message: WebSocket not connected")
            return
        
        try:
            await self.websocket.send(json.dumps(message))
            logger.debug(f"Sent message: {message}")
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            await self._handle_connection_error()
    
    async def subscribe(self, channels: List[str], symbols: List[str]):
        """
        Subscribe to data channels for specific symbols.
        
        Args:
            channels: List of channels to subscribe to
            symbols: List of symbols to subscribe to
        """
        for channel in channels:
            for symbol in symbols:
                subscription = f"{channel}:{symbol}"
                self.subscriptions.add(subscription)
        
        # Send subscription message (format depends on provider)
        subscribe_message = {
            'action': 'subscribe',
            'channels': channels,
            'symbols': symbols
        }
        
        await self.send_message(subscribe_message)
        logger.info(f"Subscribed to {len(channels)} channels for {len(symbols)} symbols")
    
    async def unsubscribe(self, channels: List[str], symbols: List[str]):
        """
        Unsubscribe from data channels.
        
        Args:
            channels: List of channels to unsubscribe from
            symbols: List of symbols to unsubscribe from
        """
        for channel in channels:
            for symbol in symbols:
                subscription = f"{channel}:{symbol}"
                self.subscriptions.discard(subscription)
        
        unsubscribe_message = {
            'action': 'unsubscribe',
            'channels': channels,
            'symbols': symbols
        }
        
        await self.send_message(unsubscribe_message)
        logger.info(f"Unsubscribed from {len(channels)} channels for {len(symbols)} symbols")
    
    def add_message_handler(self, message_type: str, handler: Callable):
        """
        Add message handler for specific message type.
        
        Args:
            message_type: Type of message to handle
            handler: Handler function
        """
        if message_type not in self.message_handlers:
            self.message_handlers[message_type] = []
        self.message_handlers[message_type].append(handler)
    
    async def _message_handler(self):
        """Handle incoming WebSocket messages."""
        while self.is_connected:
            try:
                if not self.websocket:
                    break
                
                # Receive message with timeout
                message = await asyncio.wait_for(
                    self.websocket.recv(),
                    timeout=self.heartbeat_interval + 10
                )
                
                # Parse JSON message
                try:
                    data = json.loads(message)
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON message: {message}")
                    continue
                
                # Update heartbeat
                self.last_heartbeat = datetime.now()
                
                # Add to message queue
                await self.message_queue.put(data)
                
                # Handle message based on type
                message_type = data.get('type', 'unknown')
                if message_type in self.message_handlers:
                    for handler in self.message_handlers[message_type]:
                        try:
                            await handler(data)
                        except Exception as e:
                            logger.error(f"Message handler error: {e}")
                
            except asyncio.TimeoutError:
                logger.warning("WebSocket message timeout")
                await self._handle_connection_error()
                break
            except websockets.exceptions.ConnectionClosed:
                logger.warning("WebSocket connection closed")
                await self._handle_connection_error()
                break
            except Exception as e:
                logger.error(f"Message handler error: {e}")
                await asyncio.sleep(1)
    
    async def _heartbeat_monitor(self):
        """Monitor connection health with heartbeat."""
        while self.is_connected:
            await asyncio.sleep(self.heartbeat_interval)
            
            if not self.last_heartbeat:
                continue
            
            # Check if heartbeat is stale
            time_since_heartbeat = (datetime.now() - self.last_heartbeat).total_seconds()
            
            if time_since_heartbeat > self.heartbeat_interval * 2:
                logger.warning(f"Heartbeat timeout: {time_since_heartbeat}s")
                await self._handle_connection_error()
                break
    
    async def _handle_connection_error(self):
        """Handle connection errors with automatic reconnection."""
        self.is_connected = False
        
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            logger.error("Maximum reconnection attempts reached")
            return
        
        self.reconnect_attempts += 1
        logger.info(f"Attempting reconnection {self.reconnect_attempts}/{self.max_reconnect_attempts}")
        
        await asyncio.sleep(self.reconnect_interval)
        
        if await self.connect():
            # Re-subscribe to previous subscriptions
            if self.subscriptions:
                channels = []
                symbols = []
                
                for subscription in self.subscriptions:
                    channel, symbol = subscription.split(':', 1)
                    if channel not in channels:
                        channels.append(channel)
                    if symbol not in symbols:
                        symbols.append(symbol)
                
                await self.subscribe(channels, symbols)
    
    async def get_messages(self, timeout: Optional[float] = None) -> List[Dict[str, Any]]:
        """
        Get messages from the queue.
        
        Args:
            timeout: Timeout for waiting for messages
            
        Returns:
            List of messages
        """
        messages = []
        
        try:
            # Get first message (blocking)
            message = await asyncio.wait_for(self.message_queue.get(), timeout=timeout)
            messages.append(message)
            
            # Get any additional messages (non-blocking)
            while not self.message_queue.empty():
                try:
                    message = self.message_queue.get_nowait()
                    messages.append(message)
                except asyncio.QueueEmpty:
                    break
                    
        except asyncio.TimeoutError:
            pass
        
        return messages
    
    def get_connection_status(self) -> Dict[str, Any]:
        """
        Get connection status information.
        
        Returns:
            Dictionary with connection status
        """
        return {
            'is_connected': self.is_connected,
            'reconnect_attempts': self.reconnect_attempts,
            'subscriptions': list(self.subscriptions),
            'last_heartbeat': self.last_heartbeat,
            'queue_size': self.message_queue.qsize()
        }


class AlphaVantageWebSocket(WebSocketClient):
    """WebSocket client for Alpha Vantage real-time data."""
    
    def __init__(self, api_key: str):
        super().__init__(
            url="wss://ws.twelvedata.com/v1/quotes/price",
            api_key=api_key
        )
    
    async def subscribe_quotes(self, symbols: List[str]):
        """Subscribe to real-time quotes."""
        await self.subscribe(['price'], symbols)


class FinnhubWebSocket(WebSocketClient):
    """WebSocket client for Finnhub real-time data."""
    
    def __init__(self, api_key: str):
        super().__init__(
            url=f"wss://ws.finnhub.io?token={api_key}",
            api_key=api_key
        )
    
    async def subscribe_trades(self, symbols: List[str]):
        """Subscribe to real-time trades."""
        for symbol in symbols:
            await self.send_message({'type': 'subscribe', 'symbol': symbol})


class PolygonWebSocket(WebSocketClient):
    """WebSocket client for Polygon real-time data."""
    
    def __init__(self, api_key: str):
        super().__init__(
            url="wss://socket.polygon.io/stocks",
            api_key=api_key
        )
    
    async def authenticate(self):
        """Authenticate with Polygon WebSocket."""
        auth_message = {
            'action': 'auth',
            'params': self.api_key
        }
        await self.send_message(auth_message)
    
    async def subscribe_trades(self, symbols: List[str]):
        """Subscribe to real-time trades."""
        await self.subscribe(['T'], symbols)
    
    async def subscribe_quotes(self, symbols: List[str]):
        """Subscribe to real-time quotes."""
        await self.subscribe(['Q'], symbols)
