"""Unified market data feed with multiple provider support."""

import asyncio
import logging
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime
import json

from .websocket_client import WebSocketClient, FinnhubWebSocket, PolygonWebSocket
from .data_buffer import DataBuffer
from .stream_processor import StreamProcessor

logger = logging.getLogger(__name__)


class MarketDataFeed:
    """
    Unified market data feed supporting multiple providers.
    
    Aggregates real-time data from multiple sources, normalizes formats,
    and provides a unified interface for consuming market data.
    """
    
    def __init__(
        self,
        providers: Dict[str, Dict[str, Any]],
        buffer_size: int = 10000,
        enable_processing: bool = True
    ):
        """
        Initialize market data feed.
        
        Args:
            providers: Dictionary of provider configurations
            buffer_size: Size of data buffer
            enable_processing: Whether to enable stream processing
        """
        self.providers = providers
        self.buffer_size = buffer_size
        self.enable_processing = enable_processing
        
        # Components
        self.clients = {}  # provider -> WebSocketClient
        self.data_buffer = DataBuffer(max_size=buffer_size)
        self.stream_processor = StreamProcessor() if enable_processing else None
        
        # State
        self.is_running = False
        self.subscribed_symbols = set()
        self.subscribed_channels = set()
        
        # Callbacks
        self.data_callbacks = []
        self.connection_callbacks = []
        
        # Statistics
        self.message_counts = {}
        self.error_counts = {}
        
    async def initialize(self):
        """Initialize all provider connections."""
        logger.info("Initializing market data feed")
        
        for provider_name, config in self.providers.items():
            try:
                client = self._create_client(provider_name, config)
                if client:
                    self.clients[provider_name] = client
                    
                    # Add message handlers
                    client.add_message_handler('trade', self._handle_trade_message)
                    client.add_message_handler('quote', self._handle_quote_message)
                    client.add_message_handler('error', self._handle_error_message)
                    
                    self.message_counts[provider_name] = 0
                    self.error_counts[provider_name] = 0
                    
                    logger.info(f"Initialized {provider_name} client")
                
            except Exception as e:
                logger.error(f"Failed to initialize {provider_name}: {e}")
        
        # Setup data buffer callbacks
        if self.stream_processor:
            self.data_buffer.add_data_callback(self._on_new_data)
            self.data_buffer.add_feature_callback(self._on_new_features)
        
        logger.info(f"Market data feed initialized with {len(self.clients)} providers")
    
    def _create_client(self, provider_name: str, config: Dict[str, Any]) -> Optional[WebSocketClient]:
        """Create WebSocket client for provider."""
        api_key = config.get('api_key')
        
        if not api_key:
            logger.warning(f"No API key provided for {provider_name}")
            return None
        
        if provider_name.lower() == 'finnhub':
            return FinnhubWebSocket(api_key)
        elif provider_name.lower() == 'polygon':
            return PolygonWebSocket(api_key)
        else:
            # Generic WebSocket client
            url = config.get('url')
            if url:
                return WebSocketClient(url, api_key)
        
        return None
    
    async def start(self):
        """Start the market data feed."""
        if self.is_running:
            logger.warning("Market data feed already running")
            return
        
        logger.info("Starting market data feed")
        self.is_running = True
        
        # Connect all clients
        connection_tasks = []
        for provider_name, client in self.clients.items():
            task = asyncio.create_task(self._connect_client(provider_name, client))
            connection_tasks.append(task)
        
        # Wait for connections
        await asyncio.gather(*connection_tasks, return_exceptions=True)
        
        # Start stream processor if enabled
        if self.stream_processor:
            asyncio.create_task(self.stream_processor.start_processing())
        
        logger.info("Market data feed started")
    
    async def stop(self):
        """Stop the market data feed."""
        if not self.is_running:
            return
        
        logger.info("Stopping market data feed")
        self.is_running = False
        
        # Disconnect all clients
        disconnect_tasks = []
        for client in self.clients.values():
            task = asyncio.create_task(client.disconnect())
            disconnect_tasks.append(task)
        
        await asyncio.gather(*disconnect_tasks, return_exceptions=True)
        
        # Stop stream processor
        if self.stream_processor:
            await self.stream_processor.stop_processing()
        
        logger.info("Market data feed stopped")
    
    async def _connect_client(self, provider_name: str, client: WebSocketClient):
        """Connect individual client."""
        try:
            success = await client.connect()
            if success:
                logger.info(f"Connected to {provider_name}")
                
                # Trigger connection callback
                for callback in self.connection_callbacks:
                    try:
                        await callback(provider_name, 'connected')
                    except Exception as e:
                        logger.error(f"Connection callback error: {e}")
            else:
                logger.error(f"Failed to connect to {provider_name}")
                
        except Exception as e:
            logger.error(f"Connection error for {provider_name}: {e}")
    
    async def subscribe(
        self,
        symbols: List[str],
        channels: List[str] = None,
        providers: List[str] = None
    ):
        """
        Subscribe to market data.
        
        Args:
            symbols: List of symbols to subscribe to
            channels: List of channels (trades, quotes, etc.)
            providers: List of providers to use (if None, use all)
        """
        if channels is None:
            channels = ['trades', 'quotes']
        
        if providers is None:
            providers = list(self.clients.keys())
        
        logger.info(f"Subscribing to {symbols} on {channels} via {providers}")
        
        # Update tracking
        self.subscribed_symbols.update(symbols)
        self.subscribed_channels.update(channels)
        
        # Subscribe on each provider
        for provider_name in providers:
            if provider_name in self.clients:
                client = self.clients[provider_name]
                try:
                    if hasattr(client, 'subscribe_trades') and 'trades' in channels:
                        await client.subscribe_trades(symbols)
                    if hasattr(client, 'subscribe_quotes') and 'quotes' in channels:
                        await client.subscribe_quotes(symbols)
                    else:
                        # Generic subscription
                        await client.subscribe(channels, symbols)
                        
                except Exception as e:
                    logger.error(f"Subscription error for {provider_name}: {e}")
    
    async def unsubscribe(
        self,
        symbols: List[str],
        channels: List[str] = None,
        providers: List[str] = None
    ):
        """
        Unsubscribe from market data.
        
        Args:
            symbols: List of symbols to unsubscribe from
            channels: List of channels
            providers: List of providers
        """
        if channels is None:
            channels = ['trades', 'quotes']
        
        if providers is None:
            providers = list(self.clients.keys())
        
        logger.info(f"Unsubscribing from {symbols} on {channels}")
        
        # Update tracking
        for symbol in symbols:
            self.subscribed_symbols.discard(symbol)
        
        # Unsubscribe from each provider
        for provider_name in providers:
            if provider_name in self.clients:
                client = self.clients[provider_name]
                try:
                    await client.unsubscribe(channels, symbols)
                except Exception as e:
                    logger.error(f"Unsubscription error for {provider_name}: {e}")
    
    async def _handle_trade_message(self, message: Dict[str, Any]):
        """Handle trade message from any provider."""
        try:
            # Normalize message format
            normalized = self._normalize_trade_message(message)
            
            if normalized:
                symbol = normalized['symbol']
                timestamp = normalized['timestamp']
                price = normalized['price']
                volume = normalized['volume']
                
                # Add to data buffer
                self.data_buffer.add_trade_data(
                    symbol=symbol,
                    timestamp=timestamp,
                    price=price,
                    volume=volume,
                    side=normalized.get('side')
                )
                
                # Update statistics
                provider = normalized.get('provider', 'unknown')
                self.message_counts[provider] = self.message_counts.get(provider, 0) + 1
                
                # Trigger callbacks
                for callback in self.data_callbacks:
                    try:
                        await callback('trade', normalized)
                    except Exception as e:
                        logger.error(f"Data callback error: {e}")
                        
        except Exception as e:
            logger.error(f"Trade message handling error: {e}")
    
    async def _handle_quote_message(self, message: Dict[str, Any]):
        """Handle quote message from any provider."""
        try:
            # Normalize message format
            normalized = self._normalize_quote_message(message)
            
            if normalized:
                symbol = normalized['symbol']
                timestamp = normalized['timestamp']
                bid_price = normalized['bid_price']
                ask_price = normalized['ask_price']
                
                # Add to data buffer
                self.data_buffer.add_quote_data(
                    symbol=symbol,
                    timestamp=timestamp,
                    bid_price=bid_price,
                    ask_price=ask_price,
                    bid_size=normalized.get('bid_size'),
                    ask_size=normalized.get('ask_size')
                )
                
                # Update statistics
                provider = normalized.get('provider', 'unknown')
                self.message_counts[provider] = self.message_counts.get(provider, 0) + 1
                
                # Trigger callbacks
                for callback in self.data_callbacks:
                    try:
                        await callback('quote', normalized)
                    except Exception as e:
                        logger.error(f"Data callback error: {e}")
                        
        except Exception as e:
            logger.error(f"Quote message handling error: {e}")
    
    async def _handle_error_message(self, message: Dict[str, Any]):
        """Handle error message from any provider."""
        provider = message.get('provider', 'unknown')
        self.error_counts[provider] = self.error_counts.get(provider, 0) + 1
        logger.error(f"Provider error from {provider}: {message}")
    
    def _normalize_trade_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Normalize trade message format across providers."""
        try:
            # This would contain provider-specific normalization logic
            # For now, assume a generic format
            return {
                'symbol': message.get('symbol', message.get('s')),
                'timestamp': datetime.fromtimestamp(message.get('timestamp', message.get('t', 0)) / 1000),
                'price': float(message.get('price', message.get('p', 0))),
                'volume': float(message.get('volume', message.get('v', 0))),
                'side': message.get('side'),
                'provider': message.get('provider', 'unknown')
            }
        except Exception as e:
            logger.error(f"Trade message normalization error: {e}")
            return None
    
    def _normalize_quote_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Normalize quote message format across providers."""
        try:
            return {
                'symbol': message.get('symbol', message.get('s')),
                'timestamp': datetime.fromtimestamp(message.get('timestamp', message.get('t', 0)) / 1000),
                'bid_price': float(message.get('bid_price', message.get('bp', 0))),
                'ask_price': float(message.get('ask_price', message.get('ap', 0))),
                'bid_size': message.get('bid_size', message.get('bs')),
                'ask_size': message.get('ask_size', message.get('as')),
                'provider': message.get('provider', 'unknown')
            }
        except Exception as e:
            logger.error(f"Quote message normalization error: {e}")
            return None
    
    async def _on_new_data(self, symbol: str, data_point: Dict[str, Any]):
        """Handle new data from buffer."""
        if self.stream_processor:
            await self.stream_processor.process_data_point(symbol, data_point)
    
    async def _on_new_features(self, symbol: str, features: Dict[str, float]):
        """Handle new features from buffer."""
        if self.stream_processor:
            await self.stream_processor.process_data_point(symbol, {}, features)
    
    def add_data_callback(self, callback: Callable):
        """Add callback for new data."""
        self.data_callbacks.append(callback)
    
    def add_connection_callback(self, callback: Callable):
        """Add callback for connection events."""
        self.connection_callbacks.append(callback)
    
    def get_status(self) -> Dict[str, Any]:
        """Get feed status."""
        return {
            'is_running': self.is_running,
            'providers': list(self.clients.keys()),
            'subscribed_symbols': list(self.subscribed_symbols),
            'subscribed_channels': list(self.subscribed_channels),
            'message_counts': self.message_counts.copy(),
            'error_counts': self.error_counts.copy(),
            'buffer_status': self.data_buffer.get_buffer_status(),
            'processor_stats': self.stream_processor.get_processing_stats() if self.stream_processor else None
        }
