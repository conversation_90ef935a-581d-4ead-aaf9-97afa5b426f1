"""Real-time data buffer with automatic feature calculation."""

import logging
from typing import Dict, Any, Optional, List, Callable
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from collections import deque
import threading

logger = logging.getLogger(__name__)


class DataBuffer:
    """
    Real-time data buffer with automatic feature calculation.
    
    Maintains sliding windows of market data and calculates
    technical indicators in real-time as new data arrives.
    """
    
    def __init__(
        self,
        max_size: int = 10000,
        feature_window: int = 100,
        update_frequency: int = 1
    ):
        """
        Initialize data buffer.
        
        Args:
            max_size: Maximum number of data points to store
            feature_window: Window size for feature calculation
            update_frequency: Update features every N data points
        """
        self.max_size = max_size
        self.feature_window = feature_window
        self.update_frequency = update_frequency
        
        # Data storage
        self.data_buffers = {}  # symbol -> deque of data points
        self.feature_buffers = {}  # symbol -> latest features
        self.update_counters = {}  # symbol -> update counter
        
        # Feature calculators
        self.feature_calculators = []
        
        # Thread safety
        self.lock = threading.RLock()
        
        # Callbacks
        self.data_callbacks = []
        self.feature_callbacks = []
        
    def add_data_point(
        self,
        symbol: str,
        timestamp: datetime,
        data: Dict[str, float]
    ):
        """
        Add new data point to buffer.
        
        Args:
            symbol: Symbol identifier
            timestamp: Data timestamp
            data: Data dictionary (price, volume, etc.)
        """
        with self.lock:
            # Initialize buffer if needed
            if symbol not in self.data_buffers:
                self.data_buffers[symbol] = deque(maxlen=self.max_size)
                self.feature_buffers[symbol] = {}
                self.update_counters[symbol] = 0
            
            # Add timestamp to data
            data_point = {**data, 'timestamp': timestamp}
            
            # Add to buffer
            self.data_buffers[symbol].append(data_point)
            
            # Update counter
            self.update_counters[symbol] += 1
            
            # Trigger callbacks
            for callback in self.data_callbacks:
                try:
                    callback(symbol, data_point)
                except Exception as e:
                    logger.error(f"Data callback error: {e}")
            
            # Update features if needed
            if self.update_counters[symbol] % self.update_frequency == 0:
                self._update_features(symbol)
    
    def add_trade_data(
        self,
        symbol: str,
        timestamp: datetime,
        price: float,
        volume: float,
        side: Optional[str] = None
    ):
        """
        Add trade data point.
        
        Args:
            symbol: Symbol identifier
            timestamp: Trade timestamp
            price: Trade price
            volume: Trade volume
            side: Trade side ('buy', 'sell', or None)
        """
        data = {
            'price': price,
            'volume': volume,
            'type': 'trade'
        }
        
        if side:
            data['side'] = side
        
        self.add_data_point(symbol, timestamp, data)
    
    def add_quote_data(
        self,
        symbol: str,
        timestamp: datetime,
        bid_price: float,
        ask_price: float,
        bid_size: Optional[float] = None,
        ask_size: Optional[float] = None
    ):
        """
        Add quote data point.
        
        Args:
            symbol: Symbol identifier
            timestamp: Quote timestamp
            bid_price: Bid price
            ask_price: Ask price
            bid_size: Bid size (optional)
            ask_size: Ask size (optional)
        """
        data = {
            'bid_price': bid_price,
            'ask_price': ask_price,
            'mid_price': (bid_price + ask_price) / 2,
            'spread': ask_price - bid_price,
            'type': 'quote'
        }
        
        if bid_size is not None:
            data['bid_size'] = bid_size
        if ask_size is not None:
            data['ask_size'] = ask_size
        
        self.add_data_point(symbol, timestamp, data)
    
    def get_recent_data(
        self,
        symbol: str,
        n_points: Optional[int] = None,
        time_window: Optional[timedelta] = None
    ) -> pd.DataFrame:
        """
        Get recent data points for a symbol.
        
        Args:
            symbol: Symbol identifier
            n_points: Number of recent points to return
            time_window: Time window to return data for
            
        Returns:
            DataFrame with recent data
        """
        with self.lock:
            if symbol not in self.data_buffers:
                return pd.DataFrame()
            
            buffer = self.data_buffers[symbol]
            
            if not buffer:
                return pd.DataFrame()
            
            # Convert to list for easier manipulation
            data_list = list(buffer)
            
            # Filter by time window if specified
            if time_window:
                cutoff_time = datetime.now() - time_window
                data_list = [d for d in data_list if d['timestamp'] >= cutoff_time]
            
            # Limit by number of points if specified
            if n_points and len(data_list) > n_points:
                data_list = data_list[-n_points:]
            
            if not data_list:
                return pd.DataFrame()
            
            # Convert to DataFrame
            df = pd.DataFrame(data_list)
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)
            
            return df
    
    def get_latest_features(self, symbol: str) -> Dict[str, float]:
        """
        Get latest calculated features for a symbol.
        
        Args:
            symbol: Symbol identifier
            
        Returns:
            Dictionary with latest features
        """
        with self.lock:
            return self.feature_buffers.get(symbol, {}).copy()
    
    def add_feature_calculator(self, calculator: Callable):
        """
        Add feature calculator function.
        
        Args:
            calculator: Function that takes DataFrame and returns features dict
        """
        self.feature_calculators.append(calculator)
    
    def add_data_callback(self, callback: Callable):
        """
        Add callback for new data points.
        
        Args:
            callback: Function called with (symbol, data_point)
        """
        self.data_callbacks.append(callback)
    
    def add_feature_callback(self, callback: Callable):
        """
        Add callback for feature updates.
        
        Args:
            callback: Function called with (symbol, features)
        """
        self.feature_callbacks.append(callback)
    
    def _update_features(self, symbol: str):
        """Update features for a symbol."""
        try:
            # Get recent data for feature calculation
            df = self.get_recent_data(symbol, n_points=self.feature_window)
            
            if len(df) < 10:  # Need minimum data for features
                return
            
            # Calculate features using all calculators
            features = {}
            
            for calculator in self.feature_calculators:
                try:
                    calc_features = calculator(df)
                    if isinstance(calc_features, dict):
                        features.update(calc_features)
                except Exception as e:
                    logger.error(f"Feature calculator error: {e}")
            
            # Add basic features if no calculators provided
            if not self.feature_calculators and 'price' in df.columns:
                features.update(self._calculate_basic_features(df))
            
            # Store features
            self.feature_buffers[symbol] = features
            
            # Trigger callbacks
            for callback in self.feature_callbacks:
                try:
                    callback(symbol, features)
                except Exception as e:
                    logger.error(f"Feature callback error: {e}")
                    
        except Exception as e:
            logger.error(f"Feature update error for {symbol}: {e}")
    
    def _calculate_basic_features(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate basic technical features."""
        features = {}
        
        try:
            if 'price' in df.columns:
                prices = df['price']
                
                # Price-based features
                features['price_latest'] = float(prices.iloc[-1])
                features['price_change'] = float(prices.iloc[-1] - prices.iloc[-2]) if len(prices) > 1 else 0
                features['price_change_pct'] = float(features['price_change'] / prices.iloc[-2]) if len(prices) > 1 and prices.iloc[-2] != 0 else 0
                
                # Moving averages
                if len(prices) >= 5:
                    features['sma_5'] = float(prices.tail(5).mean())
                if len(prices) >= 10:
                    features['sma_10'] = float(prices.tail(10).mean())
                if len(prices) >= 20:
                    features['sma_20'] = float(prices.tail(20).mean())
                
                # Volatility
                if len(prices) >= 10:
                    returns = prices.pct_change().dropna()
                    features['volatility'] = float(returns.std())
                
            # Volume features
            if 'volume' in df.columns:
                volumes = df['volume']
                features['volume_latest'] = float(volumes.iloc[-1])
                
                if len(volumes) >= 10:
                    features['volume_avg'] = float(volumes.tail(10).mean())
                    features['volume_ratio'] = float(volumes.iloc[-1] / features['volume_avg']) if features['volume_avg'] > 0 else 1
            
            # Spread features
            if 'spread' in df.columns:
                spreads = df['spread']
                features['spread_latest'] = float(spreads.iloc[-1])
                
                if len(spreads) >= 10:
                    features['spread_avg'] = float(spreads.tail(10).mean())
                    
        except Exception as e:
            logger.error(f"Basic feature calculation error: {e}")
        
        return features
    
    def get_buffer_status(self) -> Dict[str, Any]:
        """
        Get buffer status information.
        
        Returns:
            Dictionary with buffer status
        """
        with self.lock:
            status = {
                'symbols': list(self.data_buffers.keys()),
                'buffer_sizes': {symbol: len(buffer) for symbol, buffer in self.data_buffers.items()},
                'feature_counts': {symbol: len(features) for symbol, features in self.feature_buffers.items()},
                'update_counters': self.update_counters.copy(),
                'total_data_points': sum(len(buffer) for buffer in self.data_buffers.values())
            }
        
        return status
