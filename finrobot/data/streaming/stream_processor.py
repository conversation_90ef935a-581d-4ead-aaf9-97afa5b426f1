"""Real-time stream processing with ML inference."""

import asyncio
import logging
from typing import Dict, Any, Optional, List, Callable
import pandas as pd
import numpy as np
from datetime import datetime
import threading

logger = logging.getLogger(__name__)


class StreamProcessor:
    """
    Real-time stream processor with ML model inference.
    
    Processes incoming market data streams, calculates features,
    and generates predictions using trained ML models.
    """
    
    def __init__(
        self,
        prediction_interval: int = 60,  # seconds
        min_data_points: int = 50,
        max_processing_delay: float = 1.0  # seconds
    ):
        """
        Initialize stream processor.
        
        Args:
            prediction_interval: Interval between predictions (seconds)
            min_data_points: Minimum data points needed for prediction
            max_processing_delay: Maximum allowed processing delay
        """
        self.prediction_interval = prediction_interval
        self.min_data_points = min_data_points
        self.max_processing_delay = max_processing_delay
        
        # Models and processors
        self.models = {}  # symbol -> model
        self.feature_processors = {}  # symbol -> feature processor
        self.scalers = {}  # symbol -> scaler
        
        # Processing state
        self.last_predictions = {}  # symbol -> last prediction time
        self.processing_queue = asyncio.Queue()
        self.is_running = False
        
        # Callbacks
        self.prediction_callbacks = []
        self.alert_callbacks = []
        
        # Performance tracking
        self.processing_times = []
        self.prediction_counts = {}
        
        # Thread safety
        self.lock = threading.RLock()
        
    def add_model(
        self,
        symbol: str,
        model: Any,
        feature_processor: Optional[Any] = None,
        scaler: Optional[Any] = None
    ):
        """
        Add ML model for a symbol.
        
        Args:
            symbol: Symbol identifier
            model: Trained ML model
            feature_processor: Feature processor/pipeline
            scaler: Feature scaler
        """
        with self.lock:
            self.models[symbol] = model
            if feature_processor:
                self.feature_processors[symbol] = feature_processor
            if scaler:
                self.scalers[symbol] = scaler
            
            self.last_predictions[symbol] = datetime.min
            self.prediction_counts[symbol] = 0
        
        logger.info(f"Added model for {symbol}")
    
    def remove_model(self, symbol: str):
        """
        Remove model for a symbol.
        
        Args:
            symbol: Symbol identifier
        """
        with self.lock:
            self.models.pop(symbol, None)
            self.feature_processors.pop(symbol, None)
            self.scalers.pop(symbol, None)
            self.last_predictions.pop(symbol, None)
            self.prediction_counts.pop(symbol, None)
        
        logger.info(f"Removed model for {symbol}")
    
    async def process_data_point(
        self,
        symbol: str,
        data_point: Dict[str, Any],
        features: Optional[Dict[str, float]] = None
    ):
        """
        Process new data point.
        
        Args:
            symbol: Symbol identifier
            data_point: New data point
            features: Pre-calculated features (optional)
        """
        # Add to processing queue
        await self.processing_queue.put({
            'symbol': symbol,
            'data_point': data_point,
            'features': features,
            'timestamp': datetime.now()
        })
    
    async def start_processing(self):
        """Start the stream processing loop."""
        self.is_running = True
        logger.info("Starting stream processor")
        
        # Start processing tasks
        tasks = [
            asyncio.create_task(self._processing_loop()),
            asyncio.create_task(self._prediction_scheduler())
        ]
        
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"Stream processing error: {e}")
        finally:
            self.is_running = False
    
    async def stop_processing(self):
        """Stop the stream processing."""
        self.is_running = False
        logger.info("Stopping stream processor")
    
    async def _processing_loop(self):
        """Main processing loop."""
        while self.is_running:
            try:
                # Get data from queue with timeout
                item = await asyncio.wait_for(
                    self.processing_queue.get(),
                    timeout=1.0
                )
                
                # Check processing delay
                processing_delay = (datetime.now() - item['timestamp']).total_seconds()
                if processing_delay > self.max_processing_delay:
                    logger.warning(f"High processing delay: {processing_delay:.2f}s")
                
                # Process the item
                await self._process_item(item)
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"Processing loop error: {e}")
                await asyncio.sleep(0.1)
    
    async def _prediction_scheduler(self):
        """Schedule periodic predictions."""
        while self.is_running:
            try:
                current_time = datetime.now()
                
                # Check each symbol for prediction timing
                for symbol in list(self.models.keys()):
                    last_pred_time = self.last_predictions.get(symbol, datetime.min)
                    time_since_last = (current_time - last_pred_time).total_seconds()
                    
                    if time_since_last >= self.prediction_interval:
                        await self._generate_prediction(symbol)
                
                # Sleep until next check
                await asyncio.sleep(min(10, self.prediction_interval / 4))
                
            except Exception as e:
                logger.error(f"Prediction scheduler error: {e}")
                await asyncio.sleep(1)
    
    async def _process_item(self, item: Dict[str, Any]):
        """Process individual data item."""
        start_time = datetime.now()
        
        try:
            symbol = item['symbol']
            data_point = item['data_point']
            features = item.get('features', {})
            
            # Check for alerts/anomalies
            await self._check_alerts(symbol, data_point, features)
            
            # Update processing statistics
            processing_time = (datetime.now() - start_time).total_seconds()
            self.processing_times.append(processing_time)
            
            # Keep only recent processing times
            if len(self.processing_times) > 1000:
                self.processing_times = self.processing_times[-500:]
                
        except Exception as e:
            logger.error(f"Item processing error: {e}")
    
    async def _generate_prediction(self, symbol: str):
        """Generate prediction for a symbol."""
        try:
            if symbol not in self.models:
                return
            
            model = self.models[symbol]
            
            # This would typically get recent features from the data buffer
            # For now, we'll simulate with dummy features
            features = self._get_latest_features(symbol)
            
            if not features or len(features) < self.min_data_points:
                logger.debug(f"Insufficient features for {symbol} prediction")
                return
            
            # Prepare features for prediction
            X = self._prepare_features_for_prediction(symbol, features)
            
            if X is None:
                return
            
            # Generate prediction
            prediction = model.predict(X)
            
            # Calculate confidence/uncertainty if available
            confidence = self._calculate_prediction_confidence(model, X)
            
            # Create prediction result
            prediction_result = {
                'symbol': symbol,
                'timestamp': datetime.now(),
                'prediction': float(prediction[0]) if hasattr(prediction, '__getitem__') else float(prediction),
                'confidence': confidence,
                'model_type': type(model).__name__,
                'features_used': len(features) if isinstance(features, dict) else len(features.columns) if hasattr(features, 'columns') else 0
            }
            
            # Update tracking
            self.last_predictions[symbol] = datetime.now()
            self.prediction_counts[symbol] = self.prediction_counts.get(symbol, 0) + 1
            
            # Trigger callbacks
            for callback in self.prediction_callbacks:
                try:
                    await callback(prediction_result)
                except Exception as e:
                    logger.error(f"Prediction callback error: {e}")
            
            logger.debug(f"Generated prediction for {symbol}: {prediction_result['prediction']:.4f}")
            
        except Exception as e:
            logger.error(f"Prediction generation error for {symbol}: {e}")
    
    def _get_latest_features(self, symbol: str) -> Optional[Dict[str, float]]:
        """Get latest features for a symbol (placeholder)."""
        # This would typically interface with the DataBuffer
        # For now, return dummy features
        return {
            'price_change': np.random.normal(0, 0.01),
            'volume_ratio': np.random.uniform(0.5, 2.0),
            'volatility': np.random.uniform(0.01, 0.05),
            'rsi': np.random.uniform(20, 80),
            'macd': np.random.normal(0, 0.001)
        }
    
    def _prepare_features_for_prediction(
        self,
        symbol: str,
        features: Dict[str, float]
    ) -> Optional[np.ndarray]:
        """Prepare features for model prediction."""
        try:
            # Convert features to array
            if isinstance(features, dict):
                feature_values = list(features.values())
            else:
                feature_values = features
            
            X = np.array(feature_values).reshape(1, -1)
            
            # Apply feature processor if available
            if symbol in self.feature_processors:
                processor = self.feature_processors[symbol]
                X = processor.transform(X)
            
            # Apply scaler if available
            if symbol in self.scalers:
                scaler = self.scalers[symbol]
                X = scaler.transform(X)
            
            return X
            
        except Exception as e:
            logger.error(f"Feature preparation error for {symbol}: {e}")
            return None
    
    def _calculate_prediction_confidence(
        self,
        model: Any,
        X: np.ndarray
    ) -> float:
        """Calculate prediction confidence."""
        try:
            # Try to get prediction probabilities or uncertainty
            if hasattr(model, 'predict_proba'):
                proba = model.predict_proba(X)
                # Use max probability as confidence
                confidence = float(np.max(proba))
            elif hasattr(model, 'decision_function'):
                decision = model.decision_function(X)
                # Convert decision function to confidence
                confidence = float(1 / (1 + np.exp(-np.abs(decision))))
            else:
                # Default confidence
                confidence = 0.5
            
            return confidence
            
        except Exception as e:
            logger.debug(f"Confidence calculation error: {e}")
            return 0.5
    
    async def _check_alerts(
        self,
        symbol: str,
        data_point: Dict[str, Any],
        features: Dict[str, float]
    ):
        """Check for alerts and anomalies."""
        try:
            alerts = []
            
            # Price movement alerts
            if 'price_change_pct' in features:
                price_change = abs(features['price_change_pct'])
                if price_change > 0.05:  # 5% change
                    alerts.append({
                        'type': 'large_price_movement',
                        'severity': 'high' if price_change > 0.1 else 'medium',
                        'value': price_change,
                        'message': f"Large price movement: {price_change:.2%}"
                    })
            
            # Volume alerts
            if 'volume_ratio' in features:
                volume_ratio = features['volume_ratio']
                if volume_ratio > 3.0:  # 3x average volume
                    alerts.append({
                        'type': 'high_volume',
                        'severity': 'medium',
                        'value': volume_ratio,
                        'message': f"High volume: {volume_ratio:.1f}x average"
                    })
            
            # Trigger alert callbacks
            if alerts:
                alert_data = {
                    'symbol': symbol,
                    'timestamp': datetime.now(),
                    'alerts': alerts,
                    'data_point': data_point
                }
                
                for callback in self.alert_callbacks:
                    try:
                        await callback(alert_data)
                    except Exception as e:
                        logger.error(f"Alert callback error: {e}")
                        
        except Exception as e:
            logger.error(f"Alert checking error: {e}")
    
    def add_prediction_callback(self, callback: Callable):
        """Add callback for predictions."""
        self.prediction_callbacks.append(callback)
    
    def add_alert_callback(self, callback: Callable):
        """Add callback for alerts."""
        self.alert_callbacks.append(callback)
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics."""
        stats = {
            'is_running': self.is_running,
            'models_loaded': len(self.models),
            'symbols': list(self.models.keys()),
            'prediction_counts': self.prediction_counts.copy(),
            'queue_size': self.processing_queue.qsize()
        }
        
        if self.processing_times:
            stats['processing_times'] = {
                'avg': np.mean(self.processing_times),
                'max': np.max(self.processing_times),
                'min': np.min(self.processing_times),
                'count': len(self.processing_times)
            }
        
        return stats
