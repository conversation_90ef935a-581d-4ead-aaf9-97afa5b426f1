"""Settings management for FinRobot-Pro using Pydantic."""

import os
from typing import Optional

import os
from typing import Optional

try:
    from pydantic import BaseSettings, Field
except ImportError:
    # Fallback for newer pydantic versions
    from pydantic_settings import BaseSettings
    from pydantic import Field


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # API Keys
    alpha_vantage_api_key: Optional[str] = Field(None, env="ALPHA_VANTAGE_API_KEY")
    finnhub_api_key: Optional[str] = Field(None, env="FINNHUB_API_KEY")
    polygon_api_key: Optional[str] = Field(None, env="POLYGON_API_KEY")
    tiingo_api_key: Optional[str] = Field(None, env="TIINGO_API_KEY")
    fmp_api_key: Optional[str] = Field(None, env="FMP_API_KEY")
    sec_api_key: Optional[str] = Field(None, env="SEC_API_KEY")
    
    # Social Media APIs
    reddit_client_id: Optional[str] = Field(None, env="REDDIT_CLIENT_ID")
    reddit_client_secret: Optional[str] = Field(None, env="REDDIT_CLIENT_SECRET")
    twitter_bearer_token: Optional[str] = Field(None, env="TWITTER_BEARER_TOKEN")
    
    # OpenAI API
    openai_api_key: Optional[str] = Field(None, env="OPENAI_API_KEY")
    
    # MLflow Configuration
    mlflow_tracking_uri: str = Field("./mlruns", env="MLFLOW_TRACKING_URI")
    mlflow_experiment_name: str = Field("finrobot_forecasting", env="MLFLOW_EXPERIMENT_NAME")
    
    # Database
    database_url: str = Field("sqlite:///./finrobot.db", env="DATABASE_URL")
    
    # Logging
    log_level: str = Field("INFO", env="LOG_LEVEL")
    log_file: str = Field("./logs/finrobot.log", env="LOG_FILE")
    
    # Cache
    cache_dir: str = Field("./cache", env="CACHE_DIR")
    cache_ttl_hours: int = Field(6, env="CACHE_TTL_HOURS")
    
    # Environment
    environment: str = Field("development", env="ENVIRONMENT")
    debug: bool = Field(False, env="DEBUG")
    
    # Security
    secret_key: str = Field("change-me-in-production", env="SECRET_KEY")
    
    # Prefect
    prefect_api_url: str = Field("http://localhost:4200/api", env="PREFECT_API_URL")
    prefect_logging_level: str = Field("INFO", env="PREFECT_LOGGING_LEVEL")
    
    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        extra = "ignore"  # Ignore extra fields from .env
    
    def get_api_key(self, provider: str) -> Optional[str]:
        """Get API key for a specific provider."""
        key_mapping = {
            "alpha_vantage": self.alpha_vantage_api_key,
            "finnhub": self.finnhub_api_key,
            "polygon": self.polygon_api_key,
            "tiingo": self.tiingo_api_key,
            "fmp": self.fmp_api_key,
            "sec": self.sec_api_key,
            "reddit": self.reddit_client_id,
            "twitter": self.twitter_bearer_token,
            "openai": self.openai_api_key,
        }
        return key_mapping.get(provider.lower())
    
    def validate_api_keys(self) -> dict[str, bool]:
        """Validate that required API keys are present."""
        required_keys = {
            "alpha_vantage": self.alpha_vantage_api_key is not None,
            "finnhub": self.finnhub_api_key is not None,
            "polygon": self.polygon_api_key is not None,
            "tiingo": self.tiingo_api_key is not None,
        }
        return required_keys
    
    @property
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment.lower() == "production"
    
    @property
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.environment.lower() == "development"
