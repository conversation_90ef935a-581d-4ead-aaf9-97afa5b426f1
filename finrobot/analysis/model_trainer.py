"""Model training and validation for FinRobot-Pro."""

import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

from ..data.loaders.csv_loader import CSVLoader
from ..data.processors.time_series_processor import TimeSeriesProcessor
from ..ml.features.feature_pipeline import FeaturePipeline
from ..ml.models.model_factory import ModelFactory
from ..ml.models.base_model import BaseModel
from .walk_forward_validator import WalkForwardValidator

logger = logging.getLogger(__name__)


class ModelTrainer:
    """Comprehensive model training and validation system."""
    
    def __init__(self, data_directory: str = "."):
        """Initialize model trainer.
        
        Args:
            data_directory: Directory containing CSV files
        """
        self.data_directory = Path(data_directory)
        self.csv_loader = CSVLoader(data_directory)
        self.processor = TimeSeriesProcessor()
        self.feature_pipeline = FeaturePipeline()
        self.walk_forward_validator = WalkForwardValidator()
        
        # Training configuration
        self.train_ratio = 0.8
        self.val_ratio = 0.1
        self.test_ratio = 0.1
        
        # Results storage
        self.training_results = {}
        self.model_registry = {}
        
        logger.info("ModelTrainer initialized")
    
    def train_models_on_symbol(self, symbol: str, 
                              model_configs: List[Dict],
                              target_horizons: List[int] = [1, 5, 10],
                              use_walk_forward: bool = True) -> Dict:
        """Train multiple models on a single symbol.
        
        Args:
            symbol: Stock symbol to train on
            model_configs: List of model configurations
            target_horizons: Prediction horizons in days
            use_walk_forward: Whether to use walk-forward validation
            
        Returns:
            Dictionary with training results
        """
        logger.info(f"Training models on {symbol}")
        
        try:
            # Load and prepare data
            raw_data = self.csv_loader.get_ohlcv(symbol)
            cleaned_data = self.processor.clean_ohlcv_data(raw_data, symbol)
            
            # Build features
            features = self.feature_pipeline.build_features(cleaned_data, symbol)
            
            results = {}
            
            for horizon in target_horizons:
                logger.info(f"Training for {horizon}-day horizon")
                
                # Prepare target
                target_col = f'target_{horizon}d'
                if target_col not in features.columns:
                    features[target_col] = features['Close'].pct_change(horizon).shift(-horizon)
                
                # Prepare features for ML
                X, y = self.feature_pipeline.prepare_features_for_ml(
                    features, target_column=target_col
                )
                
                if len(X) == 0:
                    logger.warning(f"No valid data for {symbol} at {horizon}-day horizon")
                    continue
                
                horizon_results = {}
                
                for model_config in model_configs:
                    model_name = model_config.get('name', 'unknown')
                    logger.info(f"Training {model_name} for {horizon}-day horizon")
                    
                    try:
                        if use_walk_forward:
                            # Walk-forward validation
                            wf_results = self.walk_forward_validator.validate_model(
                                X, y, model_config, n_splits=5
                            )
                            horizon_results[model_name] = wf_results
                        else:
                            # Simple train/validation/test split
                            train_results = self._train_single_model(
                                X, y, model_config, symbol, horizon
                            )
                            horizon_results[model_name] = train_results
                            
                    except Exception as e:
                        logger.error(f"Error training {model_name}: {e}")
                        horizon_results[model_name] = {'error': str(e)}
                
                results[f'{horizon}d'] = horizon_results
            
            self.training_results[symbol] = results
            return results
            
        except Exception as e:
            logger.error(f"Error training models on {symbol}: {e}")
            return {'error': str(e)}
    
    def _train_single_model(self, X: pd.DataFrame, y: pd.Series,
                           model_config: Dict, symbol: str, horizon: int) -> Dict:
        """Train a single model with train/val/test split.
        
        Args:
            X: Feature matrix
            y: Target vector
            model_config: Model configuration
            symbol: Symbol name
            horizon: Prediction horizon
            
        Returns:
            Training results dictionary
        """
        # Temporal split (no shuffling for time series)
        n_samples = len(X)
        train_end = int(n_samples * self.train_ratio)
        val_end = int(n_samples * (self.train_ratio + self.val_ratio))
        
        X_train = X.iloc[:train_end]
        y_train = y.iloc[:train_end]
        X_val = X.iloc[train_end:val_end]
        y_val = y.iloc[train_end:val_end]
        X_test = X.iloc[val_end:]
        y_test = y.iloc[val_end:]
        
        # Create and train model
        model = ModelFactory.create_model(model_config['name'], **{k: v for k, v in model_config.items() if k != 'name'})
        
        # Train with validation data
        validation_data = (X_val, y_val) if len(X_val) > 0 else None
        model.fit(X_train, y_train, validation_data=validation_data)
        
        # Make predictions
        train_pred = model.predict(X_train, return_confidence=False).predictions
        val_pred = model.predict(X_val, return_confidence=False).predictions if len(X_val) > 0 else []
        test_pred = model.predict(X_test, return_confidence=False).predictions if len(X_test) > 0 else []
        
        # Calculate metrics
        results = {
            'model_info': model.get_model_info(),
            'data_splits': {
                'train_size': len(X_train),
                'val_size': len(X_val),
                'test_size': len(X_test)
            },
            'metrics': {}
        }
        
        # Training metrics
        if len(train_pred) > 0:
            results['metrics']['train'] = self._calculate_metrics(y_train, train_pred)
        
        # Validation metrics
        if len(val_pred) > 0:
            results['metrics']['val'] = self._calculate_metrics(y_val, val_pred)
        
        # Test metrics
        if len(test_pred) > 0:
            results['metrics']['test'] = self._calculate_metrics(y_test, test_pred)
        
        # Feature importance
        feature_importance = model.get_feature_importance()
        if feature_importance is not None:
            results['feature_importance'] = feature_importance.head(20).to_dict()
        
        # Store model
        model_key = f"{symbol}_{horizon}d_{model_config.get('name', 'model')}"
        self.model_registry[model_key] = model
        
        # Overfitting detection
        results['overfitting_analysis'] = self._detect_overfitting(results['metrics'])
        
        return results
    
    def _calculate_metrics(self, y_true: pd.Series, y_pred: np.ndarray) -> Dict:
        """Calculate comprehensive metrics.
        
        Args:
            y_true: True values
            y_pred: Predicted values
            
        Returns:
            Dictionary of metrics
        """
        metrics = {}
        
        try:
            # Basic regression metrics
            metrics['rmse'] = np.sqrt(mean_squared_error(y_true, y_pred))
            metrics['mae'] = mean_absolute_error(y_true, y_pred)
            metrics['r2'] = r2_score(y_true, y_pred)
            
            # Financial-specific metrics
            # Directional accuracy
            direction_true = np.sign(y_true)
            direction_pred = np.sign(y_pred)
            metrics['directional_accuracy'] = np.mean(direction_true == direction_pred)
            
            # Hit rate (percentage of correct direction predictions)
            metrics['hit_rate'] = metrics['directional_accuracy']
            
            # Mean absolute percentage error
            non_zero_mask = y_true != 0
            if np.any(non_zero_mask):
                mape = np.mean(np.abs((y_true[non_zero_mask] - y_pred[non_zero_mask]) / y_true[non_zero_mask]))
                metrics['mape'] = mape
            
            # Information coefficient (correlation between predictions and actual)
            if len(y_true) > 1 and np.std(y_pred) > 0:
                ic = np.corrcoef(y_true, y_pred)[0, 1]
                metrics['information_coefficient'] = ic
            
            # Sharpe-like ratio for predictions
            if np.std(y_pred) > 0:
                pred_sharpe = np.mean(y_pred) / np.std(y_pred)
                metrics['prediction_sharpe'] = pred_sharpe
            
            # Maximum error
            metrics['max_error'] = np.max(np.abs(y_true - y_pred))
            
        except Exception as e:
            logger.warning(f"Error calculating metrics: {e}")
            metrics['error'] = str(e)
        
        return metrics
    
    def _detect_overfitting(self, metrics: Dict) -> Dict:
        """Detect potential overfitting.
        
        Args:
            metrics: Dictionary of metrics for train/val/test
            
        Returns:
            Overfitting analysis results
        """
        analysis = {
            'is_overfitted': False,
            'warnings': [],
            'severity': 'low'
        }
        
        try:
            train_metrics = metrics.get('train', {})
            val_metrics = metrics.get('val', {})
            test_metrics = metrics.get('test', {})
            
            # Check train vs validation performance
            if train_metrics and val_metrics:
                train_rmse = train_metrics.get('rmse', 0)
                val_rmse = val_metrics.get('rmse', 0)
                
                if train_rmse > 0:
                    rmse_ratio = val_rmse / train_rmse
                    
                    if rmse_ratio > 1.5:
                        analysis['is_overfitted'] = True
                        analysis['warnings'].append(f"Validation RMSE {rmse_ratio:.2f}x higher than training")
                        analysis['severity'] = 'high'
                    elif rmse_ratio > 1.2:
                        analysis['warnings'].append(f"Validation RMSE {rmse_ratio:.2f}x higher than training")
                        analysis['severity'] = 'medium'
                
                # Check directional accuracy
                train_da = train_metrics.get('directional_accuracy', 0)
                val_da = val_metrics.get('directional_accuracy', 0)
                
                if train_da - val_da > 0.1:
                    analysis['warnings'].append(f"Directional accuracy drops {train_da - val_da:.3f} from train to val")
            
            # Check if directional accuracy is too low
            for split, split_metrics in metrics.items():
                da = split_metrics.get('directional_accuracy', 0)
                if da < 0.52:
                    analysis['warnings'].append(f"{split} directional accuracy too low: {da:.3f}")
            
        except Exception as e:
            logger.warning(f"Error in overfitting detection: {e}")
        
        return analysis
    
    def train_ensemble_models(self, symbols: List[str],
                            model_configs: List[Dict],
                            target_horizons: List[int] = [1, 5, 10]) -> Dict:
        """Train models on multiple symbols.
        
        Args:
            symbols: List of symbols to train on
            model_configs: List of model configurations
            target_horizons: Prediction horizons in days
            
        Returns:
            Dictionary with training results for all symbols
        """
        logger.info(f"Training ensemble models on {len(symbols)} symbols")
        
        all_results = {}
        
        for symbol in symbols:
            logger.info(f"Processing symbol {symbol}")
            try:
                symbol_results = self.train_models_on_symbol(
                    symbol, model_configs, target_horizons
                )
                all_results[symbol] = symbol_results
            except Exception as e:
                logger.error(f"Error training on {symbol}: {e}")
                all_results[symbol] = {'error': str(e)}
        
        # Generate ensemble summary
        ensemble_summary = self._generate_ensemble_summary(all_results)
        all_results['_ensemble_summary'] = ensemble_summary
        
        return all_results
    
    def _generate_ensemble_summary(self, results: Dict) -> Dict:
        """Generate summary of ensemble training results.
        
        Args:
            results: Training results for all symbols
            
        Returns:
            Ensemble summary
        """
        summary = {
            'total_symbols': len([k for k in results.keys() if not k.startswith('_')]),
            'successful_symbols': 0,
            'failed_symbols': 0,
            'model_performance': {},
            'best_models': {},
            'overfitting_analysis': {}
        }
        
        # Collect metrics across all symbols
        all_metrics = {}
        overfitting_counts = {}
        
        for symbol, symbol_results in results.items():
            if symbol.startswith('_') or 'error' in symbol_results:
                if 'error' in symbol_results:
                    summary['failed_symbols'] += 1
                continue
            
            summary['successful_symbols'] += 1
            
            for horizon, horizon_results in symbol_results.items():
                if horizon not in all_metrics:
                    all_metrics[horizon] = {}
                
                for model_name, model_results in horizon_results.items():
                    if 'error' in model_results:
                        continue
                    
                    if model_name not in all_metrics[horizon]:
                        all_metrics[horizon][model_name] = []
                    
                    # Collect test metrics
                    test_metrics = model_results.get('metrics', {}).get('test', {})
                    if test_metrics:
                        all_metrics[horizon][model_name].append(test_metrics)
                    
                    # Count overfitting
                    overfitting = model_results.get('overfitting_analysis', {})
                    if overfitting.get('is_overfitted', False):
                        key = f"{horizon}_{model_name}"
                        overfitting_counts[key] = overfitting_counts.get(key, 0) + 1
        
        # Calculate average performance
        for horizon, horizon_metrics in all_metrics.items():
            summary['model_performance'][horizon] = {}
            
            for model_name, metrics_list in horizon_metrics.items():
                if not metrics_list:
                    continue
                
                avg_metrics = {}
                for metric_name in ['rmse', 'mae', 'r2', 'directional_accuracy']:
                    values = [m.get(metric_name) for m in metrics_list if m.get(metric_name) is not None]
                    if values:
                        avg_metrics[f'avg_{metric_name}'] = np.mean(values)
                        avg_metrics[f'std_{metric_name}'] = np.std(values)
                
                summary['model_performance'][horizon][model_name] = avg_metrics
        
        # Find best models
        for horizon in all_metrics.keys():
            best_model = None
            best_score = -np.inf
            
            for model_name, avg_metrics in summary['model_performance'][horizon].items():
                # Use directional accuracy as primary metric
                score = avg_metrics.get('avg_directional_accuracy', 0)
                if score > best_score:
                    best_score = score
                    best_model = model_name
            
            summary['best_models'][horizon] = {
                'model': best_model,
                'directional_accuracy': best_score
            }
        
        # Overfitting summary
        summary['overfitting_analysis'] = {
            'overfitted_combinations': overfitting_counts,
            'total_overfitted': sum(overfitting_counts.values()),
            'overfitting_rate': sum(overfitting_counts.values()) / max(1, summary['successful_symbols'] * len(all_metrics))
        }
        
        return summary
    
    def save_training_results(self, filepath: Optional[str] = None) -> str:
        """Save training results to file.
        
        Args:
            filepath: Optional filepath to save results
            
        Returns:
            Path where results were saved
        """
        if filepath is None:
            output_dir = Path("training_results")
            output_dir.mkdir(exist_ok=True)
            filepath = output_dir / "model_training_results.json"
        
        try:
            import json
            
            # Convert results to JSON-serializable format
            json_results = self._convert_for_json(self.training_results)
            
            with open(filepath, 'w') as f:
                json.dump(json_results, f, indent=2, default=str)
            
            logger.info(f"Training results saved to {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"Error saving training results: {e}")
            raise
    
    def _convert_for_json(self, obj):
        """Convert numpy types to JSON-serializable types."""
        if isinstance(obj, dict):
            return {k: self._convert_for_json(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_for_json(v) for v in obj]
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif pd.isna(obj):
            return None
        else:
            return obj
    
    def get_best_model(self, symbol: str, horizon: int) -> Optional[BaseModel]:
        """Get the best trained model for a symbol and horizon.
        
        Args:
            symbol: Stock symbol
            horizon: Prediction horizon
            
        Returns:
            Best model instance or None
        """
        if symbol not in self.training_results:
            return None
        
        horizon_key = f"{horizon}d"
        if horizon_key not in self.training_results[symbol]:
            return None
        
        # Find model with best directional accuracy
        best_model_name = None
        best_score = -1
        
        for model_name, results in self.training_results[symbol][horizon_key].items():
            if 'error' in results:
                continue
            
            test_metrics = results.get('metrics', {}).get('test', {})
            da = test_metrics.get('directional_accuracy', 0)
            
            if da > best_score:
                best_score = da
                best_model_name = model_name
        
        if best_model_name:
            model_key = f"{symbol}_{horizon}d_{best_model_name}"
            return self.model_registry.get(model_key)
        
        return None
