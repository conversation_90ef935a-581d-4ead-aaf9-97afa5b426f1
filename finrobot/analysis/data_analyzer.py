"""Comprehensive data analysis for FinRobot-Pro."""

import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats

from ..data.loaders.csv_loader import CSVLoader
from ..data.processors.time_series_processor import TimeSeriesProcessor
from ..data.validators.time_series_validator import TimeSeriesValidator
from ..ml.features.feature_pipeline import FeaturePipeline

logger = logging.getLogger(__name__)


class DataAnalyzer:
    """Comprehensive data analyzer for financial time series."""
    
    def __init__(self, data_directory: str = "."):
        """Initialize data analyzer.
        
        Args:
            data_directory: Directory containing CSV files
        """
        self.data_directory = Path(data_directory)
        self.csv_loader = CSVLoader(data_directory)
        self.processor = TimeSeriesProcessor()
        self.validator = TimeSeriesValidator()
        self.feature_pipeline = FeaturePipeline()
        
        # Analysis results storage
        self.analysis_results = {}
        self.validation_results = {}
        self.feature_analysis = {}
        
        logger.info(f"DataAnalyzer initialized with {len(self.csv_loader.get_available_symbols())} symbols")
    
    def analyze_all_data(self, save_results: bool = True) -> Dict[str, Dict]:
        """Perform comprehensive analysis on all available data.
        
        Args:
            save_results: Whether to save analysis results
            
        Returns:
            Dictionary with analysis results for all symbols
        """
        logger.info("Starting comprehensive data analysis")
        
        symbols = self.csv_loader.get_available_symbols()
        results = {}
        
        for symbol in symbols:
            logger.info(f"Analyzing {symbol}")
            try:
                # Load and clean data
                raw_data = self.csv_loader.get_ohlcv(symbol)
                cleaned_data = self.processor.clean_ohlcv_data(raw_data, symbol)
                
                # Validate data
                validation_result = self.validator.validate_ohlcv_data(cleaned_data, symbol)
                
                # Perform analysis
                analysis = self._analyze_single_symbol(cleaned_data, symbol)
                
                results[symbol] = {
                    'data_summary': self._get_data_summary(cleaned_data),
                    'validation': validation_result,
                    'analysis': analysis,
                    'quality_score': self._calculate_quality_score(validation_result, analysis)
                }
                
                logger.info(f"Completed analysis for {symbol}")
                
            except Exception as e:
                logger.error(f"Error analyzing {symbol}: {e}")
                results[symbol] = {'error': str(e)}
        
        self.analysis_results = results
        
        if save_results:
            self._save_analysis_results(results)
        
        # Generate summary report
        self._generate_summary_report(results)
        
        return results
    
    def _analyze_single_symbol(self, data: pd.DataFrame, symbol: str) -> Dict:
        """Analyze a single symbol's data.
        
        Args:
            data: OHLCV DataFrame
            symbol: Symbol name
            
        Returns:
            Dictionary with analysis results
        """
        analysis = {}
        
        try:
            # Basic statistics
            analysis['basic_stats'] = self._calculate_basic_statistics(data)
            
            # Return analysis
            analysis['returns'] = self._analyze_returns(data)
            
            # Volatility analysis
            analysis['volatility'] = self._analyze_volatility(data)
            
            # Trend analysis
            analysis['trend'] = self._analyze_trend(data)
            
            # Volume analysis
            analysis['volume'] = self._analyze_volume(data)
            
            # Correlation analysis
            analysis['correlations'] = self._analyze_correlations(data)
            
            # Seasonality analysis
            analysis['seasonality'] = self._analyze_seasonality(data)
            
            # Risk metrics
            analysis['risk_metrics'] = self._calculate_risk_metrics(data)
            
        except Exception as e:
            logger.error(f"Error in single symbol analysis for {symbol}: {e}")
            analysis['error'] = str(e)
        
        return analysis
    
    def _calculate_basic_statistics(self, data: pd.DataFrame) -> Dict:
        """Calculate basic statistics for the data."""
        stats = {}
        
        if 'Close' in data.columns:
            close_prices = data['Close']
            stats['price_stats'] = {
                'mean': close_prices.mean(),
                'median': close_prices.median(),
                'std': close_prices.std(),
                'min': close_prices.min(),
                'max': close_prices.max(),
                'range': close_prices.max() - close_prices.min(),
                'cv': close_prices.std() / close_prices.mean() if close_prices.mean() != 0 else 0
            }
        
        if 'Volume' in data.columns:
            volume = data['Volume']
            stats['volume_stats'] = {
                'mean': volume.mean(),
                'median': volume.median(),
                'std': volume.std(),
                'min': volume.min(),
                'max': volume.max()
            }
        
        stats['data_info'] = {
            'total_records': len(data),
            'date_range': (data.index.min(), data.index.max()),
            'trading_days': len(data),
            'missing_values': data.isnull().sum().to_dict()
        }
        
        return stats
    
    def _analyze_returns(self, data: pd.DataFrame) -> Dict:
        """Analyze return characteristics."""
        if 'Close' not in data.columns:
            return {}
        
        returns = data['Close'].pct_change().dropna()
        
        return {
            'daily_return_stats': {
                'mean': returns.mean(),
                'std': returns.std(),
                'skewness': stats.skew(returns),
                'kurtosis': stats.kurtosis(returns),
                'min': returns.min(),
                'max': returns.max()
            },
            'annualized_metrics': {
                'return': returns.mean() * 252,
                'volatility': returns.std() * np.sqrt(252),
                'sharpe_ratio': (returns.mean() * 252) / (returns.std() * np.sqrt(252)) if returns.std() != 0 else 0
            },
            'return_distribution': {
                'positive_days': (returns > 0).sum(),
                'negative_days': (returns < 0).sum(),
                'zero_days': (returns == 0).sum(),
                'positive_ratio': (returns > 0).mean()
            }
        }
    
    def _analyze_volatility(self, data: pd.DataFrame) -> Dict:
        """Analyze volatility patterns."""
        if 'Close' not in data.columns:
            return {}
        
        returns = data['Close'].pct_change().dropna()
        
        # Rolling volatilities
        vol_5d = returns.rolling(5).std() * np.sqrt(252)
        vol_20d = returns.rolling(20).std() * np.sqrt(252)
        vol_60d = returns.rolling(60).std() * np.sqrt(252)
        
        return {
            'volatility_levels': {
                'current_5d': vol_5d.iloc[-1] if len(vol_5d) > 0 else None,
                'current_20d': vol_20d.iloc[-1] if len(vol_20d) > 0 else None,
                'current_60d': vol_60d.iloc[-1] if len(vol_60d) > 0 else None,
                'mean_20d': vol_20d.mean(),
                'std_20d': vol_20d.std()
            },
            'volatility_clustering': {
                'autocorr_1': returns.abs().autocorr(1) if len(returns) > 1 else None,
                'autocorr_5': returns.abs().autocorr(5) if len(returns) > 5 else None
            }
        }
    
    def _analyze_trend(self, data: pd.DataFrame) -> Dict:
        """Analyze trend characteristics."""
        if 'Close' not in data.columns:
            return {}
        
        close_prices = data['Close']
        
        # Moving averages
        ma_20 = close_prices.rolling(20).mean()
        ma_50 = close_prices.rolling(50).mean()
        ma_200 = close_prices.rolling(200).mean()
        
        current_price = close_prices.iloc[-1]
        
        return {
            'moving_averages': {
                'ma_20': ma_20.iloc[-1] if len(ma_20) > 0 else None,
                'ma_50': ma_50.iloc[-1] if len(ma_50) > 0 else None,
                'ma_200': ma_200.iloc[-1] if len(ma_200) > 0 else None,
                'price_vs_ma_20': (current_price / ma_20.iloc[-1] - 1) if len(ma_20) > 0 and ma_20.iloc[-1] != 0 else None,
                'price_vs_ma_50': (current_price / ma_50.iloc[-1] - 1) if len(ma_50) > 0 and ma_50.iloc[-1] != 0 else None
            },
            'trend_strength': {
                'ma_20_slope': self._calculate_slope(ma_20.tail(20)) if len(ma_20) >= 20 else None,
                'ma_50_slope': self._calculate_slope(ma_50.tail(20)) if len(ma_50) >= 20 else None,
                'price_momentum_20d': (current_price / close_prices.iloc[-21] - 1) if len(close_prices) > 21 else None
            }
        }
    
    def _analyze_volume(self, data: pd.DataFrame) -> Dict:
        """Analyze volume patterns."""
        if 'Volume' not in data.columns:
            return {}
        
        volume = data['Volume']
        
        # Volume moving averages
        vol_ma_20 = volume.rolling(20).mean()
        vol_ma_50 = volume.rolling(50).mean()
        
        return {
            'volume_stats': {
                'current_volume': volume.iloc[-1],
                'avg_volume_20d': vol_ma_20.iloc[-1] if len(vol_ma_20) > 0 else None,
                'avg_volume_50d': vol_ma_50.iloc[-1] if len(vol_ma_50) > 0 else None,
                'volume_ratio_20d': volume.iloc[-1] / vol_ma_20.iloc[-1] if len(vol_ma_20) > 0 and vol_ma_20.iloc[-1] != 0 else None
            },
            'volume_patterns': {
                'high_volume_days': (volume > vol_ma_20 * 2).sum() if len(vol_ma_20) > 0 else None,
                'low_volume_days': (volume < vol_ma_20 * 0.5).sum() if len(vol_ma_20) > 0 else None
            }
        }
    
    def _analyze_correlations(self, data: pd.DataFrame) -> Dict:
        """Analyze correlations between OHLCV components."""
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) < 2:
            return {}
        
        corr_matrix = data[numeric_cols].corr()
        
        return {
            'correlation_matrix': corr_matrix.to_dict(),
            'high_correlations': self._find_high_correlations(corr_matrix)
        }
    
    def _analyze_seasonality(self, data: pd.DataFrame) -> Dict:
        """Analyze seasonal patterns."""
        if 'Close' not in data.columns or len(data) < 252:
            return {}
        
        returns = data['Close'].pct_change().dropna()
        
        # Add time features
        data_with_time = data.copy()
        data_with_time['month'] = data_with_time.index.month
        data_with_time['day_of_week'] = data_with_time.index.dayofweek
        data_with_time['returns'] = returns
        
        return {
            'monthly_patterns': data_with_time.groupby('month')['returns'].mean().to_dict(),
            'weekly_patterns': data_with_time.groupby('day_of_week')['returns'].mean().to_dict()
        }
    
    def _calculate_risk_metrics(self, data: pd.DataFrame) -> Dict:
        """Calculate risk metrics."""
        if 'Close' not in data.columns:
            return {}
        
        returns = data['Close'].pct_change().dropna()
        
        # Value at Risk (VaR)
        var_95 = np.percentile(returns, 5)
        var_99 = np.percentile(returns, 1)
        
        # Conditional VaR (Expected Shortfall)
        cvar_95 = returns[returns <= var_95].mean()
        cvar_99 = returns[returns <= var_99].mean()
        
        # Maximum Drawdown
        cumulative_returns = (1 + returns).cumprod()
        rolling_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - rolling_max) / rolling_max
        max_drawdown = drawdown.min()
        
        return {
            'var_95': var_95,
            'var_99': var_99,
            'cvar_95': cvar_95,
            'cvar_99': cvar_99,
            'max_drawdown': max_drawdown,
            'downside_deviation': returns[returns < 0].std() if (returns < 0).any() else 0
        }
    
    def _calculate_slope(self, series: pd.Series) -> float:
        """Calculate slope of a time series."""
        if len(series) < 2:
            return 0
        
        x = np.arange(len(series))
        y = series.values
        
        # Remove NaN values
        mask = ~np.isnan(y)
        if mask.sum() < 2:
            return 0
        
        slope, _ = np.polyfit(x[mask], y[mask], 1)
        return slope
    
    def _find_high_correlations(self, corr_matrix: pd.DataFrame, threshold: float = 0.8) -> List[Tuple[str, str, float]]:
        """Find pairs with high correlation."""
        high_corrs = []
        
        for i in range(len(corr_matrix.columns)):
            for j in range(i + 1, len(corr_matrix.columns)):
                corr_val = corr_matrix.iloc[i, j]
                if abs(corr_val) > threshold:
                    high_corrs.append((
                        corr_matrix.columns[i],
                        corr_matrix.columns[j],
                        corr_val
                    ))
        
        return high_corrs
    
    def _get_data_summary(self, data: pd.DataFrame) -> Dict:
        """Get summary of data characteristics."""
        return {
            'shape': data.shape,
            'columns': list(data.columns),
            'date_range': {
                'start': data.index.min().strftime('%Y-%m-%d') if len(data) > 0 else None,
                'end': data.index.max().strftime('%Y-%m-%d') if len(data) > 0 else None,
                'days': len(data)
            },
            'missing_values': data.isnull().sum().to_dict(),
            'data_types': data.dtypes.to_dict()
        }
    
    def _calculate_quality_score(self, validation_result, analysis: Dict) -> float:
        """Calculate overall data quality score."""
        score = 1.0
        
        # Penalize validation errors
        if hasattr(validation_result, 'errors') and validation_result.errors:
            score -= 0.2 * len(validation_result.errors)
        
        # Penalize high missing data
        if 'basic_stats' in analysis and 'data_info' in analysis['basic_stats']:
            missing_ratio = sum(analysis['basic_stats']['data_info']['missing_values'].values()) / analysis['basic_stats']['data_info']['total_records']
            score -= missing_ratio * 0.3
        
        # Bonus for sufficient data
        if 'basic_stats' in analysis and 'data_info' in analysis['basic_stats']:
            if analysis['basic_stats']['data_info']['total_records'] > 1000:
                score += 0.1
        
        return max(0.0, min(1.0, score))
    
    def _save_analysis_results(self, results: Dict) -> None:
        """Save analysis results to file."""
        try:
            output_dir = Path("analysis_results")
            output_dir.mkdir(exist_ok=True)
            
            # Save detailed results
            import json
            with open(output_dir / "detailed_analysis.json", 'w') as f:
                # Convert numpy types to native Python types for JSON serialization
                json_results = self._convert_for_json(results)
                json.dump(json_results, f, indent=2, default=str)
            
            logger.info(f"Analysis results saved to {output_dir}")
            
        except Exception as e:
            logger.error(f"Error saving analysis results: {e}")
    
    def _convert_for_json(self, obj):
        """Convert numpy types to JSON-serializable types."""
        if isinstance(obj, dict):
            return {k: self._convert_for_json(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_for_json(v) for v in obj]
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif pd.isna(obj):
            return None
        else:
            return obj
    
    def _generate_summary_report(self, results: Dict) -> None:
        """Generate summary report of analysis."""
        logger.info("=== DATA ANALYSIS SUMMARY ===")
        
        total_symbols = len(results)
        valid_symbols = sum(1 for r in results.values() if 'error' not in r)
        
        logger.info(f"Total symbols analyzed: {total_symbols}")
        logger.info(f"Successfully analyzed: {valid_symbols}")
        logger.info(f"Failed analysis: {total_symbols - valid_symbols}")
        
        if valid_symbols > 0:
            # Calculate average quality score
            quality_scores = [r['quality_score'] for r in results.values() if 'quality_score' in r]
            avg_quality = np.mean(quality_scores) if quality_scores else 0
            
            logger.info(f"Average data quality score: {avg_quality:.3f}")
            
            # Find best and worst quality symbols
            symbol_quality = [(symbol, r.get('quality_score', 0)) for symbol, r in results.items() if 'quality_score' in r]
            symbol_quality.sort(key=lambda x: x[1], reverse=True)
            
            if symbol_quality:
                logger.info(f"Highest quality: {symbol_quality[0][0]} ({symbol_quality[0][1]:.3f})")
                logger.info(f"Lowest quality: {symbol_quality[-1][0]} ({symbol_quality[-1][1]:.3f})")
    
    def get_symbols_for_training(self, min_quality_score: float = 0.7,
                               min_records: int = 1000) -> List[str]:
        """Get symbols suitable for model training.
        
        Args:
            min_quality_score: Minimum quality score required
            min_records: Minimum number of records required
            
        Returns:
            List of suitable symbols
        """
        if not self.analysis_results:
            logger.warning("No analysis results available. Run analyze_all_data() first.")
            return []
        
        suitable_symbols = []
        
        for symbol, results in self.analysis_results.items():
            if 'error' in results:
                continue
            
            quality_score = results.get('quality_score', 0)
            
            if 'data_summary' in results:
                n_records = results['data_summary'].get('shape', [0])[0]
            else:
                n_records = 0
            
            if quality_score >= min_quality_score and n_records >= min_records:
                suitable_symbols.append(symbol)
        
        logger.info(f"Found {len(suitable_symbols)} symbols suitable for training")
        return suitable_symbols
