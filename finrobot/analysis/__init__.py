"""Analysis and training modules for FinRobot-Pro."""

from .data_analyzer import DataAnalyzer
from .model_trainer import ModelTrainer
from .walk_forward_validator import WalkForwardValidator

# Import other analyzers if they exist
try:
    from .performance_analyzer import PerformanceAnalyzer
except ImportError:
    PerformanceAnalyzer = None

__all__ = [
    "DataAnalyzer",
    "ModelTrainer",
    "WalkForwardValidator",
]

if PerformanceAnalyzer:
    __all__.append("PerformanceAnalyzer")
