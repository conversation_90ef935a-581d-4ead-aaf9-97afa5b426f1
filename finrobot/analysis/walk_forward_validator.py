"""Walk-forward validation for FinRobot-Pro."""

import logging
from typing import Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

from ..ml.models.model_factory import ModelFactory

logger = logging.getLogger(__name__)


class WalkForwardValidator:
    """Walk-forward validation for time series models."""
    
    def __init__(self, min_train_size: int = 500, step_size: int = 50):
        """Initialize walk-forward validator.
        
        Args:
            min_train_size: Minimum training set size
            step_size: Number of samples to step forward each iteration
        """
        self.min_train_size = min_train_size
        self.step_size = step_size
        
        logger.info("WalkForwardValidator initialized")
    
    def validate_model(self, X: pd.DataFrame, y: pd.Series,
                      model_config: Dict, n_splits: int = 5) -> Dict:
        """Perform walk-forward validation on a model.
        
        Args:
            X: Feature matrix
            y: Target vector
            model_config: Model configuration dictionary
            n_splits: Number of validation splits
            
        Returns:
            Dictionary with validation results
        """
        logger.info(f"Starting walk-forward validation with {n_splits} splits")
        
        if len(X) < self.min_train_size + n_splits * self.step_size:
            raise ValueError(f"Insufficient data for walk-forward validation. Need at least {self.min_train_size + n_splits * self.step_size} samples")
        
        # Generate split points
        splits = self._generate_splits(len(X), n_splits)
        
        results = {
            'splits': [],
            'summary_metrics': {},
            'model_config': model_config,
            'validation_type': 'walk_forward'
        }
        
        all_predictions = []
        all_actuals = []
        
        for i, (train_end, test_start, test_end) in enumerate(splits):
            logger.debug(f"Split {i+1}/{n_splits}: train[0:{train_end}], test[{test_start}:{test_end}]")
            
            try:
                # Split data
                X_train = X.iloc[:train_end]
                y_train = y.iloc[:train_end]
                X_test = X.iloc[test_start:test_end]
                y_test = y.iloc[test_start:test_end]
                
                # Create and train model
                model = ModelFactory.create_model(**model_config)
                model.fit(X_train, y_train)
                
                # Make predictions
                predictions = model.predict(X_test, return_confidence=False).predictions
                
                # Calculate metrics for this split
                split_metrics = self._calculate_metrics(y_test, predictions)
                
                split_result = {
                    'split_id': i + 1,
                    'train_size': len(X_train),
                    'test_size': len(X_test),
                    'train_period': (X_train.index.min(), X_train.index.max()),
                    'test_period': (X_test.index.min(), X_test.index.max()),
                    'metrics': split_metrics,
                    'predictions': predictions.tolist(),
                    'actuals': y_test.values.tolist()
                }
                
                results['splits'].append(split_result)
                
                # Collect for overall metrics
                all_predictions.extend(predictions)
                all_actuals.extend(y_test.values)
                
            except Exception as e:
                logger.error(f"Error in split {i+1}: {e}")
                split_result = {
                    'split_id': i + 1,
                    'error': str(e)
                }
                results['splits'].append(split_result)
        
        # Calculate summary metrics
        if all_predictions and all_actuals:
            results['summary_metrics'] = self._calculate_summary_metrics(results['splits'])
            results['overall_metrics'] = self._calculate_metrics(
                np.array(all_actuals), np.array(all_predictions)
            )
        
        # Stability analysis
        results['stability_analysis'] = self._analyze_stability(results['splits'])
        
        logger.info(f"Walk-forward validation completed. Overall directional accuracy: {results.get('overall_metrics', {}).get('directional_accuracy', 'N/A'):.3f}")
        
        return results
    
    def _generate_splits(self, n_samples: int, n_splits: int) -> List[Tuple[int, int, int]]:
        """Generate walk-forward split indices.
        
        Args:
            n_samples: Total number of samples
            n_splits: Number of splits
            
        Returns:
            List of (train_end, test_start, test_end) tuples
        """
        splits = []
        
        # Calculate test size for each split
        remaining_samples = n_samples - self.min_train_size
        test_size = max(self.step_size, remaining_samples // n_splits)
        
        for i in range(n_splits):
            train_end = self.min_train_size + i * self.step_size
            test_start = train_end
            test_end = min(test_start + test_size, n_samples)
            
            if test_end > test_start:
                splits.append((train_end, test_start, test_end))
        
        return splits
    
    def _calculate_metrics(self, y_true, y_pred) -> Dict:
        """Calculate comprehensive metrics.
        
        Args:
            y_true: True values
            y_pred: Predicted values
            
        Returns:
            Dictionary of metrics
        """
        metrics = {}
        
        try:
            # Convert to numpy arrays
            y_true = np.array(y_true)
            y_pred = np.array(y_pred)
            
            # Basic regression metrics
            metrics['rmse'] = np.sqrt(mean_squared_error(y_true, y_pred))
            metrics['mae'] = mean_absolute_error(y_true, y_pred)
            metrics['r2'] = r2_score(y_true, y_pred)
            
            # Financial-specific metrics
            # Directional accuracy
            direction_true = np.sign(y_true)
            direction_pred = np.sign(y_pred)
            metrics['directional_accuracy'] = np.mean(direction_true == direction_pred)
            
            # Hit rate (same as directional accuracy)
            metrics['hit_rate'] = metrics['directional_accuracy']
            
            # Mean absolute percentage error
            non_zero_mask = y_true != 0
            if np.any(non_zero_mask):
                mape = np.mean(np.abs((y_true[non_zero_mask] - y_pred[non_zero_mask]) / y_true[non_zero_mask]))
                metrics['mape'] = mape
            
            # Information coefficient
            if len(y_true) > 1 and np.std(y_pred) > 0:
                ic = np.corrcoef(y_true, y_pred)[0, 1]
                metrics['information_coefficient'] = ic if not np.isnan(ic) else 0
            
            # Prediction statistics
            metrics['pred_mean'] = np.mean(y_pred)
            metrics['pred_std'] = np.std(y_pred)
            metrics['actual_mean'] = np.mean(y_true)
            metrics['actual_std'] = np.std(y_true)
            
            # Bias
            metrics['bias'] = np.mean(y_pred - y_true)
            
        except Exception as e:
            logger.warning(f"Error calculating metrics: {e}")
            metrics['error'] = str(e)
        
        return metrics
    
    def _calculate_summary_metrics(self, splits: List[Dict]) -> Dict:
        """Calculate summary metrics across all splits.
        
        Args:
            splits: List of split results
            
        Returns:
            Summary metrics dictionary
        """
        summary = {}
        
        # Collect metrics from successful splits
        valid_splits = [s for s in splits if 'error' not in s and 'metrics' in s]
        
        if not valid_splits:
            return {'error': 'No valid splits found'}
        
        # Calculate mean and std for each metric
        metric_names = valid_splits[0]['metrics'].keys()
        
        for metric_name in metric_names:
            values = [s['metrics'][metric_name] for s in valid_splits 
                     if metric_name in s['metrics'] and s['metrics'][metric_name] is not None]
            
            if values:
                summary[f'{metric_name}_mean'] = np.mean(values)
                summary[f'{metric_name}_std'] = np.std(values)
                summary[f'{metric_name}_min'] = np.min(values)
                summary[f'{metric_name}_max'] = np.max(values)
        
        # Additional summary statistics
        summary['n_valid_splits'] = len(valid_splits)
        summary['n_failed_splits'] = len(splits) - len(valid_splits)
        
        return summary
    
    def _analyze_stability(self, splits: List[Dict]) -> Dict:
        """Analyze model stability across splits.
        
        Args:
            splits: List of split results
            
        Returns:
            Stability analysis results
        """
        stability = {
            'is_stable': True,
            'warnings': [],
            'metrics': {}
        }
        
        valid_splits = [s for s in splits if 'error' not in s and 'metrics' in s]
        
        if len(valid_splits) < 2:
            stability['warnings'].append("Insufficient splits for stability analysis")
            return stability
        
        # Analyze directional accuracy stability
        da_values = [s['metrics'].get('directional_accuracy', 0) for s in valid_splits]
        da_std = np.std(da_values)
        da_mean = np.mean(da_values)
        
        stability['metrics']['directional_accuracy'] = {
            'mean': da_mean,
            'std': da_std,
            'cv': da_std / da_mean if da_mean != 0 else np.inf,
            'range': np.max(da_values) - np.min(da_values)
        }
        
        # Check for instability
        if da_std > 0.1:  # High variance in directional accuracy
            stability['is_stable'] = False
            stability['warnings'].append(f"High variance in directional accuracy: {da_std:.3f}")
        
        if np.max(da_values) - np.min(da_values) > 0.2:  # Large range
            stability['is_stable'] = False
            stability['warnings'].append(f"Large range in directional accuracy: {np.max(da_values) - np.min(da_values):.3f}")
        
        # Analyze RMSE stability
        rmse_values = [s['metrics'].get('rmse', 0) for s in valid_splits]
        rmse_std = np.std(rmse_values)
        rmse_mean = np.mean(rmse_values)
        
        stability['metrics']['rmse'] = {
            'mean': rmse_mean,
            'std': rmse_std,
            'cv': rmse_std / rmse_mean if rmse_mean != 0 else np.inf
        }
        
        # Check for degrading performance over time
        if len(da_values) >= 3:
            # Simple trend analysis
            x = np.arange(len(da_values))
            slope, _ = np.polyfit(x, da_values, 1)
            
            if slope < -0.02:  # Declining performance
                stability['warnings'].append(f"Declining performance trend: {slope:.4f}")
        
        return stability
    
    def validate_multiple_models(self, X: pd.DataFrame, y: pd.Series,
                                model_configs: List[Dict], n_splits: int = 5) -> Dict:
        """Validate multiple models using walk-forward validation.
        
        Args:
            X: Feature matrix
            y: Target vector
            model_configs: List of model configurations
            n_splits: Number of validation splits
            
        Returns:
            Dictionary with results for all models
        """
        logger.info(f"Validating {len(model_configs)} models")
        
        results = {}
        
        for model_config in model_configs:
            model_name = model_config.get('name', 'unknown')
            logger.info(f"Validating {model_name}")
            
            try:
                model_results = self.validate_model(X, y, model_config, n_splits)
                results[model_name] = model_results
            except Exception as e:
                logger.error(f"Error validating {model_name}: {e}")
                results[model_name] = {'error': str(e)}
        
        # Compare models
        results['_comparison'] = self._compare_models(results)
        
        return results
    
    def _compare_models(self, results: Dict) -> Dict:
        """Compare validation results across models.
        
        Args:
            results: Dictionary of model results
            
        Returns:
            Model comparison results
        """
        comparison = {
            'ranking': [],
            'best_model': None,
            'summary': {}
        }
        
        model_scores = []
        
        for model_name, model_results in results.items():
            if model_name.startswith('_') or 'error' in model_results:
                continue
            
            # Use directional accuracy as primary ranking metric
            overall_metrics = model_results.get('overall_metrics', {})
            da = overall_metrics.get('directional_accuracy', 0)
            
            # Consider stability
            stability = model_results.get('stability_analysis', {})
            is_stable = stability.get('is_stable', False)
            
            # Penalize unstable models
            score = da * (1.0 if is_stable else 0.8)
            
            model_scores.append({
                'model': model_name,
                'score': score,
                'directional_accuracy': da,
                'is_stable': is_stable,
                'rmse': overall_metrics.get('rmse', np.inf)
            })
        
        # Sort by score
        model_scores.sort(key=lambda x: x['score'], reverse=True)
        comparison['ranking'] = model_scores
        
        if model_scores:
            comparison['best_model'] = model_scores[0]['model']
            
            # Summary statistics
            comparison['summary'] = {
                'best_score': model_scores[0]['score'],
                'score_range': model_scores[0]['score'] - model_scores[-1]['score'],
                'stable_models': sum(1 for m in model_scores if m['is_stable']),
                'total_models': len(model_scores)
            }
        
        return comparison
