"""LightGBM model implementation for FinRobot-Pro."""

import logging
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
import lightgbm as lgb
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

from .base_model import BaseModel, ModelResult

logger = logging.getLogger(__name__)


class LightGBMModel(BaseModel):
    """LightGBM model for financial forecasting."""
    
    def __init__(self, **kwargs):
        """Initialize LightGBM model.
        
        Args:
            **kwargs: LightGBM parameters
        """
        super().__init__("LightGBM", **kwargs)
        
        # Default parameters optimized for financial time series
        default_params = {
            'objective': 'regression',
            'metric': 'rmse',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.9,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'verbose': -1,
            'random_state': self.random_seed,
            'n_estimators': 1000,
            'early_stopping_rounds': 50,
            'reg_alpha': 0.1,
            'reg_lambda': 0.1
        }
        
        # Merge with user parameters
        self.lgb_params = {**default_params, **self.model_config, **kwargs}
        
        # Separate training parameters
        self.n_estimators = self.lgb_params.pop('n_estimators', 1000)
        self.early_stopping_rounds = self.lgb_params.pop('early_stopping_rounds', 50)
        
        logger.info(f"LightGBM initialized with params: {self.lgb_params}")
    
    def fit(self, X: Union[pd.DataFrame, np.ndarray], 
            y: Union[pd.Series, np.ndarray],
            validation_data: Optional[Tuple] = None,
            **kwargs) -> 'LightGBMModel':
        """Fit LightGBM model.
        
        Args:
            X: Feature matrix
            y: Target vector
            validation_data: Optional validation data tuple (X_val, y_val)
            **kwargs: Additional fitting parameters
            
        Returns:
            Self for method chaining
        """
        logger.info("Training LightGBM model")
        
        # Validate input
        X, y = self.validate_input(X, y)
        
        # Start MLflow run
        self._start_mlflow_run()
        
        try:
            # Create LightGBM datasets
            train_data = lgb.Dataset(X, label=y, feature_name=self.feature_names)
            
            valid_sets = [train_data]
            valid_names = ['train']
            
            if validation_data is not None:
                X_val, y_val = validation_data
                X_val, y_val = self.validate_input(X_val, y_val)
                valid_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
                valid_sets.append(valid_data)
                valid_names.append('valid')
            
            # Train model
            self.model = lgb.train(
                self.lgb_params,
                train_data,
                num_boost_round=self.n_estimators,
                valid_sets=valid_sets,
                valid_names=valid_names,
                callbacks=[
                    lgb.early_stopping(self.early_stopping_rounds, verbose=False),
                    lgb.log_evaluation(0)  # Suppress verbose output
                ]
            )
            
            self.is_fitted = True
            
            # Calculate training metrics
            train_pred = self.model.predict(X)
            train_metrics = self._calculate_metrics(y, train_pred, "train")
            
            # Calculate validation metrics if available
            if validation_data is not None:
                val_pred = self.model.predict(X_val)
                val_metrics = self._calculate_metrics(y_val, val_pred, "val")
                train_metrics.update(val_metrics)
            
            # Store training metadata
            self.training_metadata = {
                'n_samples': len(X),
                'n_features': X.shape[1],
                'best_iteration': self.model.best_iteration,
                'feature_importance': self.get_feature_importance().to_dict() if self.feature_names else None,
                **train_metrics
            }
            
            # Log metrics to MLflow
            self._log_metrics(train_metrics)
            
            logger.info(f"LightGBM training completed. Best iteration: {self.model.best_iteration}")
            
        except Exception as e:
            logger.error(f"Error training LightGBM model: {e}")
            raise
        finally:
            self._end_mlflow_run()
        
        return self
    
    def predict(self, X: Union[pd.DataFrame, np.ndarray],
                return_confidence: bool = True,
                **kwargs) -> ModelResult:
        """Make predictions with LightGBM.
        
        Args:
            X: Feature matrix
            return_confidence: Whether to return confidence intervals
            **kwargs: Additional prediction parameters
            
        Returns:
            ModelResult with predictions and metadata
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before making predictions")
        
        # Validate input
        X, _ = self.validate_input(X)
        
        # Make predictions
        predictions = self.model.predict(X)
        
        # Calculate confidence intervals if requested
        confidence_intervals = None
        prediction_intervals = None
        confidence_scores = None
        
        if return_confidence:
            # Use training residuals for prediction intervals
            if hasattr(self, 'training_residuals'):
                prediction_intervals = self.calculate_prediction_intervals(
                    predictions, self.training_residuals
                )
            
            # Calculate confidence scores based on prediction consistency
            confidence_scores = self._calculate_confidence_scores(X, predictions)
        
        return ModelResult(
            predictions=predictions,
            confidence_intervals=confidence_intervals,
            prediction_intervals=prediction_intervals,
            confidence_scores=confidence_scores,
            metadata={
                'model_name': self.model_name,
                'n_predictions': len(predictions),
                'feature_importance': self.get_feature_importance().to_dict() if self.feature_names else None
            }
        )
    
    def get_feature_importance(self) -> Optional[pd.DataFrame]:
        """Get feature importance from LightGBM model.
        
        Returns:
            DataFrame with feature importance
        """
        if not self.is_fitted or not self.feature_names:
            return None
        
        importance = self.model.feature_importance(importance_type='gain')
        
        importance_df = pd.DataFrame({
            'feature': self.feature_names,
            'importance': importance
        }).sort_values('importance', ascending=False)
        
        return importance_df
    
    def _calculate_metrics(self, y_true: np.ndarray, y_pred: np.ndarray, 
                          prefix: str = "") -> Dict[str, float]:
        """Calculate regression metrics.
        
        Args:
            y_true: True values
            y_pred: Predicted values
            prefix: Prefix for metric names
            
        Returns:
            Dictionary of metrics
        """
        metrics = {}
        prefix = f"{prefix}_" if prefix else ""
        
        try:
            metrics[f'{prefix}rmse'] = np.sqrt(mean_squared_error(y_true, y_pred))
            metrics[f'{prefix}mae'] = mean_absolute_error(y_true, y_pred)
            metrics[f'{prefix}r2'] = r2_score(y_true, y_pred)
            
            # Financial-specific metrics
            # Directional accuracy
            direction_true = np.sign(y_true)
            direction_pred = np.sign(y_pred)
            metrics[f'{prefix}directional_accuracy'] = np.mean(direction_true == direction_pred)
            
            # Mean absolute percentage error (handle division by zero)
            non_zero_mask = y_true != 0
            if np.any(non_zero_mask):
                mape = np.mean(np.abs((y_true[non_zero_mask] - y_pred[non_zero_mask]) / y_true[non_zero_mask]))
                metrics[f'{prefix}mape'] = mape
            
        except Exception as e:
            logger.warning(f"Error calculating metrics: {e}")
        
        return metrics
    
    def _calculate_confidence_scores(self, X: np.ndarray, predictions: np.ndarray) -> np.ndarray:
        """Calculate confidence scores for predictions.
        
        Args:
            X: Feature matrix
            predictions: Model predictions
            
        Returns:
            Array of confidence scores
        """
        try:
            # Use prediction variance from tree ensemble
            # This is a simplified approach - could be enhanced with more sophisticated methods
            
            # Get predictions from individual trees (if available)
            if hasattr(self.model, 'predict') and hasattr(self.model, 'num_trees'):
                # Calculate prediction variance across trees
                tree_predictions = []
                for i in range(min(10, self.model.num_trees())):  # Sample first 10 trees
                    try:
                        tree_pred = self.model.predict(X, start_iteration=i, num_iteration=1)
                        tree_predictions.append(tree_pred)
                    except:
                        break
                
                if tree_predictions:
                    tree_predictions = np.array(tree_predictions)
                    prediction_std = np.std(tree_predictions, axis=0)
                    
                    # Convert to confidence scores (higher std = lower confidence)
                    max_std = np.max(prediction_std) if len(prediction_std) > 0 else 1.0
                    confidence_scores = 1.0 - (prediction_std / max_std)
                    return np.clip(confidence_scores, 0.1, 1.0)
            
            # Fallback: uniform confidence
            return np.full(len(predictions), 0.7)
            
        except Exception as e:
            logger.warning(f"Error calculating confidence scores: {e}")
            return np.full(len(predictions), 0.5)
    
    def get_model_params(self) -> Dict[str, Any]:
        """Get model parameters.
        
        Returns:
            Dictionary of model parameters
        """
        return {
            **self.lgb_params,
            'n_estimators': self.n_estimators,
            'early_stopping_rounds': self.early_stopping_rounds
        }
