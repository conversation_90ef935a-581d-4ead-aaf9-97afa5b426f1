"""Model factory for FinRobot-Pro."""

import logging
from typing import Any, Dict, List, Optional, Type, Union

from .base_model import BaseModel
from .lightgbm_model import LightGBMModel
from .xgboost_model import XGBoostModel
from .lstm_model import LSTMModel

logger = logging.getLogger(__name__)


class ModelFactory:
    """Factory for creating and managing ML models."""
    
    # Registry of available models
    _model_registry = {
        'lightgbm': LightGBMModel,
        'lgb': LightGBMModel,
        'xgboost': XGBoostModel,
        'xgb': XGBoostModel,
        'lstm': LSTMModel,
    }
    
    @classmethod
    def create_model(cls, model_name: str, **kwargs) -> BaseModel:
        """Create a model instance.
        
        Args:
            model_name: Name of the model to create
            **kwargs: Model-specific parameters
            
        Returns:
            Model instance
            
        Raises:
            ValueError: If model name is not recognized
        """
        model_name_lower = model_name.lower()
        
        if model_name_lower not in cls._model_registry:
            available_models = list(cls._model_registry.keys())
            raise ValueError(f"Unknown model '{model_name}'. Available models: {available_models}")
        
        model_class = cls._model_registry[model_name_lower]
        logger.info(f"Creating {model_class.__name__} model")
        
        return model_class(**kwargs)
    
    @classmethod
    def register_model(cls, name: str, model_class: Type[BaseModel]) -> None:
        """Register a new model class.
        
        Args:
            name: Name to register the model under
            model_class: Model class to register
        """
        if not issubclass(model_class, BaseModel):
            raise ValueError("Model class must inherit from BaseModel")
        
        cls._model_registry[name.lower()] = model_class
        logger.info(f"Registered model '{name}' -> {model_class.__name__}")
    
    @classmethod
    def get_available_models(cls) -> List[str]:
        """Get list of available model names.
        
        Returns:
            List of available model names
        """
        return list(cls._model_registry.keys())
    
    @classmethod
    def get_model_info(cls, model_name: str) -> Dict[str, Any]:
        """Get information about a model.
        
        Args:
            model_name: Name of the model
            
        Returns:
            Dictionary with model information
        """
        model_name_lower = model_name.lower()
        
        if model_name_lower not in cls._model_registry:
            raise ValueError(f"Unknown model '{model_name}'")
        
        model_class = cls._model_registry[model_name_lower]
        
        return {
            'name': model_name,
            'class': model_class.__name__,
            'module': model_class.__module__,
            'docstring': model_class.__doc__,
        }
    
    @classmethod
    def create_ensemble_models(cls, model_configs: List[Dict[str, Any]]) -> List[BaseModel]:
        """Create multiple models for ensemble.
        
        Args:
            model_configs: List of model configuration dictionaries
                          Each dict should have 'name' key and optional parameters
            
        Returns:
            List of model instances
        """
        models = []
        
        for config in model_configs:
            if 'name' not in config:
                raise ValueError("Each model config must have a 'name' key")
            
            model_name = config.pop('name')
            model = cls.create_model(model_name, **config)
            models.append(model)
        
        logger.info(f"Created ensemble of {len(models)} models")
        return models
    
    @classmethod
    def get_default_configs(cls) -> Dict[str, Dict[str, Any]]:
        """Get default configurations for all models.
        
        Returns:
            Dictionary mapping model names to default configurations
        """
        return {
            'lightgbm': {
                'objective': 'regression',
                'metric': 'rmse',
                'boosting_type': 'gbdt',
                'num_leaves': 31,
                'learning_rate': 0.05,
                'feature_fraction': 0.9,
                'n_estimators': 1000,
                'early_stopping_rounds': 50
            },
            'xgboost': {
                'objective': 'reg:squarederror',
                'eval_metric': 'rmse',
                'max_depth': 6,
                'learning_rate': 0.05,
                'subsample': 0.8,
                'colsample_bytree': 0.8,
                'n_estimators': 1000,
                'early_stopping_rounds': 50
            },
            'lstm': {
                'hidden_size': 64,
                'num_layers': 2,
                'dropout': 0.2,
                'sequence_length': 60,
                'batch_size': 32,
                'learning_rate': 0.001,
                'epochs': 100,
                'patience': 10
            }
        }
    
    @classmethod
    def create_model_from_config(cls, config: Dict[str, Any]) -> BaseModel:
        """Create model from configuration dictionary.
        
        Args:
            config: Configuration dictionary with 'name' and parameters
            
        Returns:
            Model instance
        """
        if 'name' not in config:
            raise ValueError("Configuration must have a 'name' key")
        
        model_name = config.pop('name')
        return cls.create_model(model_name, **config)
    
    @classmethod
    def get_recommended_models(cls, task_type: str = 'regression',
                              data_size: str = 'medium') -> List[str]:
        """Get recommended models for a specific task and data size.
        
        Args:
            task_type: Type of task ('regression', 'classification')
            data_size: Size of dataset ('small', 'medium', 'large')
            
        Returns:
            List of recommended model names
        """
        recommendations = {
            'regression': {
                'small': ['lightgbm', 'xgboost'],
                'medium': ['lightgbm', 'xgboost', 'lstm'],
                'large': ['lightgbm', 'xgboost', 'lstm']
            },
            'classification': {
                'small': ['lightgbm', 'xgboost'],
                'medium': ['lightgbm', 'xgboost'],
                'large': ['lightgbm', 'xgboost']
            }
        }
        
        return recommendations.get(task_type, {}).get(data_size, ['lightgbm'])
    
    @classmethod
    def validate_model_config(cls, model_name: str, config: Dict[str, Any]) -> bool:
        """Validate model configuration.
        
        Args:
            model_name: Name of the model
            config: Configuration dictionary
            
        Returns:
            True if configuration is valid
        """
        try:
            # Try to create model with config
            model = cls.create_model(model_name, **config)
            return True
        except Exception as e:
            logger.error(f"Invalid configuration for {model_name}: {e}")
            return False


# Convenience functions
def create_model(model_name: str, **kwargs) -> BaseModel:
    """Create a model instance (convenience function).
    
    Args:
        model_name: Name of the model to create
        **kwargs: Model-specific parameters
        
    Returns:
        Model instance
    """
    return ModelFactory.create_model(model_name, **kwargs)


def get_available_models() -> List[str]:
    """Get list of available model names (convenience function).
    
    Returns:
        List of available model names
    """
    return ModelFactory.get_available_models()


def create_ensemble(model_configs: List[Dict[str, Any]]) -> List[BaseModel]:
    """Create ensemble of models (convenience function).
    
    Args:
        model_configs: List of model configuration dictionaries
        
    Returns:
        List of model instances
    """
    return ModelFactory.create_ensemble_models(model_configs)
