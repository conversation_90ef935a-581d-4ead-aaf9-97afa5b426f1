"""XGBoost model implementation for FinRobot-Pro."""

import logging
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
import xgboost as xgb
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

from .base_model import BaseModel, ModelResult

logger = logging.getLogger(__name__)


class XGBoostModel(BaseModel):
    """XGBoost model for financial forecasting."""
    
    def __init__(self, **kwargs):
        """Initialize XGBoost model.
        
        Args:
            **kwargs: XGBoost parameters
        """
        super().__init__("XGBoost", **kwargs)
        
        # Default parameters optimized for financial time series
        default_params = {
            'objective': 'reg:squarederror',
            'eval_metric': 'rmse',
            'max_depth': 6,
            'learning_rate': 0.05,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'random_state': self.random_seed,
            'n_estimators': 1000,
            'early_stopping_rounds': 50,
            'reg_alpha': 0.1,
            'reg_lambda': 0.1,
            'tree_method': 'hist',
            'verbosity': 0
        }
        
        # Merge with user parameters
        self.xgb_params = {**default_params, **self.model_config, **kwargs}
        
        # Separate training parameters
        self.n_estimators = self.xgb_params.pop('n_estimators', 1000)
        self.early_stopping_rounds = self.xgb_params.pop('early_stopping_rounds', 50)
        
        logger.info(f"XGBoost initialized with params: {self.xgb_params}")
    
    def fit(self, X: Union[pd.DataFrame, np.ndarray], 
            y: Union[pd.Series, np.ndarray],
            validation_data: Optional[Tuple] = None,
            **kwargs) -> 'XGBoostModel':
        """Fit XGBoost model.
        
        Args:
            X: Feature matrix
            y: Target vector
            validation_data: Optional validation data tuple (X_val, y_val)
            **kwargs: Additional fitting parameters
            
        Returns:
            Self for method chaining
        """
        logger.info("Training XGBoost model")
        
        # Validate input
        X, y = self.validate_input(X, y)
        
        # Start MLflow run
        self._start_mlflow_run()
        
        try:
            # Prepare evaluation sets
            eval_set = [(X, y)]
            eval_names = ['train']
            
            if validation_data is not None:
                X_val, y_val = validation_data
                X_val, y_val = self.validate_input(X_val, y_val)
                eval_set.append((X_val, y_val))
                eval_names.append('valid')
            
            # Initialize and train model
            self.model = xgb.XGBRegressor(
                **self.xgb_params,
                n_estimators=self.n_estimators,
                early_stopping_rounds=self.early_stopping_rounds if validation_data else None
            )
            
            # Fit model
            fit_params = {
                'eval_set': eval_set,
                'verbose': False
            }
            
            if validation_data is not None:
                fit_params['early_stopping_rounds'] = self.early_stopping_rounds
            
            self.model.fit(X, y, **fit_params)
            self.is_fitted = True
            
            # Calculate training metrics
            train_pred = self.model.predict(X)
            train_metrics = self._calculate_metrics(y, train_pred, "train")
            
            # Store training residuals for prediction intervals
            self.training_residuals = y - train_pred
            
            # Calculate validation metrics if available
            if validation_data is not None:
                val_pred = self.model.predict(X_val)
                val_metrics = self._calculate_metrics(y_val, val_pred, "val")
                train_metrics.update(val_metrics)
            
            # Store training metadata
            self.training_metadata = {
                'n_samples': len(X),
                'n_features': X.shape[1],
                'best_iteration': getattr(self.model, 'best_iteration', self.n_estimators),
                'feature_importance': self.get_feature_importance().to_dict() if self.feature_names else None,
                **train_metrics
            }
            
            # Log metrics to MLflow
            self._log_metrics(train_metrics)
            
            logger.info(f"XGBoost training completed. Best iteration: {getattr(self.model, 'best_iteration', self.n_estimators)}")
            
        except Exception as e:
            logger.error(f"Error training XGBoost model: {e}")
            raise
        finally:
            self._end_mlflow_run()
        
        return self
    
    def predict(self, X: Union[pd.DataFrame, np.ndarray],
                return_confidence: bool = True,
                **kwargs) -> ModelResult:
        """Make predictions with XGBoost.
        
        Args:
            X: Feature matrix
            return_confidence: Whether to return confidence intervals
            **kwargs: Additional prediction parameters
            
        Returns:
            ModelResult with predictions and metadata
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before making predictions")
        
        # Validate input
        X, _ = self.validate_input(X)
        
        # Make predictions
        predictions = self.model.predict(X)
        
        # Calculate confidence intervals if requested
        confidence_intervals = None
        prediction_intervals = None
        confidence_scores = None
        
        if return_confidence:
            # Use training residuals for prediction intervals
            if hasattr(self, 'training_residuals'):
                prediction_intervals = self.calculate_prediction_intervals(
                    predictions, self.training_residuals
                )
            
            # Calculate confidence scores
            confidence_scores = self._calculate_confidence_scores(X, predictions)
        
        return ModelResult(
            predictions=predictions,
            confidence_intervals=confidence_intervals,
            prediction_intervals=prediction_intervals,
            confidence_scores=confidence_scores,
            metadata={
                'model_name': self.model_name,
                'n_predictions': len(predictions),
                'feature_importance': self.get_feature_importance().to_dict() if self.feature_names else None
            }
        )
    
    def get_feature_importance(self) -> Optional[pd.DataFrame]:
        """Get feature importance from XGBoost model.
        
        Returns:
            DataFrame with feature importance
        """
        if not self.is_fitted or not self.feature_names:
            return None
        
        importance = self.model.feature_importances_
        
        importance_df = pd.DataFrame({
            'feature': self.feature_names,
            'importance': importance
        }).sort_values('importance', ascending=False)
        
        return importance_df
    
    def _calculate_metrics(self, y_true: np.ndarray, y_pred: np.ndarray, 
                          prefix: str = "") -> Dict[str, float]:
        """Calculate regression metrics.
        
        Args:
            y_true: True values
            y_pred: Predicted values
            prefix: Prefix for metric names
            
        Returns:
            Dictionary of metrics
        """
        metrics = {}
        prefix = f"{prefix}_" if prefix else ""
        
        try:
            metrics[f'{prefix}rmse'] = np.sqrt(mean_squared_error(y_true, y_pred))
            metrics[f'{prefix}mae'] = mean_absolute_error(y_true, y_pred)
            metrics[f'{prefix}r2'] = r2_score(y_true, y_pred)
            
            # Financial-specific metrics
            # Directional accuracy
            direction_true = np.sign(y_true)
            direction_pred = np.sign(y_pred)
            metrics[f'{prefix}directional_accuracy'] = np.mean(direction_true == direction_pred)
            
            # Mean absolute percentage error (handle division by zero)
            non_zero_mask = y_true != 0
            if np.any(non_zero_mask):
                mape = np.mean(np.abs((y_true[non_zero_mask] - y_pred[non_zero_mask]) / y_true[non_zero_mask]))
                metrics[f'{prefix}mape'] = mape
            
            # Sharpe-like ratio for predictions
            if np.std(y_pred) > 0:
                pred_sharpe = np.mean(y_pred) / np.std(y_pred)
                metrics[f'{prefix}prediction_sharpe'] = pred_sharpe
            
        except Exception as e:
            logger.warning(f"Error calculating metrics: {e}")
        
        return metrics
    
    def _calculate_confidence_scores(self, X: np.ndarray, predictions: np.ndarray) -> np.ndarray:
        """Calculate confidence scores for predictions.
        
        Args:
            X: Feature matrix
            predictions: Model predictions
            
        Returns:
            Array of confidence scores
        """
        try:
            # Use leaf indices to estimate prediction uncertainty
            # Get leaf indices for each tree
            leaf_indices = self.model.apply(X)  # Shape: (n_samples, n_trees)
            
            if leaf_indices.ndim == 2:
                # Calculate prediction consistency across trees
                # Trees that put samples in similar leaf positions are more consistent
                
                # For each sample, calculate the variance in leaf positions
                leaf_variance = np.var(leaf_indices, axis=1)
                
                # Convert to confidence scores (lower variance = higher confidence)
                max_variance = np.max(leaf_variance) if len(leaf_variance) > 0 else 1.0
                confidence_scores = 1.0 - (leaf_variance / max_variance)
                
                # Ensure scores are in reasonable range
                return np.clip(confidence_scores, 0.1, 1.0)
            
            # Fallback: uniform confidence
            return np.full(len(predictions), 0.7)
            
        except Exception as e:
            logger.warning(f"Error calculating confidence scores: {e}")
            return np.full(len(predictions), 0.5)
    
    def get_model_params(self) -> Dict[str, Any]:
        """Get model parameters.
        
        Returns:
            Dictionary of model parameters
        """
        return {
            **self.xgb_params,
            'n_estimators': self.n_estimators,
            'early_stopping_rounds': self.early_stopping_rounds
        }
    
    def plot_importance(self, max_features: int = 20) -> None:
        """Plot feature importance.
        
        Args:
            max_features: Maximum number of features to plot
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before plotting importance")
        
        try:
            import matplotlib.pyplot as plt
            
            importance_df = self.get_feature_importance()
            if importance_df is not None:
                top_features = importance_df.head(max_features)
                
                plt.figure(figsize=(10, 8))
                plt.barh(range(len(top_features)), top_features['importance'])
                plt.yticks(range(len(top_features)), top_features['feature'])
                plt.xlabel('Feature Importance')
                plt.title(f'Top {max_features} Feature Importance - XGBoost')
                plt.gca().invert_yaxis()
                plt.tight_layout()
                plt.show()
            
        except ImportError:
            logger.warning("Matplotlib not available for plotting")
        except Exception as e:
            logger.error(f"Error plotting importance: {e}")
