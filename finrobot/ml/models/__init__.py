"""Model zoo for FinRobot-Pro."""

from .base_model import BaseModel, ModelResult
from .lightgbm_model import LightGBMModel
from .xgboost_model import XGBoostModel
from .lstm_model import LSTMModel
from .model_factory import ModelFactory

# Import other models if they exist
try:
    from .arima_model import ARIMAModel
except ImportError:
    ARIMAModel = None

try:
    from .prophet_model import ProphetModel
except ImportError:
    ProphetModel = None

try:
    from .tft_model import TFTModel
except ImportError:
    TFTModel = None

try:
    from .model_registry import ModelRegistry
except ImportError:
    ModelRegistry = None

__all__ = [
    "BaseModel",
    "ModelResult",
    "LightGBMModel",
    "XGBoostModel",
    "LSTMModel",
    "ModelFactory",
]

# Add to __all__ only if imported successfully
if ARIMAModel:
    __all__.append("ARIMAModel")
if ProphetModel:
    __all__.append("ProphetModel")
if TFTModel:
    __all__.append("TFTModel")
if ModelRegistry:
    __all__.append("ModelRegistry")
