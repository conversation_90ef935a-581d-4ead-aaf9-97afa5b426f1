"""LSTM model implementation for FinRobot-Pro."""

import logging
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

from .base_model import BaseModel, ModelResult

logger = logging.getLogger(__name__)


class LSTMNetwork(nn.Module):
    """LSTM neural network for time series forecasting."""
    
    def __init__(self, input_size: int, hidden_size: int = 64, 
                 num_layers: int = 2, dropout: float = 0.2, 
                 output_size: int = 1):
        """Initialize LSTM network.
        
        Args:
            input_size: Number of input features
            hidden_size: Hidden layer size
            num_layers: Number of LSTM layers
            dropout: Dropout rate
            output_size: Number of output features
        """
        super(LSTMNetwork, self).__init__()
        
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # LSTM layers
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout if num_layers > 1 else 0,
            batch_first=True
        )
        
        # Dropout layer
        self.dropout = nn.Dropout(dropout)
        
        # Output layer
        self.linear = nn.Linear(hidden_size, output_size)
        
    def forward(self, x):
        """Forward pass."""
        # Initialize hidden state
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
        
        # LSTM forward pass
        lstm_out, _ = self.lstm(x, (h0, c0))
        
        # Take the last output
        lstm_out = lstm_out[:, -1, :]
        
        # Apply dropout
        lstm_out = self.dropout(lstm_out)
        
        # Linear layer
        output = self.linear(lstm_out)
        
        return output


class LSTMModel(BaseModel):
    """LSTM model for financial time series forecasting."""
    
    def __init__(self, **kwargs):
        """Initialize LSTM model.
        
        Args:
            **kwargs: LSTM parameters
        """
        super().__init__("LSTM", **kwargs)
        
        # Default parameters
        default_params = {
            'hidden_size': 64,
            'num_layers': 2,
            'dropout': 0.2,
            'sequence_length': 60,
            'batch_size': 32,
            'learning_rate': 0.001,
            'epochs': 100,
            'patience': 10,
            'min_delta': 1e-4
        }
        
        # Merge with user parameters
        self.lstm_params = {**default_params, **self.model_config, **kwargs}
        
        # Set device
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"Using device: {self.device}")
        
        # Model components
        self.network = None
        self.optimizer = None
        self.criterion = nn.MSELoss()
        self.scaler_X = None
        self.scaler_y = None
        
        logger.info(f"LSTM initialized with params: {self.lstm_params}")
    
    def _create_sequences(self, X: np.ndarray, y: np.ndarray, 
                         sequence_length: int) -> Tuple[np.ndarray, np.ndarray]:
        """Create sequences for LSTM training.
        
        Args:
            X: Feature matrix
            y: Target vector
            sequence_length: Length of input sequences
            
        Returns:
            Tuple of (X_sequences, y_sequences)
        """
        X_sequences = []
        y_sequences = []
        
        for i in range(sequence_length, len(X)):
            X_sequences.append(X[i-sequence_length:i])
            y_sequences.append(y[i])
        
        return np.array(X_sequences), np.array(y_sequences)
    
    def fit(self, X: Union[pd.DataFrame, np.ndarray], 
            y: Union[pd.Series, np.ndarray],
            validation_data: Optional[Tuple] = None,
            **kwargs) -> 'LSTMModel':
        """Fit LSTM model.
        
        Args:
            X: Feature matrix
            y: Target vector
            validation_data: Optional validation data tuple (X_val, y_val)
            **kwargs: Additional fitting parameters
            
        Returns:
            Self for method chaining
        """
        logger.info("Training LSTM model")
        
        # Validate input
        X, y = self.validate_input(X, y)
        
        # Start MLflow run
        self._start_mlflow_run()
        
        try:
            # Normalize data
            from sklearn.preprocessing import StandardScaler
            
            self.scaler_X = StandardScaler()
            self.scaler_y = StandardScaler()
            
            X_scaled = self.scaler_X.fit_transform(X)
            y_scaled = self.scaler_y.fit_transform(y.reshape(-1, 1)).ravel()
            
            # Create sequences
            sequence_length = self.lstm_params['sequence_length']
            X_seq, y_seq = self._create_sequences(X_scaled, y_scaled, sequence_length)
            
            if len(X_seq) == 0:
                raise ValueError(f"Not enough data for sequence length {sequence_length}")
            
            # Convert to tensors
            X_tensor = torch.FloatTensor(X_seq).to(self.device)
            y_tensor = torch.FloatTensor(y_seq).to(self.device)
            
            # Create data loader
            dataset = TensorDataset(X_tensor, y_tensor)
            dataloader = DataLoader(
                dataset, 
                batch_size=self.lstm_params['batch_size'], 
                shuffle=True
            )
            
            # Prepare validation data if available
            val_loader = None
            if validation_data is not None:
                X_val, y_val = validation_data
                X_val, y_val = self.validate_input(X_val, y_val)
                
                X_val_scaled = self.scaler_X.transform(X_val)
                y_val_scaled = self.scaler_y.transform(y_val.reshape(-1, 1)).ravel()
                
                X_val_seq, y_val_seq = self._create_sequences(X_val_scaled, y_val_scaled, sequence_length)
                
                if len(X_val_seq) > 0:
                    X_val_tensor = torch.FloatTensor(X_val_seq).to(self.device)
                    y_val_tensor = torch.FloatTensor(y_val_seq).to(self.device)
                    
                    val_dataset = TensorDataset(X_val_tensor, y_val_tensor)
                    val_loader = DataLoader(val_dataset, batch_size=self.lstm_params['batch_size'])
            
            # Initialize network
            input_size = X_seq.shape[2]
            self.network = LSTMNetwork(
                input_size=input_size,
                hidden_size=self.lstm_params['hidden_size'],
                num_layers=self.lstm_params['num_layers'],
                dropout=self.lstm_params['dropout']
            ).to(self.device)
            
            # Initialize optimizer
            self.optimizer = optim.Adam(
                self.network.parameters(), 
                lr=self.lstm_params['learning_rate']
            )
            
            # Training loop
            best_val_loss = float('inf')
            patience_counter = 0
            train_losses = []
            val_losses = []
            
            for epoch in range(self.lstm_params['epochs']):
                # Training phase
                self.network.train()
                train_loss = 0.0
                
                for batch_X, batch_y in dataloader:
                    self.optimizer.zero_grad()
                    
                    outputs = self.network(batch_X)
                    loss = self.criterion(outputs.squeeze(), batch_y)
                    
                    loss.backward()
                    self.optimizer.step()
                    
                    train_loss += loss.item()
                
                train_loss /= len(dataloader)
                train_losses.append(train_loss)
                
                # Validation phase
                val_loss = 0.0
                if val_loader is not None:
                    self.network.eval()
                    with torch.no_grad():
                        for batch_X, batch_y in val_loader:
                            outputs = self.network(batch_X)
                            loss = self.criterion(outputs.squeeze(), batch_y)
                            val_loss += loss.item()
                    
                    val_loss /= len(val_loader)
                    val_losses.append(val_loss)
                    
                    # Early stopping
                    if val_loss < best_val_loss - self.lstm_params['min_delta']:
                        best_val_loss = val_loss
                        patience_counter = 0
                        # Save best model state
                        self.best_model_state = self.network.state_dict().copy()
                    else:
                        patience_counter += 1
                    
                    if patience_counter >= self.lstm_params['patience']:
                        logger.info(f"Early stopping at epoch {epoch}")
                        break
                
                if epoch % 10 == 0:
                    logger.debug(f"Epoch {epoch}: Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}")
            
            # Load best model if available
            if hasattr(self, 'best_model_state'):
                self.network.load_state_dict(self.best_model_state)
            
            self.is_fitted = True
            
            # Calculate training metrics
            self.network.eval()
            with torch.no_grad():
                train_pred_scaled = self.network(X_tensor).cpu().numpy().squeeze()
                train_pred = self.scaler_y.inverse_transform(train_pred_scaled.reshape(-1, 1)).ravel()
                
                # Get corresponding true values
                y_train_true = self.scaler_y.inverse_transform(y_seq.reshape(-1, 1)).ravel()
                
                train_metrics = self._calculate_metrics(y_train_true, train_pred, "train")
                
                # Store training residuals
                self.training_residuals = y_train_true - train_pred
            
            # Store training metadata
            self.training_metadata = {
                'n_samples': len(X_seq),
                'n_features': input_size,
                'sequence_length': sequence_length,
                'epochs_trained': epoch + 1,
                'final_train_loss': train_losses[-1] if train_losses else None,
                'final_val_loss': val_losses[-1] if val_losses else None,
                **train_metrics
            }
            
            # Log metrics to MLflow
            self._log_metrics(train_metrics)
            
            logger.info(f"LSTM training completed. Epochs: {epoch + 1}")
            
        except Exception as e:
            logger.error(f"Error training LSTM model: {e}")
            raise
        finally:
            self._end_mlflow_run()
        
        return self
    
    def predict(self, X: Union[pd.DataFrame, np.ndarray],
                return_confidence: bool = True,
                **kwargs) -> ModelResult:
        """Make predictions with LSTM.
        
        Args:
            X: Feature matrix
            return_confidence: Whether to return confidence intervals
            **kwargs: Additional prediction parameters
            
        Returns:
            ModelResult with predictions and metadata
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before making predictions")
        
        # Validate input
        X, _ = self.validate_input(X)
        
        # Scale input
        X_scaled = self.scaler_X.transform(X)
        
        # Create sequences
        sequence_length = self.lstm_params['sequence_length']
        X_seq, _ = self._create_sequences(X_scaled, np.zeros(len(X_scaled)), sequence_length)
        
        if len(X_seq) == 0:
            raise ValueError(f"Not enough data for sequence length {sequence_length}")
        
        # Convert to tensor
        X_tensor = torch.FloatTensor(X_seq).to(self.device)
        
        # Make predictions
        self.network.eval()
        with torch.no_grad():
            pred_scaled = self.network(X_tensor).cpu().numpy().squeeze()
            predictions = self.scaler_y.inverse_transform(pred_scaled.reshape(-1, 1)).ravel()
        
        # Calculate confidence intervals if requested
        confidence_intervals = None
        prediction_intervals = None
        confidence_scores = None
        
        if return_confidence:
            # Use training residuals for prediction intervals
            if hasattr(self, 'training_residuals'):
                prediction_intervals = self.calculate_prediction_intervals(
                    predictions, self.training_residuals
                )
            
            # Simple confidence scores (could be enhanced)
            confidence_scores = np.full(len(predictions), 0.6)
        
        return ModelResult(
            predictions=predictions,
            confidence_intervals=confidence_intervals,
            prediction_intervals=prediction_intervals,
            confidence_scores=confidence_scores,
            metadata={
                'model_name': self.model_name,
                'n_predictions': len(predictions),
                'sequence_length': sequence_length
            }
        )
    
    def _calculate_metrics(self, y_true: np.ndarray, y_pred: np.ndarray, 
                          prefix: str = "") -> Dict[str, float]:
        """Calculate regression metrics.
        
        Args:
            y_true: True values
            y_pred: Predicted values
            prefix: Prefix for metric names
            
        Returns:
            Dictionary of metrics
        """
        metrics = {}
        prefix = f"{prefix}_" if prefix else ""
        
        try:
            metrics[f'{prefix}rmse'] = np.sqrt(mean_squared_error(y_true, y_pred))
            metrics[f'{prefix}mae'] = mean_absolute_error(y_true, y_pred)
            metrics[f'{prefix}r2'] = r2_score(y_true, y_pred)
            
            # Directional accuracy
            direction_true = np.sign(y_true)
            direction_pred = np.sign(y_pred)
            metrics[f'{prefix}directional_accuracy'] = np.mean(direction_true == direction_pred)
            
        except Exception as e:
            logger.warning(f"Error calculating metrics: {e}")
        
        return metrics
    
    def save_model(self, filepath: str) -> bool:
        """Save LSTM model to disk.
        
        Args:
            filepath: Path to save the model
            
        Returns:
            True if successful, False otherwise
        """
        try:
            model_data = {
                'network_state_dict': self.network.state_dict() if self.network else None,
                'model_name': self.model_name,
                'params': self.lstm_params,
                'is_fitted': self.is_fitted,
                'feature_names': self.feature_names,
                'target_name': self.target_name,
                'training_metadata': self.training_metadata,
                'scaler_X': self.scaler_X,
                'scaler_y': self.scaler_y
            }
            
            torch.save(model_data, filepath)
            logger.info(f"LSTM model saved to {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving LSTM model: {e}")
            return False
    
    def load_model(self, filepath: str) -> bool:
        """Load LSTM model from disk.
        
        Args:
            filepath: Path to load the model from
            
        Returns:
            True if successful, False otherwise
        """
        try:
            model_data = torch.load(filepath, map_location=self.device)
            
            self.model_name = model_data['model_name']
            self.lstm_params = model_data['params']
            self.is_fitted = model_data['is_fitted']
            self.feature_names = model_data.get('feature_names')
            self.target_name = model_data.get('target_name')
            self.training_metadata = model_data.get('training_metadata', {})
            self.scaler_X = model_data.get('scaler_X')
            self.scaler_y = model_data.get('scaler_y')
            
            # Reconstruct network
            if model_data['network_state_dict'] and self.feature_names:
                input_size = len(self.feature_names)
                self.network = LSTMNetwork(
                    input_size=input_size,
                    hidden_size=self.lstm_params['hidden_size'],
                    num_layers=self.lstm_params['num_layers'],
                    dropout=self.lstm_params['dropout']
                ).to(self.device)
                
                self.network.load_state_dict(model_data['network_state_dict'])
            
            logger.info(f"LSTM model loaded from {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"Error loading LSTM model: {e}")
            return False
