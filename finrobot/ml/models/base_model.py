"""Base model interface for FinRobot-Pro."""

import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
import mlflow
import joblib
from pathlib import Path

from ...config import get_config

logger = logging.getLogger(__name__)


@dataclass
class ModelResult:
    """Container for model prediction results."""
    
    predictions: np.ndarray
    confidence_intervals: Optional[Dict[str, np.ndarray]] = None
    prediction_intervals: Optional[Dict[str, np.ndarray]] = None
    confidence_scores: Optional[np.ndarray] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'predictions': self.predictions.tolist() if isinstance(self.predictions, np.ndarray) else self.predictions,
            'confidence_intervals': {k: v.tolist() if isinstance(v, np.ndarray) else v 
                                   for k, v in (self.confidence_intervals or {}).items()},
            'prediction_intervals': {k: v.tolist() if isinstance(v, np.ndarray) else v 
                                   for k, v in (self.prediction_intervals or {}).items()},
            'confidence_scores': self.confidence_scores.tolist() if isinstance(self.confidence_scores, np.ndarray) else self.confidence_scores,
            'metadata': self.metadata or {}
        }


class BaseModel(ABC):
    """Abstract base class for all financial forecasting models."""
    
    def __init__(self, model_name: str, **kwargs):
        """Initialize base model.
        
        Args:
            model_name: Name of the model
            **kwargs: Model-specific parameters
        """
        self.model_name = model_name
        self.config = get_config()
        self.model_config = self.config.models.get(model_name.lower(), {})
        
        # Model parameters
        self.params = kwargs
        self.random_seed = self.config.models.get("random_seed", 42)
        
        # Model state
        self.is_fitted = False
        self.model = None
        self.feature_names = None
        self.target_name = None
        
        # Training metadata
        self.training_metadata = {}
        
        # MLflow tracking
        self.mlflow_enabled = True
        self.experiment_name = self.config.mlflow.get("experiment_name", "finrobot_forecasting")
        
        logger.info(f"Initialized {model_name} model")
    
    @abstractmethod
    def fit(self, X: Union[pd.DataFrame, np.ndarray], 
            y: Union[pd.Series, np.ndarray],
            validation_data: Optional[Tuple] = None,
            **kwargs) -> 'BaseModel':
        """Fit the model to training data.
        
        Args:
            X: Feature matrix
            y: Target vector
            validation_data: Optional validation data tuple (X_val, y_val)
            **kwargs: Additional fitting parameters
            
        Returns:
            Self for method chaining
        """
        pass
    
    @abstractmethod
    def predict(self, X: Union[pd.DataFrame, np.ndarray],
                return_confidence: bool = True,
                **kwargs) -> ModelResult:
        """Make predictions.
        
        Args:
            X: Feature matrix
            return_confidence: Whether to return confidence intervals
            **kwargs: Additional prediction parameters
            
        Returns:
            ModelResult with predictions and metadata
        """
        pass
    
    def predict_proba(self, X: Union[pd.DataFrame, np.ndarray]) -> np.ndarray:
        """Predict class probabilities (for classification models).
        
        Args:
            X: Feature matrix
            
        Returns:
            Probability matrix
        """
        raise NotImplementedError(f"predict_proba not implemented for {self.model_name}")
    
    def get_feature_importance(self) -> Optional[pd.DataFrame]:
        """Get feature importance scores.
        
        Returns:
            DataFrame with feature importance or None if not available
        """
        return None
    
    def save_model(self, filepath: str) -> bool:
        """Save model to disk.
        
        Args:
            filepath: Path to save the model
            
        Returns:
            True if successful, False otherwise
        """
        try:
            model_data = {
                'model': self.model,
                'model_name': self.model_name,
                'params': self.params,
                'is_fitted': self.is_fitted,
                'feature_names': self.feature_names,
                'target_name': self.target_name,
                'training_metadata': self.training_metadata
            }
            
            joblib.dump(model_data, filepath)
            logger.info(f"Model saved to {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving model: {e}")
            return False
    
    def load_model(self, filepath: str) -> bool:
        """Load model from disk.
        
        Args:
            filepath: Path to load the model from
            
        Returns:
            True if successful, False otherwise
        """
        try:
            model_data = joblib.load(filepath)
            
            self.model = model_data['model']
            self.model_name = model_data['model_name']
            self.params = model_data['params']
            self.is_fitted = model_data['is_fitted']
            self.feature_names = model_data.get('feature_names')
            self.target_name = model_data.get('target_name')
            self.training_metadata = model_data.get('training_metadata', {})
            
            logger.info(f"Model loaded from {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            return False
    
    def validate_input(self, X: Union[pd.DataFrame, np.ndarray],
                      y: Optional[Union[pd.Series, np.ndarray]] = None) -> Tuple[np.ndarray, Optional[np.ndarray]]:
        """Validate and convert input data.
        
        Args:
            X: Feature matrix
            y: Optional target vector
            
        Returns:
            Tuple of validated (X, y)
        """
        # Convert to numpy arrays
        if isinstance(X, pd.DataFrame):
            self.feature_names = X.columns.tolist()
            X = X.values
        elif isinstance(X, pd.Series):
            X = X.values.reshape(-1, 1)
        
        if y is not None:
            if isinstance(y, pd.Series):
                self.target_name = y.name
                y = y.values
            elif isinstance(y, pd.DataFrame):
                self.target_name = y.columns[0] if len(y.columns) == 1 else None
                y = y.values.ravel()
        
        # Validate shapes
        if X.ndim == 1:
            X = X.reshape(-1, 1)
        
        if y is not None and len(X) != len(y):
            raise ValueError(f"X and y must have same length: {len(X)} vs {len(y)}")
        
        return X, y
    
    def _start_mlflow_run(self, run_name: Optional[str] = None):
        """Start MLflow run for tracking."""
        if not self.mlflow_enabled:
            return
        
        try:
            mlflow.set_experiment(self.experiment_name)
            mlflow.start_run(run_name=run_name or f"{self.model_name}_training")
            
            # Log model parameters
            mlflow.log_params(self.params)
            mlflow.log_param("model_name", self.model_name)
            mlflow.log_param("random_seed", self.random_seed)
            
        except Exception as e:
            logger.warning(f"MLflow tracking failed: {e}")
            self.mlflow_enabled = False
    
    def _log_metrics(self, metrics: Dict[str, float]):
        """Log metrics to MLflow."""
        if not self.mlflow_enabled:
            return
        
        try:
            mlflow.log_metrics(metrics)
        except Exception as e:
            logger.warning(f"MLflow metric logging failed: {e}")
    
    def _end_mlflow_run(self):
        """End MLflow run."""
        if not self.mlflow_enabled:
            return
        
        try:
            mlflow.end_run()
        except Exception as e:
            logger.warning(f"MLflow run end failed: {e}")
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get model information.
        
        Returns:
            Dictionary with model information
        """
        return {
            'model_name': self.model_name,
            'is_fitted': self.is_fitted,
            'parameters': self.params,
            'feature_names': self.feature_names,
            'target_name': self.target_name,
            'training_metadata': self.training_metadata
        }
    
    def calculate_prediction_intervals(self, predictions: np.ndarray,
                                     residuals: Optional[np.ndarray] = None,
                                     confidence_levels: List[float] = [0.8, 0.95]) -> Dict[str, np.ndarray]:
        """Calculate prediction intervals using residual bootstrap.
        
        Args:
            predictions: Point predictions
            residuals: Training residuals for bootstrap
            confidence_levels: Confidence levels for intervals
            
        Returns:
            Dictionary with prediction intervals
        """
        if residuals is None or len(residuals) == 0:
            # Fallback to simple intervals based on prediction variance
            pred_std = np.std(predictions) if len(predictions) > 1 else 0.1
            intervals = {}
            
            for level in confidence_levels:
                alpha = 1 - level
                z_score = 1.96 if level == 0.95 else 1.28  # Approximate z-scores
                
                intervals[f'{level:.0%}_lower'] = predictions - z_score * pred_std
                intervals[f'{level:.0%}_upper'] = predictions + z_score * pred_std
            
            return intervals
        
        # Bootstrap prediction intervals
        n_bootstrap = 1000
        intervals = {}
        
        for level in confidence_levels:
            alpha = 1 - level
            lower_percentile = (alpha / 2) * 100
            upper_percentile = (1 - alpha / 2) * 100
            
            # Bootstrap residuals
            bootstrap_predictions = []
            for _ in range(n_bootstrap):
                bootstrap_residuals = np.random.choice(residuals, size=len(predictions), replace=True)
                bootstrap_pred = predictions + bootstrap_residuals
                bootstrap_predictions.append(bootstrap_pred)
            
            bootstrap_predictions = np.array(bootstrap_predictions)
            
            intervals[f'{level:.0%}_lower'] = np.percentile(bootstrap_predictions, lower_percentile, axis=0)
            intervals[f'{level:.0%}_upper'] = np.percentile(bootstrap_predictions, upper_percentile, axis=0)
        
        return intervals
    
    def __repr__(self) -> str:
        """String representation."""
        status = "fitted" if self.is_fitted else "not fitted"
        return f"{self.model_name}({status}, params={self.params})"
