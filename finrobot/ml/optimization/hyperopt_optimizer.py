"""Bayesian hyperparameter optimization using hyperopt."""

import logging
from typing import Dict, Any, Optional, Callable, Tuple
import numpy as np
import pandas as pd
from pathlib import Path

logger = logging.getLogger(__name__)

try:
    from hyperopt import fmin, tpe, Trials, STATUS_OK, STATUS_FAIL
    HYPEROPT_AVAILABLE = True
except ImportError:
    HYPEROPT_AVAILABLE = False
    logger.warning("hyperopt not available. Install with: pip install hyperopt")

from .parameter_spaces import get_parameter_space, get_default_parameters
from .early_stopping import EarlyStopping
from ..models.model_factory import ModelFactory


class HyperoptOptimizer:
    """
    Bayesian hyperparameter optimization using Tree-structured Parzen Estimator (TPE).
    
    Integrates with walk-forward cross-validation and early stopping mechanisms
    to find optimal hyperparameters while preventing overfitting.
    """
    
    def __init__(
        self,
        max_evals: int = 50,
        early_stopping_patience: int = 10,
        overfitting_threshold: float = 1.2,
        random_state: int = 42
    ):
        """
        Initialize hyperparameter optimizer.
        
        Args:
            max_evals: Maximum number of evaluations
            early_stopping_patience: Patience for early stopping
            overfitting_threshold: Threshold for overfitting detection (val_loss/train_loss)
            random_state: Random seed for reproducibility
        """
        if not HYPEROPT_AVAILABLE:
            logger.warning("hyperopt not available. Using default parameters.")
            
        self.max_evals = max_evals
        self.early_stopping_patience = early_stopping_patience
        self.overfitting_threshold = overfitting_threshold
        self.random_state = random_state
        
        self.trials = None
        self.best_params = None
        self.best_score = float('inf')
        
    def optimize(
        self,
        model_name: str,
        X_train: pd.DataFrame,
        y_train: pd.Series,
        X_val: pd.DataFrame,
        y_val: pd.Series,
        objective_metric: str = 'rmse',
        **model_kwargs
    ) -> Dict[str, Any]:
        """
        Optimize hyperparameters for a given model.
        
        Args:
            model_name: Name of the model to optimize
            X_train: Training features
            y_train: Training targets
            X_val: Validation features
            y_val: Validation targets
            objective_metric: Metric to optimize ('rmse', 'mae', 'directional_accuracy')
            **model_kwargs: Additional model arguments
            
        Returns:
            Dictionary containing best parameters and optimization results
        """
        if not HYPEROPT_AVAILABLE:
            logger.warning("Using default parameters (hyperopt not available)")
            return {
                'best_params': get_default_parameters(model_name),
                'best_score': None,
                'optimization_history': [],
                'status': 'default_params'
            }
        
        logger.info(f"Starting hyperparameter optimization for {model_name}")
        logger.info(f"Training samples: {len(X_train)}, Validation samples: {len(X_val)}")
        
        # Get parameter space
        try:
            space = get_parameter_space(model_name)
        except ValueError as e:
            logger.error(f"Error getting parameter space: {e}")
            return {
                'best_params': get_default_parameters(model_name),
                'best_score': None,
                'optimization_history': [],
                'status': 'error'
            }
        
        # Initialize trials
        self.trials = Trials()
        
        # Define objective function
        def objective(params):
            return self._objective_function(
                params, model_name, X_train, y_train, X_val, y_val,
                objective_metric, **model_kwargs
            )
        
        try:
            # Run optimization
            best_params = fmin(
                fn=objective,
                space=space,
                algo=tpe.suggest,
                max_evals=self.max_evals,
                trials=self.trials,
                rstate=np.random.RandomState(self.random_state),
                verbose=False
            )
            
            # Get optimization history
            history = []
            for trial in self.trials.trials:
                if trial['state'] == 2:  # COMPLETE
                    history.append({
                        'params': trial['misc']['vals'],
                        'loss': trial['result']['loss'],
                        'status': trial['result']['status']
                    })
            
            logger.info(f"Optimization completed. Best score: {self.best_score:.4f}")
            
            return {
                'best_params': self.best_params or best_params,
                'best_score': self.best_score,
                'optimization_history': history,
                'status': 'success'
            }
            
        except Exception as e:
            logger.error(f"Optimization failed: {e}")
            return {
                'best_params': get_default_parameters(model_name),
                'best_score': None,
                'optimization_history': [],
                'status': 'error',
                'error': str(e)
            }
    
    def _objective_function(
        self,
        params: Dict[str, Any],
        model_name: str,
        X_train: pd.DataFrame,
        y_train: pd.Series,
        X_val: pd.DataFrame,
        y_val: pd.Series,
        objective_metric: str,
        **model_kwargs
    ) -> Dict[str, Any]:
        """
        Objective function for hyperparameter optimization.
        
        Args:
            params: Hyperparameters to evaluate
            model_name: Name of the model
            X_train: Training features
            y_train: Training targets
            X_val: Validation features
            y_val: Validation targets
            objective_metric: Metric to optimize
            **model_kwargs: Additional model arguments
            
        Returns:
            Dictionary with loss and status
        """
        try:
            # Create model with parameters
            model_config = {**params, **model_kwargs}
            model = ModelFactory.create_model(model_name, **model_config)
            
            # Train model
            model.fit(X_train, y_train, X_val, y_val)
            
            # Get predictions
            train_pred = model.predict(X_train)
            val_pred = model.predict(X_val)
            
            # Calculate metrics
            train_loss = self._calculate_metric(y_train, train_pred, objective_metric)
            val_loss = self._calculate_metric(y_val, val_pred, objective_metric)
            
            # Check for overfitting
            if val_loss > self.overfitting_threshold * train_loss:
                logger.debug(f"Overfitting detected: val_loss={val_loss:.4f}, train_loss={train_loss:.4f}")
                return {'loss': val_loss * 2, 'status': STATUS_OK}  # Penalize overfitting
            
            # Update best score
            if val_loss < self.best_score:
                self.best_score = val_loss
                self.best_params = params.copy()
            
            return {'loss': val_loss, 'status': STATUS_OK}
            
        except Exception as e:
            logger.debug(f"Objective function failed: {e}")
            return {'loss': float('inf'), 'status': STATUS_FAIL}
    
    def _calculate_metric(
        self,
        y_true: pd.Series,
        y_pred: np.ndarray,
        metric: str
    ) -> float:
        """
        Calculate the specified metric.
        
        Args:
            y_true: True values
            y_pred: Predicted values
            metric: Metric name
            
        Returns:
            Metric value
        """
        if metric == 'rmse':
            return np.sqrt(np.mean((y_true - y_pred) ** 2))
        elif metric == 'mae':
            return np.mean(np.abs(y_true - y_pred))
        elif metric == 'directional_accuracy':
            # For directional accuracy, we want to maximize, so return negative
            if len(y_true) < 2:
                return 1.0
            direction_true = np.sign(y_true.diff().dropna())
            direction_pred = np.sign(np.diff(y_pred))
            accuracy = np.mean(direction_true == direction_pred[:len(direction_true)])
            return -accuracy  # Negative because we minimize
        else:
            raise ValueError(f"Unsupported metric: {metric}")


def optimize_model_hyperparameters(
    model_name: str,
    X_train: pd.DataFrame,
    y_train: pd.Series,
    X_val: pd.DataFrame,
    y_val: pd.Series,
    max_evals: int = 50,
    objective_metric: str = 'rmse',
    **model_kwargs
) -> Dict[str, Any]:
    """
    Convenience function for hyperparameter optimization.
    
    Args:
        model_name: Name of the model to optimize
        X_train: Training features
        y_train: Training targets
        X_val: Validation features
        y_val: Validation targets
        max_evals: Maximum number of evaluations
        objective_metric: Metric to optimize
        **model_kwargs: Additional model arguments
        
    Returns:
        Optimization results
    """
    optimizer = HyperoptOptimizer(max_evals=max_evals)
    return optimizer.optimize(
        model_name, X_train, y_train, X_val, y_val,
        objective_metric, **model_kwargs
    )
