"""Early stopping mechanism for hyperparameter optimization."""

import logging
from typing import List, Optional

logger = logging.getLogger(__name__)


class EarlyStopping:
    """
    Early stopping mechanism to prevent overfitting during optimization.
    
    Stops training when validation loss exceeds 1.2 * training loss,
    indicating potential overfitting.
    """
    
    def __init__(
        self,
        patience: int = 10,
        min_delta: float = 0.001,
        overfitting_threshold: float = 1.2,
        restore_best_weights: bool = True
    ):
        """
        Initialize early stopping.
        
        Args:
            patience: Number of epochs to wait before stopping
            min_delta: Minimum change to qualify as improvement
            overfitting_threshold: Ratio of val_loss/train_loss to trigger stop
            restore_best_weights: Whether to restore best weights on stop
        """
        self.patience = patience
        self.min_delta = min_delta
        self.overfitting_threshold = overfitting_threshold
        self.restore_best_weights = restore_best_weights
        
        self.best_loss = float('inf')
        self.best_epoch = 0
        self.wait = 0
        self.stopped_epoch = 0
        self.best_weights = None
        
    def __call__(
        self,
        val_loss: float,
        train_loss: Optional[float] = None,
        epoch: int = 0,
        model_weights: Optional[dict] = None
    ) -> bool:
        """
        Check if training should stop.
        
        Args:
            val_loss: Current validation loss
            train_loss: Current training loss (optional)
            epoch: Current epoch number
            model_weights: Current model weights (optional)
            
        Returns:
            True if training should stop, False otherwise
        """
        # Check for overfitting
        if train_loss is not None and val_loss > self.overfitting_threshold * train_loss:
            logger.warning(
                f"Overfitting detected at epoch {epoch}: "
                f"val_loss ({val_loss:.4f}) > {self.overfitting_threshold} * "
                f"train_loss ({train_loss:.4f})"
            )
            self.stopped_epoch = epoch
            return True
        
        # Check for improvement
        if val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.best_epoch = epoch
            self.wait = 0
            
            if self.restore_best_weights and model_weights is not None:
                self.best_weights = model_weights.copy()
                
        else:
            self.wait += 1
            
        # Check patience
        if self.wait >= self.patience:
            self.stopped_epoch = epoch
            logger.info(
                f"Early stopping at epoch {epoch}. "
                f"Best loss: {self.best_loss:.4f} at epoch {self.best_epoch}"
            )
            return True
            
        return False
    
    def get_best_weights(self) -> Optional[dict]:
        """Get the best model weights."""
        return self.best_weights
    
    def reset(self):
        """Reset the early stopping state."""
        self.best_loss = float('inf')
        self.best_epoch = 0
        self.wait = 0
        self.stopped_epoch = 0
        self.best_weights = None


class ValidationLossTracker:
    """Track validation losses for early stopping decisions."""
    
    def __init__(self, window_size: int = 5):
        """
        Initialize loss tracker.
        
        Args:
            window_size: Size of moving window for loss averaging
        """
        self.window_size = window_size
        self.losses: List[float] = []
        
    def add_loss(self, loss: float):
        """Add a new loss value."""
        self.losses.append(loss)
        if len(self.losses) > self.window_size:
            self.losses.pop(0)
    
    def get_average_loss(self) -> float:
        """Get average loss over the window."""
        if not self.losses:
            return float('inf')
        return sum(self.losses) / len(self.losses)
    
    def is_improving(self, threshold: float = 0.001) -> bool:
        """Check if loss is improving over the window."""
        if len(self.losses) < 2:
            return True
            
        recent_avg = sum(self.losses[-2:]) / 2
        older_avg = sum(self.losses[:-2]) / max(1, len(self.losses) - 2)
        
        return recent_avg < older_avg - threshold
    
    def reset(self):
        """Reset the tracker."""
        self.losses.clear()
