"""Parameter search spaces for hyperparameter optimization."""

from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)

try:
    from hyperopt import hp
    HYPEROPT_AVAILABLE = True
except ImportError:
    HYPEROPT_AVAILABLE = False
    logger.warning("hyperopt not available. Install with: pip install hyperopt")


def get_parameter_space(model_name: str) -> Dict[str, Any]:
    """
    Get hyperparameter search space for a given model.
    
    Args:
        model_name: Name of the model ('lightgbm', 'xgboost', 'lstm')
        
    Returns:
        Dictionary defining the hyperparameter search space
        
    Raises:
        ValueError: If model_name is not supported
        ImportError: If hyperopt is not available
    """
    if not HYPEROPT_AVAILABLE:
        raise ImportError("hyperopt is required for parameter optimization")
    
    spaces = {
        'lightgbm': {
            'n_estimators': hp.choice('n_estimators', [100, 200, 500, 1000]),
            'learning_rate': hp.uniform('learning_rate', 0.01, 0.3),
            'num_leaves': hp.choice('num_leaves', [15, 31, 63, 127]),
            'max_depth': hp.choice('max_depth', [3, 5, 7, 10, -1]),
            'min_child_samples': hp.choice('min_child_samples', [10, 20, 50, 100]),
            'subsample': hp.uniform('subsample', 0.6, 1.0),
            'colsample_bytree': hp.uniform('colsample_bytree', 0.6, 1.0),
            'reg_alpha': hp.uniform('reg_alpha', 0.0, 1.0),
            'reg_lambda': hp.uniform('reg_lambda', 0.0, 1.0),
        },
        
        'xgboost': {
            'n_estimators': hp.choice('n_estimators', [100, 200, 500, 1000]),
            'learning_rate': hp.uniform('learning_rate', 0.01, 0.3),
            'max_depth': hp.choice('max_depth', [3, 5, 7, 10]),
            'min_child_weight': hp.choice('min_child_weight', [1, 3, 5, 10]),
            'subsample': hp.uniform('subsample', 0.6, 1.0),
            'colsample_bytree': hp.uniform('colsample_bytree', 0.6, 1.0),
            'reg_alpha': hp.uniform('reg_alpha', 0.0, 1.0),
            'reg_lambda': hp.uniform('reg_lambda', 0.0, 1.0),
            'gamma': hp.uniform('gamma', 0.0, 0.5),
        },
        
        'lstm': {
            'hidden_size': hp.choice('hidden_size', [32, 64, 128, 256]),
            'num_layers': hp.choice('num_layers', [1, 2, 3]),
            'dropout': hp.uniform('dropout', 0.0, 0.5),
            'learning_rate': hp.uniform('learning_rate', 0.0001, 0.01),
            'batch_size': hp.choice('batch_size', [16, 32, 64, 128]),
            'sequence_length': hp.choice('sequence_length', [10, 20, 30, 60]),
        }
    }
    
    if model_name not in spaces:
        raise ValueError(f"Unsupported model: {model_name}. Supported: {list(spaces.keys())}")
    
    return spaces[model_name]


def get_default_parameters(model_name: str) -> Dict[str, Any]:
    """
    Get default parameters for a model when optimization is not available.
    
    Args:
        model_name: Name of the model
        
    Returns:
        Dictionary of default parameters
    """
    defaults = {
        'lightgbm': {
            'n_estimators': 200,
            'learning_rate': 0.05,
            'num_leaves': 31,
            'max_depth': -1,
            'min_child_samples': 20,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'reg_alpha': 0.1,
            'reg_lambda': 0.1,
        },
        
        'xgboost': {
            'n_estimators': 200,
            'learning_rate': 0.05,
            'max_depth': 6,
            'min_child_weight': 1,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'reg_alpha': 0.1,
            'reg_lambda': 0.1,
            'gamma': 0.0,
        },
        
        'lstm': {
            'hidden_size': 64,
            'num_layers': 2,
            'dropout': 0.2,
            'learning_rate': 0.001,
            'batch_size': 32,
            'sequence_length': 20,
        }
    }
    
    return defaults.get(model_name, {})
