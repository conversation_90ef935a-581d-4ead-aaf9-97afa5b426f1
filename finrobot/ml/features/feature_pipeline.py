"""Feature engineering pipeline for FinRobot-Pro."""

import logging
import pickle
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler, RobustScaler, MinMaxScaler
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression

from ...config import get_config
from .technical_features import TechnicalFeatureEngineer
from .feature_store import FeatureStore

logger = logging.getLogger(__name__)


class FeaturePipeline:
    """Comprehensive feature engineering pipeline."""
    
    def __init__(self, feature_store: Optional[FeatureStore] = None):
        """Initialize feature pipeline.
        
        Args:
            feature_store: Optional feature store for persistence
        """
        self.config = get_config()
        self.feature_store = feature_store or FeatureStore()
        
        # Initialize feature engineers
        self.technical_engineer = TechnicalFeatureEngineer()
        
        # Scalers for different feature types
        self.scalers = {
            'standard': StandardScaler(),
            'robust': RobustScaler(),
            'minmax': MinMaxScaler()
        }
        
        # Feature selection
        self.feature_selector = None
        self.selected_features = None
        
        # Fitted scalers storage
        self.fitted_scalers = {}
        
        # Feature metadata
        self.feature_metadata = {}
    
    def build_features(self, data: pd.DataFrame, symbol: str = "") -> pd.DataFrame:
        """Build comprehensive feature set.
        
        Args:
            data: OHLCV DataFrame
            symbol: Symbol name for caching
            
        Returns:
            DataFrame with engineered features
        """
        logger.info(f"Building features for {symbol}")
        
        if data.empty:
            return pd.DataFrame()
        
        # Check cache first
        if symbol:
            cached_features = self.feature_store.get_features(symbol)
            if cached_features is not None:
                logger.debug(f"Using cached features for {symbol}")
                return cached_features
        
        # Build technical features
        features = self.technical_engineer.build_features(data)
        
        # Add lag features
        features = self._add_lag_features(features)
        
        # Add rolling window features
        features = self._add_rolling_features(features)
        
        # Add interaction features
        features = self._add_interaction_features(features)
        
        # Add target encoding features (if applicable)
        features = self._add_target_features(features)
        
        # Store feature metadata
        self._update_feature_metadata(features)
        
        # Cache features
        if symbol:
            self.feature_store.store_features(symbol, features)
        
        logger.info(f"Built {len(features.columns)} features for {symbol}")
        return features
    
    def _add_lag_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add lagged features."""
        lag_features = ['Close', 'Volume', 'returns_1d', 'volatility_20d', 'rsi']
        lag_periods = [1, 2, 3, 5, 10]
        
        for feature in lag_features:
            if feature in data.columns:
                for lag in lag_periods:
                    data[f'{feature}_lag_{lag}'] = data[feature].shift(lag)
        
        return data
    
    def _add_rolling_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add rolling window statistical features."""
        base_features = ['Close', 'Volume', 'returns_1d']
        windows = [5, 10, 20]
        
        for feature in base_features:
            if feature in data.columns:
                for window in windows:
                    # Rolling statistics
                    data[f'{feature}_roll_mean_{window}'] = data[feature].rolling(window).mean()
                    data[f'{feature}_roll_std_{window}'] = data[feature].rolling(window).std()
                    data[f'{feature}_roll_min_{window}'] = data[feature].rolling(window).min()
                    data[f'{feature}_roll_max_{window}'] = data[feature].rolling(window).max()
                    
                    # Rolling rank (percentile)
                    data[f'{feature}_roll_rank_{window}'] = data[feature].rolling(window).rank(pct=True)
        
        return data
    
    def _add_interaction_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add interaction features between key indicators."""
        # Price-Volume interactions
        if all(col in data.columns for col in ['returns_1d', 'Volume']):
            data['return_volume_interaction'] = data['returns_1d'] * np.log1p(data['Volume'])
        
        # Volatility-Momentum interactions
        if all(col in data.columns for col in ['volatility_20d', 'rsi']):
            data['volatility_rsi_interaction'] = data['volatility_20d'] * (data['rsi'] - 50) / 50
        
        # Trend-Momentum interactions
        if all(col in data.columns for col in ['macd', 'rsi']):
            data['macd_rsi_interaction'] = data['macd'] * (data['rsi'] - 50) / 50
        
        # Volume-Price position interaction
        if all(col in data.columns for col in ['Volume', 'price_position']):
            data['volume_position_interaction'] = np.log1p(data['Volume']) * data['price_position']
        
        return data
    
    def _add_target_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add target-based features for prediction."""
        if 'Close' not in data.columns:
            return data
        
        # Future returns (for training - will be NaN for prediction)
        data['target_1d'] = data['Close'].pct_change().shift(-1)
        data['target_5d'] = data['Close'].pct_change(5).shift(-5)
        data['target_20d'] = data['Close'].pct_change(20).shift(-20)
        
        # Binary targets (up/down)
        data['target_1d_binary'] = (data['target_1d'] > 0).astype(int)
        data['target_5d_binary'] = (data['target_5d'] > 0).astype(int)
        
        return data
    
    def _update_feature_metadata(self, features: pd.DataFrame) -> None:
        """Update feature metadata for tracking."""
        feature_groups = self.technical_engineer.get_feature_importance_groups()
        
        self.feature_metadata = {
            'total_features': len(features.columns),
            'feature_groups': feature_groups,
            'numeric_features': features.select_dtypes(include=[np.number]).columns.tolist(),
            'categorical_features': features.select_dtypes(include=['object', 'category']).columns.tolist(),
            'datetime_features': features.select_dtypes(include=['datetime64']).columns.tolist()
        }
    
    def prepare_features_for_ml(self, features: pd.DataFrame, 
                               target_column: str = 'target_1d',
                               scaler_type: str = 'robust',
                               feature_selection: bool = True,
                               n_features: Optional[int] = None) -> Tuple[pd.DataFrame, pd.Series]:
        """Prepare features for machine learning.
        
        Args:
            features: Feature DataFrame
            target_column: Target column name
            scaler_type: Type of scaler ('standard', 'robust', 'minmax')
            feature_selection: Whether to perform feature selection
            n_features: Number of features to select (if None, use default)
            
        Returns:
            Tuple of (X, y) for ML training
        """
        logger.info("Preparing features for ML")
        
        if features.empty:
            return pd.DataFrame(), pd.Series()
        
        # Separate features and target
        if target_column not in features.columns:
            raise ValueError(f"Target column '{target_column}' not found in features")
        
        y = features[target_column].copy()
        X = features.drop(columns=[col for col in features.columns if col.startswith('target_')])
        
        # Remove non-numeric columns
        numeric_columns = X.select_dtypes(include=[np.number]).columns
        X = X[numeric_columns]
        
        # Handle missing values
        X = X.fillna(method='ffill').fillna(method='bfill').fillna(0)
        y = y.fillna(method='ffill').fillna(method='bfill')
        
        # Remove rows where target is NaN
        valid_indices = ~y.isna()
        X = X[valid_indices]
        y = y[valid_indices]
        
        if len(X) == 0:
            logger.warning("No valid samples after preprocessing")
            return pd.DataFrame(), pd.Series()
        
        # Scale features
        if scaler_type in self.scalers:
            scaler_key = f"{scaler_type}_{target_column}"
            
            if scaler_key not in self.fitted_scalers:
                # Fit new scaler
                scaler = self.scalers[scaler_type].__class__()
                X_scaled = scaler.fit_transform(X)
                self.fitted_scalers[scaler_key] = scaler
                logger.info(f"Fitted new {scaler_type} scaler")
            else:
                # Use existing scaler
                scaler = self.fitted_scalers[scaler_key]
                X_scaled = scaler.transform(X)
            
            X = pd.DataFrame(X_scaled, columns=X.columns, index=X.index)
        
        # Feature selection
        if feature_selection:
            n_features = n_features or min(50, len(X.columns))
            
            if self.feature_selector is None or self.selected_features is None:
                # Fit new feature selector
                self.feature_selector = SelectKBest(
                    score_func=f_regression, 
                    k=min(n_features, len(X.columns))
                )
                X_selected = self.feature_selector.fit_transform(X, y)
                self.selected_features = X.columns[self.feature_selector.get_support()]
                logger.info(f"Selected {len(self.selected_features)} features")
            else:
                # Use existing feature selector
                available_features = [f for f in self.selected_features if f in X.columns]
                X_selected = X[available_features]
            
            X = pd.DataFrame(X_selected, columns=self.selected_features[:X_selected.shape[1]], index=X.index)
        
        logger.info(f"Prepared {len(X)} samples with {len(X.columns)} features")
        return X, y
    
    def transform_features(self, features: pd.DataFrame, 
                          scaler_type: str = 'robust',
                          target_column: str = 'target_1d') -> pd.DataFrame:
        """Transform features using fitted scalers and selectors.
        
        Args:
            features: Feature DataFrame
            scaler_type: Type of scaler used during training
            target_column: Target column used during training
            
        Returns:
            Transformed feature DataFrame
        """
        if features.empty:
            return pd.DataFrame()
        
        # Remove target columns
        X = features.drop(columns=[col for col in features.columns if col.startswith('target_')], errors='ignore')
        
        # Keep only numeric columns
        numeric_columns = X.select_dtypes(include=[np.number]).columns
        X = X[numeric_columns]
        
        # Handle missing values
        X = X.fillna(method='ffill').fillna(method='bfill').fillna(0)
        
        # Apply scaling
        scaler_key = f"{scaler_type}_{target_column}"
        if scaler_key in self.fitted_scalers:
            scaler = self.fitted_scalers[scaler_key]
            X_scaled = scaler.transform(X)
            X = pd.DataFrame(X_scaled, columns=X.columns, index=X.index)
        
        # Apply feature selection
        if self.selected_features is not None:
            available_features = [f for f in self.selected_features if f in X.columns]
            X = X[available_features]
        
        return X
    
    def save_pipeline(self, filepath: str) -> None:
        """Save the fitted pipeline to disk.
        
        Args:
            filepath: Path to save the pipeline
        """
        pipeline_data = {
            'fitted_scalers': self.fitted_scalers,
            'feature_selector': self.feature_selector,
            'selected_features': self.selected_features,
            'feature_metadata': self.feature_metadata
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(pipeline_data, f)
        
        logger.info(f"Pipeline saved to {filepath}")
    
    def load_pipeline(self, filepath: str) -> None:
        """Load a fitted pipeline from disk.
        
        Args:
            filepath: Path to load the pipeline from
        """
        with open(filepath, 'rb') as f:
            pipeline_data = pickle.load(f)
        
        self.fitted_scalers = pipeline_data.get('fitted_scalers', {})
        self.feature_selector = pipeline_data.get('feature_selector')
        self.selected_features = pipeline_data.get('selected_features')
        self.feature_metadata = pipeline_data.get('feature_metadata', {})
        
        logger.info(f"Pipeline loaded from {filepath}")
    
    def get_feature_importance(self) -> Optional[pd.DataFrame]:
        """Get feature importance from the fitted selector.
        
        Returns:
            DataFrame with feature importance scores
        """
        if self.feature_selector is None or self.selected_features is None:
            return None
        
        scores = self.feature_selector.scores_
        selected_indices = self.feature_selector.get_support(indices=True)
        
        importance_df = pd.DataFrame({
            'feature': self.selected_features,
            'importance_score': scores[selected_indices]
        }).sort_values('importance_score', ascending=False)
        
        return importance_df
