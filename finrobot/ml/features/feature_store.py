"""Feature store for FinRobot-Pro."""

import logging
from pathlib import Path
from typing import Dict, List, Optional, Union

import pandas as pd
import joblib

from ...config import get_config
from ...data.cache_manager import get_cache

logger = logging.getLogger(__name__)


class FeatureStore:
    """Feature store for caching and managing engineered features."""
    
    def __init__(self, storage_path: Optional[str] = None):
        """Initialize feature store.
        
        Args:
            storage_path: Path for persistent storage
        """
        self.config = get_config()
        self.cache = get_cache()
        
        # Setup storage
        self.storage_path = Path(storage_path or "./feature_store")
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # Feature metadata
        self.feature_metadata = {}
        
        logger.info(f"Feature store initialized at {self.storage_path}")
    
    def store_features(self, symbol: str, features: pd.DataFrame, 
                      metadata: Optional[Dict] = None) -> bool:
        """Store features for a symbol.
        
        Args:
            symbol: Stock symbol
            features: Feature DataFrame
            metadata: Optional metadata dictionary
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Cache in memory
            cache_key = f"features_{symbol}"
            self.cache.cache_dataframe(features, cache_key)
            
            # Store to disk (using pickle as fallback)
            feature_file = self.storage_path / f"{symbol}_features.pkl"
            features.to_pickle(feature_file)
            
            # Store metadata
            if metadata:
                metadata_file = self.storage_path / f"{symbol}_metadata.joblib"
                joblib.dump(metadata, metadata_file)
                self.feature_metadata[symbol] = metadata
            
            logger.debug(f"Stored features for {symbol}: {len(features)} records, {len(features.columns)} features")
            return True
            
        except Exception as e:
            logger.error(f"Error storing features for {symbol}: {e}")
            return False
    
    def get_features(self, symbol: str) -> Optional[pd.DataFrame]:
        """Get features for a symbol.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Feature DataFrame or None if not found
        """
        try:
            # Try cache first
            cache_key = f"features_{symbol}"
            cached_features = self.cache.get_dataframe(cache_key)
            if cached_features is not None:
                logger.debug(f"Retrieved cached features for {symbol}")
                return cached_features
            
            # Try disk storage
            feature_file = self.storage_path / f"{symbol}_features.pkl"
            if feature_file.exists():
                features = pd.read_pickle(feature_file)
                
                # Cache for future use
                self.cache.cache_dataframe(features, cache_key)
                
                logger.debug(f"Retrieved stored features for {symbol}")
                return features
            
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving features for {symbol}: {e}")
            return None
    
    def get_metadata(self, symbol: str) -> Optional[Dict]:
        """Get metadata for a symbol.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Metadata dictionary or None if not found
        """
        try:
            # Check in-memory cache
            if symbol in self.feature_metadata:
                return self.feature_metadata[symbol]
            
            # Try disk storage
            metadata_file = self.storage_path / f"{symbol}_metadata.joblib"
            if metadata_file.exists():
                metadata = joblib.load(metadata_file)
                self.feature_metadata[symbol] = metadata
                return metadata
            
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving metadata for {symbol}: {e}")
            return None
    
    def list_available_symbols(self) -> List[str]:
        """List all symbols with stored features.
        
        Returns:
            List of available symbols
        """
        try:
            feature_files = list(self.storage_path.glob("*_features.pkl"))
            symbols = [f.stem.replace("_features", "") for f in feature_files]
            return sorted(symbols)
        except Exception as e:
            logger.error(f"Error listing symbols: {e}")
            return []
    
    def delete_features(self, symbol: str) -> bool:
        """Delete features for a symbol.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Remove from cache
            cache_key = f"features_{symbol}"
            self.cache.delete(cache_key)
            
            # Remove from disk
            feature_file = self.storage_path / f"{symbol}_features.pkl"
            if feature_file.exists():
                feature_file.unlink()
            
            metadata_file = self.storage_path / f"{symbol}_metadata.joblib"
            if metadata_file.exists():
                metadata_file.unlink()
            
            # Remove from memory
            if symbol in self.feature_metadata:
                del self.feature_metadata[symbol]
            
            logger.info(f"Deleted features for {symbol}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting features for {symbol}: {e}")
            return False
    
    def get_feature_summary(self) -> Dict[str, Dict]:
        """Get summary of all stored features.
        
        Returns:
            Dictionary with feature summaries for each symbol
        """
        summary = {}
        
        for symbol in self.list_available_symbols():
            try:
                features = self.get_features(symbol)
                metadata = self.get_metadata(symbol)
                
                if features is not None:
                    summary[symbol] = {
                        'records': len(features),
                        'features': len(features.columns),
                        'start_date': features.index.min() if hasattr(features.index, 'min') else None,
                        'end_date': features.index.max() if hasattr(features.index, 'max') else None,
                        'metadata': metadata
                    }
            except Exception as e:
                logger.error(f"Error getting summary for {symbol}: {e}")
                summary[symbol] = {'error': str(e)}
        
        return summary
    
    def update_features(self, symbol: str, new_features: pd.DataFrame,
                       merge_strategy: str = 'update') -> bool:
        """Update existing features with new data.
        
        Args:
            symbol: Stock symbol
            new_features: New feature DataFrame
            merge_strategy: 'update', 'append', or 'replace'
            
        Returns:
            True if successful, False otherwise
        """
        try:
            existing_features = self.get_features(symbol)
            
            if existing_features is None:
                # No existing features, just store new ones
                return self.store_features(symbol, new_features)
            
            if merge_strategy == 'replace':
                updated_features = new_features
            elif merge_strategy == 'append':
                # Append new rows
                updated_features = pd.concat([existing_features, new_features])
                updated_features = updated_features[~updated_features.index.duplicated(keep='last')]
            elif merge_strategy == 'update':
                # Update existing rows and add new ones
                updated_features = existing_features.copy()
                
                # Update overlapping indices
                overlap_indices = existing_features.index.intersection(new_features.index)
                if len(overlap_indices) > 0:
                    updated_features.loc[overlap_indices] = new_features.loc[overlap_indices]
                
                # Add new indices
                new_indices = new_features.index.difference(existing_features.index)
                if len(new_indices) > 0:
                    updated_features = pd.concat([updated_features, new_features.loc[new_indices]])
            else:
                raise ValueError(f"Unknown merge strategy: {merge_strategy}")
            
            # Sort by index
            updated_features = updated_features.sort_index()
            
            return self.store_features(symbol, updated_features)
            
        except Exception as e:
            logger.error(f"Error updating features for {symbol}: {e}")
            return False
    
    def cleanup_old_features(self, days_old: int = 30) -> int:
        """Clean up old feature files.
        
        Args:
            days_old: Remove files older than this many days
            
        Returns:
            Number of files removed
        """
        try:
            from datetime import datetime, timedelta
            
            cutoff_date = datetime.now() - timedelta(days=days_old)
            removed_count = 0
            
            for feature_file in self.storage_path.glob("*_features.pkl"):
                if feature_file.stat().st_mtime < cutoff_date.timestamp():
                    symbol = feature_file.stem.replace("_features", "")
                    if self.delete_features(symbol):
                        removed_count += 1
            
            logger.info(f"Cleaned up {removed_count} old feature files")
            return removed_count
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
            return 0
    
    def export_features(self, symbol: str, format: str = 'csv') -> Optional[str]:
        """Export features to file.
        
        Args:
            symbol: Stock symbol
            format: Export format ('csv', 'json', 'excel')
            
        Returns:
            Path to exported file or None if failed
        """
        try:
            features = self.get_features(symbol)
            if features is None:
                return None
            
            export_path = self.storage_path / "exports"
            export_path.mkdir(exist_ok=True)
            
            if format == 'csv':
                file_path = export_path / f"{symbol}_features.csv"
                features.to_csv(file_path)
            elif format == 'json':
                file_path = export_path / f"{symbol}_features.json"
                features.to_json(file_path, orient='index', date_format='iso')
            elif format == 'excel':
                file_path = export_path / f"{symbol}_features.xlsx"
                features.to_excel(file_path)
            else:
                raise ValueError(f"Unsupported format: {format}")
            
            logger.info(f"Exported features for {symbol} to {file_path}")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"Error exporting features for {symbol}: {e}")
            return None
