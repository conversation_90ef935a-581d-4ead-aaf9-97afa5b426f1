"""Technical indicator feature engineering for FinRobot-Pro."""

import logging
from typing import Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
# import talib  # Commented out due to system dependencies
from scipy import stats

from ...config import get_config

logger = logging.getLogger(__name__)


class TechnicalFeatureEngineer:
    """Advanced technical indicator feature engineering."""
    
    def __init__(self):
        """Initialize technical feature engineer."""
        self.config = get_config()
        self.feature_config = self.config.features.technical_indicators
        
        # Configuration parameters
        self.volatility_windows = self.feature_config.get("volatility_windows", [5, 10, 20, 60])
        self.rsi_period = self.feature_config.get("rsi_period", 14)
        self.macd_fast = self.feature_config.get("macd_fast", 12)
        self.macd_slow = self.feature_config.get("macd_slow", 26)
        self.macd_signal = self.feature_config.get("macd_signal", 9)
        self.atr_period = self.feature_config.get("atr_period", 14)
        self.bollinger_period = self.feature_config.get("bollinger_period", 20)
        self.bollinger_std = self.feature_config.get("bollinger_std", 2)
    
    def build_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Build comprehensive technical features.
        
        Args:
            data: OHLCV DataFrame
            
        Returns:
            DataFrame with technical features
        """
        logger.info("Building technical features")
        
        if data.empty:
            return pd.DataFrame()
        
        features = data.copy()
        
        # Price-based features
        features = self._add_price_features(features)
        
        # Volatility features
        features = self._add_volatility_features(features)
        
        # Momentum indicators
        features = self._add_momentum_features(features)
        
        # Trend indicators
        features = self._add_trend_features(features)
        
        # Volume indicators
        features = self._add_volume_features(features)
        
        # Statistical features
        features = self._add_statistical_features(features)
        
        # Market microstructure features
        features = self._add_microstructure_features(features)
        
        logger.info(f"Generated {len(features.columns) - len(data.columns)} technical features")
        return features
    
    def _add_price_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add price-based features."""
        if 'Close' not in data.columns:
            return data
        
        # Returns
        data['returns_1d'] = data['Close'].pct_change()
        data['returns_5d'] = data['Close'].pct_change(5)
        data['returns_20d'] = data['Close'].pct_change(20)
        
        # Log returns
        data['log_returns_1d'] = np.log(data['Close'] / data['Close'].shift(1))
        
        # Price ratios
        if all(col in data.columns for col in ['High', 'Low', 'Open']):
            data['high_low_ratio'] = data['High'] / data['Low']
            data['close_open_ratio'] = data['Close'] / data['Open']
            data['high_close_ratio'] = data['High'] / data['Close']
            data['low_close_ratio'] = data['Low'] / data['Close']
        
        # Price position within daily range
        if all(col in data.columns for col in ['High', 'Low']):
            data['price_position'] = (data['Close'] - data['Low']) / (data['High'] - data['Low'])
        
        return data
    
    def _add_volatility_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add volatility-based features."""
        if 'Close' not in data.columns:
            return data
        
        returns = data['Close'].pct_change()
        
        # Rolling volatility (standard deviation)
        for window in self.volatility_windows:
            data[f'volatility_{window}d'] = returns.rolling(window).std() * np.sqrt(252)
        
        # Parkinson volatility (using High-Low)
        if all(col in data.columns for col in ['High', 'Low']):
            hl_ratio = np.log(data['High'] / data['Low'])
            for window in self.volatility_windows:
                data[f'parkinson_vol_{window}d'] = np.sqrt(
                    hl_ratio.pow(2).rolling(window).mean() / (4 * np.log(2))
                ) * np.sqrt(252)
        
        # Garman-Klass volatility
        if all(col in data.columns for col in ['High', 'Low', 'Open', 'Close']):
            gk_vol = (
                0.5 * np.log(data['High'] / data['Low']).pow(2) -
                (2 * np.log(2) - 1) * np.log(data['Close'] / data['Open']).pow(2)
            )
            for window in self.volatility_windows:
                data[f'gk_volatility_{window}d'] = np.sqrt(
                    gk_vol.rolling(window).mean()
                ) * np.sqrt(252)
        
        # VIX-style volatility of volatility
        vol_20d = returns.rolling(20).std()
        data['vol_of_vol'] = vol_20d.rolling(20).std()
        
        # Downside volatility
        downside_returns = returns.where(returns < 0, 0)
        data['downside_volatility_20d'] = downside_returns.rolling(20).std() * np.sqrt(252)
        
        return data
    
    def _add_momentum_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add momentum indicators."""
        if 'Close' not in data.columns:
            return data

        close = data['Close']

        # RSI (simple implementation)
        data['rsi'] = self._calculate_rsi(close, self.rsi_period)

        # Stochastic oscillator
        if all(col in data.columns for col in ['High', 'Low']):
            data['stoch_k'] = self._calculate_stochastic_k(data, 14)
            data['stoch_d'] = data['stoch_k'].rolling(3).mean()

        # Williams %R
        if all(col in data.columns for col in ['High', 'Low']):
            data['williams_r'] = self._calculate_williams_r(data, 14)

        # Rate of Change
        data['roc_10d'] = close.pct_change(10) * 100
        data['roc_20d'] = close.pct_change(20) * 100

        # Momentum
        data['momentum_10d'] = close - close.shift(10)

        # Commodity Channel Index (simplified)
        if all(col in data.columns for col in ['High', 'Low']):
            data['cci'] = self._calculate_cci(data, 20)

        return data
    
    def _add_trend_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add trend indicators."""
        if 'Close' not in data.columns:
            return data

        close = data['Close']

        # Moving averages
        data['sma_5'] = close.rolling(5).mean()
        data['sma_10'] = close.rolling(10).mean()
        data['sma_20'] = close.rolling(20).mean()
        data['sma_50'] = close.rolling(50).mean()
        data['sma_200'] = close.rolling(200).mean()

        # Exponential moving averages
        data['ema_12'] = close.ewm(span=12).mean()
        data['ema_26'] = close.ewm(span=26).mean()

        # MACD
        ema_fast = close.ewm(span=self.macd_fast).mean()
        ema_slow = close.ewm(span=self.macd_slow).mean()
        data['macd'] = ema_fast - ema_slow
        data['macd_signal'] = data['macd'].ewm(span=self.macd_signal).mean()
        data['macd_hist'] = data['macd'] - data['macd_signal']

        # Bollinger Bands
        sma = close.rolling(self.bollinger_period).mean()
        std = close.rolling(self.bollinger_period).std()
        data['bb_upper'] = sma + (std * self.bollinger_std)
        data['bb_lower'] = sma - (std * self.bollinger_std)
        data['bb_middle'] = sma
        data['bb_width'] = (data['bb_upper'] - data['bb_lower']) / data['bb_middle']
        data['bb_position'] = (data['Close'] - data['bb_lower']) / (data['bb_upper'] - data['bb_lower'])

        # Average Directional Index (simplified)
        if all(col in data.columns for col in ['High', 'Low']):
            data['adx'] = self._calculate_adx(data, 14)
            data['plus_di'] = self._calculate_plus_di(data, 14)
            data['minus_di'] = self._calculate_minus_di(data, 14)

        # Parabolic SAR (simplified - just use a simple trend indicator)
        if all(col in data.columns for col in ['High', 'Low']):
            data['sar'] = self._calculate_simple_sar(data)

        # Price relative to moving averages
        for period in [5, 10, 20, 50, 200]:
            if f'sma_{period}' in data.columns:
                data[f'price_sma_{period}_ratio'] = data['Close'] / data[f'sma_{period}']

        return data
    
    def _add_volume_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add volume-based indicators."""
        if 'Volume' not in data.columns:
            return data

        volume = data['Volume']

        # Volume moving averages
        data['volume_sma_10'] = volume.rolling(10).mean()
        data['volume_sma_20'] = volume.rolling(20).mean()

        # Volume ratio
        data['volume_ratio_10d'] = data['Volume'] / data['volume_sma_10']
        data['volume_ratio_20d'] = data['Volume'] / data['volume_sma_20']

        # On-Balance Volume
        data['obv'] = self._calculate_obv(data)

        # Volume Price Trend
        data['vpt'] = self._calculate_vpt(data)

        # Chaikin Money Flow
        if all(col in data.columns for col in ['High', 'Low']):
            data['cmf'] = self._calculate_cmf(data, 20)

        # Volume-weighted average price
        if all(col in data.columns for col in ['High', 'Low']):
            typical_price = (data['High'] + data['Low'] + data['Close']) / 3
            data['vwap'] = (typical_price * data['Volume']).rolling(20).sum() / data['Volume'].rolling(20).sum()

        return data
    
    def _add_statistical_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add statistical features."""
        if 'Close' not in data.columns:
            return data
        
        returns = data['Close'].pct_change()
        
        # Rolling statistics
        for window in [5, 10, 20]:
            data[f'returns_mean_{window}d'] = returns.rolling(window).mean()
            data[f'returns_std_{window}d'] = returns.rolling(window).std()
            data[f'returns_skew_{window}d'] = returns.rolling(window).skew()
            data[f'returns_kurt_{window}d'] = returns.rolling(window).kurt()
        
        # Z-score of returns
        data['returns_zscore_20d'] = (
            (returns - returns.rolling(20).mean()) / returns.rolling(20).std()
        )
        
        # Percentile rank
        data['price_percentile_252d'] = data['Close'].rolling(252).rank(pct=True)
        
        return data
    
    def _add_microstructure_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add market microstructure features."""
        if not all(col in data.columns for col in ['High', 'Low', 'Open', 'Close']):
            return data

        # Average True Range
        data['true_range'] = self._calculate_true_range(data)
        data['atr'] = data['true_range'].rolling(self.atr_period).mean()
        data['atr_ratio'] = data['atr'] / data['Close']
        
        # Intraday returns
        data['intraday_return'] = (data['Close'] - data['Open']) / data['Open']
        data['overnight_return'] = (data['Open'] - data['Close'].shift(1)) / data['Close'].shift(1)
        
        # Gap analysis
        data['gap_up'] = (data['Open'] > data['Close'].shift(1)).astype(int)
        data['gap_down'] = (data['Open'] < data['Close'].shift(1)).astype(int)
        data['gap_size'] = (data['Open'] - data['Close'].shift(1)) / data['Close'].shift(1)
        
        # Candlestick patterns (simplified)
        body_size = abs(data['Close'] - data['Open'])
        upper_shadow = data['High'] - np.maximum(data['Open'], data['Close'])
        lower_shadow = np.minimum(data['Open'], data['Close']) - data['Low']
        
        data['body_size_ratio'] = body_size / (data['High'] - data['Low'])
        data['upper_shadow_ratio'] = upper_shadow / (data['High'] - data['Low'])
        data['lower_shadow_ratio'] = lower_shadow / (data['High'] - data['Low'])
        
        # Doji pattern (small body relative to range)
        data['is_doji'] = (body_size / (data['High'] - data['Low']) < 0.1).astype(int)
        
        return data
    
    def get_feature_importance_groups(self) -> Dict[str, List[str]]:
        """Get feature groups for importance analysis.
        
        Returns:
            Dictionary mapping group names to feature lists
        """
        return {
            "price": ["returns_1d", "returns_5d", "returns_20d", "log_returns_1d"],
            "volatility": [f"volatility_{w}d" for w in self.volatility_windows] +
                         ["vol_of_vol", "downside_volatility_20d"],
            "momentum": ["rsi", "stoch_k", "stoch_d", "williams_r", "roc_10d", "roc_20d", "momentum_10d"],
            "trend": ["sma_5", "sma_10", "sma_20", "sma_50", "sma_200", "macd", "macd_signal", "adx"],
            "volume": ["volume_ratio_10d", "volume_ratio_20d", "obv", "vpt", "cmf", "vwap"],
            "microstructure": ["atr", "atr_ratio", "intraday_return", "overnight_return", "gap_size"],
            "statistical": ["returns_zscore_20d", "price_percentile_252d"]
        }

    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI indicator."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def _calculate_stochastic_k(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate Stochastic %K."""
        low_min = data['Low'].rolling(window=period).min()
        high_max = data['High'].rolling(window=period).max()
        k_percent = 100 * ((data['Close'] - low_min) / (high_max - low_min))
        return k_percent

    def _calculate_williams_r(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate Williams %R."""
        high_max = data['High'].rolling(window=period).max()
        low_min = data['Low'].rolling(window=period).min()
        wr = -100 * ((high_max - data['Close']) / (high_max - low_min))
        return wr

    def _calculate_cci(self, data: pd.DataFrame, period: int = 20) -> pd.Series:
        """Calculate Commodity Channel Index."""
        typical_price = (data['High'] + data['Low'] + data['Close']) / 3
        sma = typical_price.rolling(window=period).mean()
        mad = typical_price.rolling(window=period).apply(lambda x: np.mean(np.abs(x - x.mean())))
        cci = (typical_price - sma) / (0.015 * mad)
        return cci

    def _calculate_adx(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate Average Directional Index (simplified)."""
        high = data['High']
        low = data['Low']
        close = data['Close']

        # True Range
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

        # Directional Movement
        dm_plus = high.diff()
        dm_minus = -low.diff()

        dm_plus[dm_plus < 0] = 0
        dm_minus[dm_minus < 0] = 0

        # Smooth the values
        tr_smooth = tr.rolling(period).mean()
        dm_plus_smooth = dm_plus.rolling(period).mean()
        dm_minus_smooth = dm_minus.rolling(period).mean()

        # Directional Indicators
        di_plus = 100 * dm_plus_smooth / tr_smooth
        di_minus = 100 * dm_minus_smooth / tr_smooth

        # ADX
        dx = 100 * abs(di_plus - di_minus) / (di_plus + di_minus)
        adx = dx.rolling(period).mean()

        return adx

    def _calculate_plus_di(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate +DI."""
        # Simplified implementation
        high = data['High']
        low = data['Low']
        close = data['Close']

        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

        dm_plus = high.diff()
        dm_plus[dm_plus < 0] = 0

        return 100 * dm_plus.rolling(period).mean() / tr.rolling(period).mean()

    def _calculate_minus_di(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate -DI."""
        # Simplified implementation
        high = data['High']
        low = data['Low']
        close = data['Close']

        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

        dm_minus = -low.diff()
        dm_minus[dm_minus < 0] = 0

        return 100 * dm_minus.rolling(period).mean() / tr.rolling(period).mean()

    def _calculate_simple_sar(self, data: pd.DataFrame) -> pd.Series:
        """Calculate simplified SAR (just use EMA as proxy)."""
        # This is a very simplified version - real SAR is more complex
        return data['Close'].ewm(span=20).mean()

    def _calculate_obv(self, data: pd.DataFrame) -> pd.Series:
        """Calculate On-Balance Volume."""
        close = data['Close']
        volume = data['Volume']

        obv = pd.Series(index=data.index, dtype=float)
        obv.iloc[0] = volume.iloc[0]

        for i in range(1, len(data)):
            if close.iloc[i] > close.iloc[i-1]:
                obv.iloc[i] = obv.iloc[i-1] + volume.iloc[i]
            elif close.iloc[i] < close.iloc[i-1]:
                obv.iloc[i] = obv.iloc[i-1] - volume.iloc[i]
            else:
                obv.iloc[i] = obv.iloc[i-1]

        return obv

    def _calculate_vpt(self, data: pd.DataFrame) -> pd.Series:
        """Calculate Volume Price Trend."""
        close = data['Close']
        volume = data['Volume']

        price_change = close.pct_change()
        vpt = (price_change * volume).cumsum()

        return vpt

    def _calculate_cmf(self, data: pd.DataFrame, period: int = 20) -> pd.Series:
        """Calculate Chaikin Money Flow."""
        high = data['High']
        low = data['Low']
        close = data['Close']
        volume = data['Volume']

        # Money Flow Multiplier
        mfm = ((close - low) - (high - close)) / (high - low)
        mfm = mfm.fillna(0)  # Handle division by zero

        # Money Flow Volume
        mfv = mfm * volume

        # Chaikin Money Flow
        cmf = mfv.rolling(period).sum() / volume.rolling(period).sum()

        return cmf

    def _calculate_true_range(self, data: pd.DataFrame) -> pd.Series:
        """Calculate True Range."""
        high = data['High']
        low = data['Low']
        close = data['Close']

        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))

        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        return true_range
