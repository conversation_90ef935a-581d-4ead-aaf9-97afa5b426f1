"""Technical indicator feature engineering for FinRobot-Pro."""

import logging
from typing import Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
import talib
from scipy import stats

from ...config import get_config

logger = logging.getLogger(__name__)


class TechnicalFeatureEngineer:
    """Advanced technical indicator feature engineering."""
    
    def __init__(self):
        """Initialize technical feature engineer."""
        self.config = get_config()
        self.feature_config = self.config.features.technical_indicators
        
        # Configuration parameters
        self.volatility_windows = self.feature_config.get("volatility_windows", [5, 10, 20, 60])
        self.rsi_period = self.feature_config.get("rsi_period", 14)
        self.macd_fast = self.feature_config.get("macd_fast", 12)
        self.macd_slow = self.feature_config.get("macd_slow", 26)
        self.macd_signal = self.feature_config.get("macd_signal", 9)
        self.atr_period = self.feature_config.get("atr_period", 14)
        self.bollinger_period = self.feature_config.get("bollinger_period", 20)
        self.bollinger_std = self.feature_config.get("bollinger_std", 2)
    
    def build_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Build comprehensive technical features.
        
        Args:
            data: OHLCV DataFrame
            
        Returns:
            DataFrame with technical features
        """
        logger.info("Building technical features")
        
        if data.empty:
            return pd.DataFrame()
        
        features = data.copy()
        
        # Price-based features
        features = self._add_price_features(features)
        
        # Volatility features
        features = self._add_volatility_features(features)
        
        # Momentum indicators
        features = self._add_momentum_features(features)
        
        # Trend indicators
        features = self._add_trend_features(features)
        
        # Volume indicators
        features = self._add_volume_features(features)
        
        # Statistical features
        features = self._add_statistical_features(features)
        
        # Market microstructure features
        features = self._add_microstructure_features(features)
        
        logger.info(f"Generated {len(features.columns) - len(data.columns)} technical features")
        return features
    
    def _add_price_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add price-based features."""
        if 'Close' not in data.columns:
            return data
        
        # Returns
        data['returns_1d'] = data['Close'].pct_change()
        data['returns_5d'] = data['Close'].pct_change(5)
        data['returns_20d'] = data['Close'].pct_change(20)
        
        # Log returns
        data['log_returns_1d'] = np.log(data['Close'] / data['Close'].shift(1))
        
        # Price ratios
        if all(col in data.columns for col in ['High', 'Low', 'Open']):
            data['high_low_ratio'] = data['High'] / data['Low']
            data['close_open_ratio'] = data['Close'] / data['Open']
            data['high_close_ratio'] = data['High'] / data['Close']
            data['low_close_ratio'] = data['Low'] / data['Close']
        
        # Price position within daily range
        if all(col in data.columns for col in ['High', 'Low']):
            data['price_position'] = (data['Close'] - data['Low']) / (data['High'] - data['Low'])
        
        return data
    
    def _add_volatility_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add volatility-based features."""
        if 'Close' not in data.columns:
            return data
        
        returns = data['Close'].pct_change()
        
        # Rolling volatility (standard deviation)
        for window in self.volatility_windows:
            data[f'volatility_{window}d'] = returns.rolling(window).std() * np.sqrt(252)
        
        # Parkinson volatility (using High-Low)
        if all(col in data.columns for col in ['High', 'Low']):
            hl_ratio = np.log(data['High'] / data['Low'])
            for window in self.volatility_windows:
                data[f'parkinson_vol_{window}d'] = np.sqrt(
                    hl_ratio.pow(2).rolling(window).mean() / (4 * np.log(2))
                ) * np.sqrt(252)
        
        # Garman-Klass volatility
        if all(col in data.columns for col in ['High', 'Low', 'Open', 'Close']):
            gk_vol = (
                0.5 * np.log(data['High'] / data['Low']).pow(2) -
                (2 * np.log(2) - 1) * np.log(data['Close'] / data['Open']).pow(2)
            )
            for window in self.volatility_windows:
                data[f'gk_volatility_{window}d'] = np.sqrt(
                    gk_vol.rolling(window).mean()
                ) * np.sqrt(252)
        
        # VIX-style volatility of volatility
        vol_20d = returns.rolling(20).std()
        data['vol_of_vol'] = vol_20d.rolling(20).std()
        
        # Downside volatility
        downside_returns = returns.where(returns < 0, 0)
        data['downside_volatility_20d'] = downside_returns.rolling(20).std() * np.sqrt(252)
        
        return data
    
    def _add_momentum_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add momentum indicators."""
        if 'Close' not in data.columns:
            return data
        
        close = data['Close'].values
        
        # RSI
        data['rsi'] = talib.RSI(close, timeperiod=self.rsi_period)
        
        # Stochastic oscillator
        if all(col in data.columns for col in ['High', 'Low']):
            high = data['High'].values
            low = data['Low'].values
            data['stoch_k'], data['stoch_d'] = talib.STOCH(high, low, close)
        
        # Williams %R
        if all(col in data.columns for col in ['High', 'Low']):
            data['williams_r'] = talib.WILLR(high, low, close)
        
        # Rate of Change
        data['roc_10d'] = talib.ROC(close, timeperiod=10)
        data['roc_20d'] = talib.ROC(close, timeperiod=20)
        
        # Momentum
        data['momentum_10d'] = talib.MOM(close, timeperiod=10)
        
        # Commodity Channel Index
        if all(col in data.columns for col in ['High', 'Low']):
            data['cci'] = talib.CCI(high, low, close)
        
        return data
    
    def _add_trend_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add trend indicators."""
        if 'Close' not in data.columns:
            return data
        
        close = data['Close'].values
        
        # Moving averages
        data['sma_5'] = talib.SMA(close, timeperiod=5)
        data['sma_10'] = talib.SMA(close, timeperiod=10)
        data['sma_20'] = talib.SMA(close, timeperiod=20)
        data['sma_50'] = talib.SMA(close, timeperiod=50)
        data['sma_200'] = talib.SMA(close, timeperiod=200)
        
        # Exponential moving averages
        data['ema_12'] = talib.EMA(close, timeperiod=12)
        data['ema_26'] = talib.EMA(close, timeperiod=26)
        
        # MACD
        data['macd'], data['macd_signal'], data['macd_hist'] = talib.MACD(
            close, fastperiod=self.macd_fast, slowperiod=self.macd_slow, signalperiod=self.macd_signal
        )
        
        # Bollinger Bands
        data['bb_upper'], data['bb_middle'], data['bb_lower'] = talib.BBANDS(
            close, timeperiod=self.bollinger_period, nbdevup=self.bollinger_std, 
            nbdevdn=self.bollinger_std
        )
        data['bb_width'] = (data['bb_upper'] - data['bb_lower']) / data['bb_middle']
        data['bb_position'] = (data['Close'] - data['bb_lower']) / (data['bb_upper'] - data['bb_lower'])
        
        # Average Directional Index
        if all(col in data.columns for col in ['High', 'Low']):
            high = data['High'].values
            low = data['Low'].values
            data['adx'] = talib.ADX(high, low, close)
            data['plus_di'] = talib.PLUS_DI(high, low, close)
            data['minus_di'] = talib.MINUS_DI(high, low, close)
        
        # Parabolic SAR
        if all(col in data.columns for col in ['High', 'Low']):
            data['sar'] = talib.SAR(high, low)
        
        # Price relative to moving averages
        for period in [5, 10, 20, 50, 200]:
            if f'sma_{period}' in data.columns:
                data[f'price_sma_{period}_ratio'] = data['Close'] / data[f'sma_{period}']
        
        return data
    
    def _add_volume_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add volume-based indicators."""
        if 'Volume' not in data.columns:
            return data
        
        close = data['Close'].values
        volume = data['Volume'].values
        
        # Volume moving averages
        data['volume_sma_10'] = talib.SMA(volume, timeperiod=10)
        data['volume_sma_20'] = talib.SMA(volume, timeperiod=20)
        
        # Volume ratio
        data['volume_ratio_10d'] = data['Volume'] / data['volume_sma_10']
        data['volume_ratio_20d'] = data['Volume'] / data['volume_sma_20']
        
        # On-Balance Volume
        data['obv'] = talib.OBV(close, volume)
        
        # Volume Price Trend
        data['vpt'] = talib.AD(data['High'].values, data['Low'].values, close, volume)
        
        # Chaikin Money Flow
        if all(col in data.columns for col in ['High', 'Low']):
            high = data['High'].values
            low = data['Low'].values
            data['cmf'] = talib.ADOSC(high, low, close, volume)
        
        # Volume-weighted average price
        if all(col in data.columns for col in ['High', 'Low']):
            typical_price = (data['High'] + data['Low'] + data['Close']) / 3
            data['vwap'] = (typical_price * data['Volume']).rolling(20).sum() / data['Volume'].rolling(20).sum()
        
        return data
    
    def _add_statistical_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add statistical features."""
        if 'Close' not in data.columns:
            return data
        
        returns = data['Close'].pct_change()
        
        # Rolling statistics
        for window in [5, 10, 20]:
            data[f'returns_mean_{window}d'] = returns.rolling(window).mean()
            data[f'returns_std_{window}d'] = returns.rolling(window).std()
            data[f'returns_skew_{window}d'] = returns.rolling(window).skew()
            data[f'returns_kurt_{window}d'] = returns.rolling(window).kurt()
        
        # Z-score of returns
        data['returns_zscore_20d'] = (
            (returns - returns.rolling(20).mean()) / returns.rolling(20).std()
        )
        
        # Percentile rank
        data['price_percentile_252d'] = data['Close'].rolling(252).rank(pct=True)
        
        return data
    
    def _add_microstructure_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add market microstructure features."""
        if not all(col in data.columns for col in ['High', 'Low', 'Open', 'Close']):
            return data
        
        # Average True Range
        high = data['High'].values
        low = data['Low'].values
        close = data['Close'].values
        
        data['atr'] = talib.ATR(high, low, close, timeperiod=self.atr_period)
        data['atr_ratio'] = data['atr'] / data['Close']
        
        # True Range
        data['true_range'] = talib.TRANGE(high, low, close)
        
        # Intraday returns
        data['intraday_return'] = (data['Close'] - data['Open']) / data['Open']
        data['overnight_return'] = (data['Open'] - data['Close'].shift(1)) / data['Close'].shift(1)
        
        # Gap analysis
        data['gap_up'] = (data['Open'] > data['Close'].shift(1)).astype(int)
        data['gap_down'] = (data['Open'] < data['Close'].shift(1)).astype(int)
        data['gap_size'] = (data['Open'] - data['Close'].shift(1)) / data['Close'].shift(1)
        
        # Candlestick patterns (simplified)
        body_size = abs(data['Close'] - data['Open'])
        upper_shadow = data['High'] - np.maximum(data['Open'], data['Close'])
        lower_shadow = np.minimum(data['Open'], data['Close']) - data['Low']
        
        data['body_size_ratio'] = body_size / (data['High'] - data['Low'])
        data['upper_shadow_ratio'] = upper_shadow / (data['High'] - data['Low'])
        data['lower_shadow_ratio'] = lower_shadow / (data['High'] - data['Low'])
        
        # Doji pattern (small body relative to range)
        data['is_doji'] = (body_size / (data['High'] - data['Low']) < 0.1).astype(int)
        
        return data
    
    def get_feature_importance_groups(self) -> Dict[str, List[str]]:
        """Get feature groups for importance analysis.
        
        Returns:
            Dictionary mapping group names to feature lists
        """
        return {
            "price": ["returns_1d", "returns_5d", "returns_20d", "log_returns_1d"],
            "volatility": [f"volatility_{w}d" for w in self.volatility_windows] + 
                         ["vol_of_vol", "downside_volatility_20d"],
            "momentum": ["rsi", "stoch_k", "stoch_d", "williams_r", "roc_10d", "roc_20d", "momentum_10d"],
            "trend": ["sma_5", "sma_10", "sma_20", "sma_50", "sma_200", "macd", "macd_signal", "adx"],
            "volume": ["volume_ratio_10d", "volume_ratio_20d", "obv", "vpt", "cmf", "vwap"],
            "microstructure": ["atr", "atr_ratio", "intraday_return", "overnight_return", "gap_size"],
            "statistical": ["returns_zscore_20d", "price_percentile_252d"]
        }
