"""Ridge stacking ensemble implementation."""

import logging
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
import pandas as pd
from sklearn.linear_model import Ridge
from sklearn.model_selection import cross_val_score
from sklearn.metrics import mean_squared_error, mean_absolute_error

logger = logging.getLogger(__name__)


class StackingEnsemble:
    """
    Ridge stacking ensemble that combines multiple base models.
    
    Uses Ridge regression as the meta-learner to combine predictions
    from multiple base models, providing robust ensemble predictions.
    """
    
    def __init__(
        self,
        base_models: List[Any],
        meta_learner: Optional[Any] = None,
        cv_folds: int = 5,
        random_state: int = 42
    ):
        """
        Initialize stacking ensemble.
        
        Args:
            base_models: List of base models to ensemble
            meta_learner: Meta-learner model (default: Ridge regression)
            cv_folds: Number of cross-validation folds for stacking
            random_state: Random seed for reproducibility
        """
        self.base_models = base_models
        self.meta_learner = meta_learner or Ridge(alpha=1.0, random_state=random_state)
        self.cv_folds = cv_folds
        self.random_state = random_state
        
        self.is_fitted = False
        self.base_predictions_train = None
        self.feature_importance_ = None
        
    def fit(
        self,
        X: pd.DataFrame,
        y: pd.Series,
        X_val: Optional[pd.DataFrame] = None,
        y_val: Optional[pd.Series] = None
    ) -> 'StackingEnsemble':
        """
        Fit the stacking ensemble.
        
        Args:
            X: Training features
            y: Training targets
            X_val: Validation features (optional)
            y_val: Validation targets (optional)
            
        Returns:
            Self for method chaining
        """
        logger.info(f"Training stacking ensemble with {len(self.base_models)} base models")
        
        # Train base models and collect out-of-fold predictions
        base_predictions = np.zeros((len(X), len(self.base_models)))
        
        for i, model in enumerate(self.base_models):
            logger.info(f"Training base model {i+1}/{len(self.base_models)}: {type(model).__name__}")
            
            try:
                # Train the base model
                if X_val is not None and y_val is not None:
                    model.fit(X, y, X_val, y_val)
                else:
                    model.fit(X, y)
                
                # Get out-of-fold predictions using cross-validation
                oof_predictions = self._get_oof_predictions(model, X, y)
                base_predictions[:, i] = oof_predictions
                
                logger.info(f"Base model {i+1} trained successfully")
                
            except Exception as e:
                logger.error(f"Error training base model {i+1}: {e}")
                # Fill with zeros if model fails
                base_predictions[:, i] = 0
        
        # Store base predictions for analysis
        self.base_predictions_train = base_predictions
        
        # Train meta-learner on base model predictions
        logger.info("Training meta-learner")
        self.meta_learner.fit(base_predictions, y)
        
        # Calculate feature importance (contribution of each base model)
        if hasattr(self.meta_learner, 'coef_'):
            self.feature_importance_ = np.abs(self.meta_learner.coef_)
            self.feature_importance_ /= np.sum(self.feature_importance_)
        
        self.is_fitted = True
        logger.info("Stacking ensemble training completed")
        
        return self
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """
        Make predictions using the ensemble.
        
        Args:
            X: Features for prediction
            
        Returns:
            Ensemble predictions
        """
        if not self.is_fitted:
            raise ValueError("Ensemble must be fitted before making predictions")
        
        # Get predictions from all base models
        base_predictions = np.zeros((len(X), len(self.base_models)))
        
        for i, model in enumerate(self.base_models):
            try:
                base_predictions[:, i] = model.predict(X)
            except Exception as e:
                logger.warning(f"Base model {i+1} prediction failed: {e}")
                base_predictions[:, i] = 0
        
        # Use meta-learner to combine predictions
        ensemble_predictions = self.meta_learner.predict(base_predictions)
        
        return ensemble_predictions
    
    def _get_oof_predictions(
        self,
        model: Any,
        X: pd.DataFrame,
        y: pd.Series
    ) -> np.ndarray:
        """
        Get out-of-fold predictions for a base model.
        
        Args:
            model: Base model
            X: Training features
            y: Training targets
            
        Returns:
            Out-of-fold predictions
        """
        from sklearn.model_selection import KFold
        
        oof_predictions = np.zeros(len(X))
        kf = KFold(n_splits=self.cv_folds, shuffle=True, random_state=self.random_state)
        
        for fold, (train_idx, val_idx) in enumerate(kf.split(X)):
            X_fold_train = X.iloc[train_idx]
            y_fold_train = y.iloc[train_idx]
            X_fold_val = X.iloc[val_idx]
            
            # Create a copy of the model for this fold
            fold_model = type(model)(**model.get_params() if hasattr(model, 'get_params') else {})
            
            # Train on fold
            fold_model.fit(X_fold_train, y_fold_train)
            
            # Predict on validation fold
            oof_predictions[val_idx] = fold_model.predict(X_fold_val)
        
        return oof_predictions
    
    def get_model_weights(self) -> Optional[np.ndarray]:
        """
        Get the weights assigned to each base model.
        
        Returns:
            Array of model weights or None if not fitted
        """
        return self.feature_importance_
    
    def get_base_predictions(self) -> Optional[np.ndarray]:
        """
        Get the base model predictions used for training.
        
        Returns:
            Base model predictions or None if not fitted
        """
        return self.base_predictions_train
    
    def evaluate(
        self,
        X: pd.DataFrame,
        y: pd.Series
    ) -> Dict[str, float]:
        """
        Evaluate the ensemble performance.
        
        Args:
            X: Features
            y: True targets
            
        Returns:
            Dictionary of evaluation metrics
        """
        if not self.is_fitted:
            raise ValueError("Ensemble must be fitted before evaluation")
        
        predictions = self.predict(X)
        
        metrics = {
            'rmse': np.sqrt(mean_squared_error(y, predictions)),
            'mae': mean_absolute_error(y, predictions),
            'r2': 1 - np.sum((y - predictions) ** 2) / np.sum((y - np.mean(y)) ** 2)
        }
        
        # Calculate directional accuracy
        if len(y) > 1:
            y_diff = np.diff(y)
            pred_diff = np.diff(predictions)
            directional_accuracy = np.mean(np.sign(y_diff) == np.sign(pred_diff))
            metrics['directional_accuracy'] = directional_accuracy
        
        return metrics
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the ensemble.
        
        Returns:
            Dictionary with ensemble information
        """
        info = {
            'n_base_models': len(self.base_models),
            'meta_learner': type(self.meta_learner).__name__,
            'cv_folds': self.cv_folds,
            'is_fitted': self.is_fitted
        }
        
        if self.is_fitted and self.feature_importance_ is not None:
            info['model_weights'] = self.feature_importance_.tolist()
            info['dominant_model'] = int(np.argmax(self.feature_importance_))
        
        return info
