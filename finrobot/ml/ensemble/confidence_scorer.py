"""Confidence scoring for predictions and forecasts."""

import logging
from typing import Dict, Any, List, Optional, Tuple
import numpy as np
import pandas as pd
from sklearn.metrics import mean_squared_error

logger = logging.getLogger(__name__)


class ConfidenceScorer:
    """
    Calculate confidence scores for predictions based on multiple factors.
    
    Combines model uncertainty, prediction interval width, ensemble agreement,
    and historical performance to generate comprehensive confidence scores.
    """
    
    def __init__(
        self,
        uncertainty_weight: float = 0.3,
        interval_weight: float = 0.3,
        ensemble_weight: float = 0.2,
        performance_weight: float = 0.2
    ):
        """
        Initialize confidence scorer.
        
        Args:
            uncertainty_weight: Weight for model uncertainty component
            interval_weight: Weight for prediction interval width component
            ensemble_weight: Weight for ensemble agreement component
            performance_weight: Weight for historical performance component
        """
        # Normalize weights
        total_weight = uncertainty_weight + interval_weight + ensemble_weight + performance_weight
        
        self.uncertainty_weight = uncertainty_weight / total_weight
        self.interval_weight = interval_weight / total_weight
        self.ensemble_weight = ensemble_weight / total_weight
        self.performance_weight = performance_weight / total_weight
        
        self.historical_performance = None
        self.is_fitted = False
        
    def fit(
        self,
        y_true: np.ndar<PERSON>,
        y_pred: np.ndarray,
        prediction_intervals: Optional[Dict[str, Dict[str, np.ndarray]]] = None
    ) -> 'ConfidenceScorer':
        """
        Fit the confidence scorer on historical data.
        
        Args:
            y_true: True values
            y_pred: Predicted values
            prediction_intervals: Historical prediction intervals
            
        Returns:
            Self for method chaining
        """
        logger.info("Fitting confidence scorer")
        
        # Calculate historical performance metrics
        self.historical_performance = self._calculate_performance_metrics(y_true, y_pred)
        
        # Store interval statistics if available
        if prediction_intervals:
            self.historical_performance['interval_stats'] = self._analyze_intervals(
                prediction_intervals, y_true
            )
        
        self.is_fitted = True
        logger.info("Confidence scorer fitted successfully")
        
        return self
    
    def score_predictions(
        self,
        predictions: np.ndarray,
        prediction_intervals: Optional[Dict[str, Dict[str, np.ndarray]]] = None,
        ensemble_predictions: Optional[np.ndarray] = None,
        model_uncertainty: Optional[np.ndarray] = None
    ) -> np.ndarray:
        """
        Calculate confidence scores for predictions.
        
        Args:
            predictions: Point predictions
            prediction_intervals: Prediction intervals
            ensemble_predictions: Individual ensemble member predictions
            model_uncertainty: Model uncertainty estimates
            
        Returns:
            Array of confidence scores (0-1, higher is more confident)
        """
        if not self.is_fitted:
            logger.warning("Confidence scorer not fitted, using default scoring")
        
        n_predictions = len(predictions)
        confidence_scores = np.ones(n_predictions)  # Start with full confidence
        
        # Component 1: Model uncertainty
        if model_uncertainty is not None:
            uncertainty_component = self._score_uncertainty(model_uncertainty)
            confidence_scores *= (1 - self.uncertainty_weight) + self.uncertainty_weight * uncertainty_component
        
        # Component 2: Prediction interval width
        if prediction_intervals is not None:
            interval_component = self._score_intervals(prediction_intervals)
            confidence_scores *= (1 - self.interval_weight) + self.interval_weight * interval_component
        
        # Component 3: Ensemble agreement
        if ensemble_predictions is not None:
            ensemble_component = self._score_ensemble_agreement(ensemble_predictions, predictions)
            confidence_scores *= (1 - self.ensemble_weight) + self.ensemble_weight * ensemble_component
        
        # Component 4: Historical performance
        if self.is_fitted and self.historical_performance:
            performance_component = self._score_performance(predictions)
            confidence_scores *= (1 - self.performance_weight) + self.performance_weight * performance_component
        
        # Ensure scores are in [0, 1] range
        confidence_scores = np.clip(confidence_scores, 0, 1)
        
        return confidence_scores
    
    def _score_uncertainty(self, uncertainty: np.ndarray) -> np.ndarray:
        """Score based on model uncertainty (lower uncertainty = higher confidence)."""
        # Normalize uncertainty to [0, 1] range
        if np.max(uncertainty) > np.min(uncertainty):
            normalized_uncertainty = (uncertainty - np.min(uncertainty)) / (np.max(uncertainty) - np.min(uncertainty))
        else:
            normalized_uncertainty = np.zeros_like(uncertainty)
        
        # Convert to confidence (inverse of uncertainty)
        return 1 - normalized_uncertainty
    
    def _score_intervals(self, intervals: Dict[str, Dict[str, np.ndarray]]) -> np.ndarray:
        """Score based on prediction interval width (narrower intervals = higher confidence)."""
        # Use 80% intervals if available, otherwise use the first available
        interval_key = '80%' if '80%' in intervals else list(intervals.keys())[0]
        interval_widths = intervals[interval_key]['width']
        
        # Normalize widths to [0, 1] range
        if np.max(interval_widths) > np.min(interval_widths):
            normalized_widths = (interval_widths - np.min(interval_widths)) / (np.max(interval_widths) - np.min(interval_widths))
        else:
            normalized_widths = np.zeros_like(interval_widths)
        
        # Convert to confidence (inverse of width)
        return 1 - normalized_widths
    
    def _score_ensemble_agreement(
        self,
        ensemble_predictions: np.ndarray,
        final_predictions: np.ndarray
    ) -> np.ndarray:
        """Score based on ensemble member agreement (higher agreement = higher confidence)."""
        if ensemble_predictions.ndim == 1:
            # Single prediction, perfect agreement
            return np.ones_like(final_predictions)
        
        # Calculate standard deviation across ensemble members
        ensemble_std = np.std(ensemble_predictions, axis=1)
        
        # Normalize to [0, 1] range
        if np.max(ensemble_std) > np.min(ensemble_std):
            normalized_std = (ensemble_std - np.min(ensemble_std)) / (np.max(ensemble_std) - np.min(ensemble_std))
        else:
            normalized_std = np.zeros_like(ensemble_std)
        
        # Convert to confidence (inverse of disagreement)
        return 1 - normalized_std
    
    def _score_performance(self, predictions: np.ndarray) -> np.ndarray:
        """Score based on historical performance."""
        if not self.historical_performance:
            return np.ones_like(predictions)
        
        # Use historical accuracy as baseline confidence
        base_confidence = self.historical_performance.get('r2', 0.5)
        base_confidence = max(0.1, min(0.9, base_confidence))  # Clamp to reasonable range
        
        return np.full_like(predictions, base_confidence)
    
    def _calculate_performance_metrics(
        self,
        y_true: np.ndarray,
        y_pred: np.ndarray
    ) -> Dict[str, float]:
        """Calculate historical performance metrics."""
        metrics = {}
        
        try:
            # Basic metrics
            metrics['rmse'] = np.sqrt(mean_squared_error(y_true, y_pred))
            metrics['mae'] = np.mean(np.abs(y_true - y_pred))
            
            # R-squared
            ss_res = np.sum((y_true - y_pred) ** 2)
            ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
            metrics['r2'] = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0
            
            # Directional accuracy
            if len(y_true) > 1:
                y_true_diff = np.diff(y_true)
                y_pred_diff = np.diff(y_pred)
                metrics['directional_accuracy'] = np.mean(np.sign(y_true_diff) == np.sign(y_pred_diff))
            
        except Exception as e:
            logger.warning(f"Error calculating performance metrics: {e}")
            metrics = {'rmse': 1.0, 'mae': 1.0, 'r2': 0.0, 'directional_accuracy': 0.5}
        
        return metrics
    
    def _analyze_intervals(
        self,
        intervals: Dict[str, Dict[str, np.ndarray]],
        y_true: np.ndarray
    ) -> Dict[str, float]:
        """Analyze prediction interval performance."""
        stats = {}
        
        for confidence_level, interval_data in intervals.items():
            lower = interval_data['lower']
            upper = interval_data['upper']
            
            # Coverage
            coverage = np.mean((y_true >= lower) & (y_true <= upper))
            stats[f'coverage_{confidence_level}'] = coverage
            
            # Average width
            stats[f'width_{confidence_level}'] = np.mean(interval_data['width'])
        
        return stats
    
    def get_confidence_summary(
        self,
        confidence_scores: np.ndarray,
        predictions: np.ndarray
    ) -> Dict[str, Any]:
        """
        Get summary statistics for confidence scores.
        
        Args:
            confidence_scores: Calculated confidence scores
            predictions: Corresponding predictions
            
        Returns:
            Dictionary with confidence summary
        """
        summary = {
            'mean_confidence': float(np.mean(confidence_scores)),
            'std_confidence': float(np.std(confidence_scores)),
            'min_confidence': float(np.min(confidence_scores)),
            'max_confidence': float(np.max(confidence_scores)),
            'high_confidence_pct': float(np.mean(confidence_scores > 0.7)),
            'low_confidence_pct': float(np.mean(confidence_scores < 0.3))
        }
        
        # Confidence distribution
        summary['confidence_quartiles'] = {
            'q25': float(np.percentile(confidence_scores, 25)),
            'q50': float(np.percentile(confidence_scores, 50)),
            'q75': float(np.percentile(confidence_scores, 75))
        }
        
        return summary
    
    def get_scorer_info(self) -> Dict[str, Any]:
        """
        Get information about the confidence scorer configuration.
        
        Returns:
            Dictionary with scorer information
        """
        info = {
            'weights': {
                'uncertainty': self.uncertainty_weight,
                'interval': self.interval_weight,
                'ensemble': self.ensemble_weight,
                'performance': self.performance_weight
            },
            'is_fitted': self.is_fitted
        }
        
        if self.historical_performance:
            info['historical_performance'] = self.historical_performance.copy()
        
        return info
