"""Monte Carlo forecasting with student-t residual bootstrap."""

import logging
from typing import Dict, Any, Optional, <PERSON>ple
import numpy as np
import pandas as pd
from scipy import stats
from sklearn.metrics import mean_squared_error

logger = logging.getLogger(__name__)


class MonteCarloForecaster:
    """
    Monte Carlo forecasting system with student-t residual bootstrap.
    
    Generates probabilistic forecasts by bootstrapping residuals from
    a student-t distribution to capture fat tails common in financial data.
    """
    
    def __init__(
        self,
        n_simulations: int = 1000,
        confidence_levels: Tuple[float, float] = (0.8, 0.95),
        random_state: int = 42
    ):
        """
        Initialize Monte Carlo forecaster.
        
        Args:
            n_simulations: Number of Monte Carlo simulations
            confidence_levels: Confidence levels for prediction intervals
            random_state: Random seed for reproducibility
        """
        self.n_simulations = n_simulations
        self.confidence_levels = confidence_levels
        self.random_state = random_state
        
        self.residuals_params = None
        self.is_fitted = False
        
        # Set random seed
        np.random.seed(random_state)
        
    def fit(
        self,
        model: Any,
        X_train: pd.DataFrame,
        y_train: pd.Series,
        X_val: Optional[pd.DataFrame] = None,
        y_val: Optional[pd.Series] = None
    ) -> 'MonteCarloForecaster':
        """
        Fit the Monte Carlo forecaster.
        
        Args:
            model: Trained model for forecasting
            X_train: Training features
            y_train: Training targets
            X_val: Validation features (optional)
            y_val: Validation targets (optional)
            
        Returns:
            Self for method chaining
        """
        logger.info("Fitting Monte Carlo forecaster")
        
        self.model = model
        
        # Get model predictions on training data
        train_predictions = model.predict(X_train)
        
        # Calculate residuals
        residuals = y_train - train_predictions
        
        # Fit student-t distribution to residuals
        self.residuals_params = self._fit_student_t(residuals)
        
        logger.info(f"Fitted student-t distribution: df={self.residuals_params['df']:.2f}, "
                   f"loc={self.residuals_params['loc']:.4f}, scale={self.residuals_params['scale']:.4f}")
        
        self.is_fitted = True
        return self
    
    def forecast(
        self,
        X: pd.DataFrame,
        return_simulations: bool = False
    ) -> Dict[str, Any]:
        """
        Generate probabilistic forecasts.
        
        Args:
            X: Features for forecasting
            return_simulations: Whether to return all simulation paths
            
        Returns:
            Dictionary containing forecasts and prediction intervals
        """
        if not self.is_fitted:
            raise ValueError("Forecaster must be fitted before making forecasts")
        
        logger.info(f"Generating {self.n_simulations} Monte Carlo forecasts")
        
        # Get point predictions from the model
        point_predictions = self.model.predict(X)
        
        # Generate Monte Carlo simulations
        simulations = np.zeros((len(X), self.n_simulations))
        
        for i in range(self.n_simulations):
            # Sample residuals from student-t distribution
            residual_samples = stats.t.rvs(
                df=self.residuals_params['df'],
                loc=self.residuals_params['loc'],
                scale=self.residuals_params['scale'],
                size=len(X),
                random_state=self.random_state + i
            )
            
            # Add residuals to point predictions
            simulations[:, i] = point_predictions + residual_samples
        
        # Calculate prediction intervals
        prediction_intervals = {}
        for confidence_level in self.confidence_levels:
            alpha = 1 - confidence_level
            lower_percentile = (alpha / 2) * 100
            upper_percentile = (1 - alpha / 2) * 100
            
            lower_bound = np.percentile(simulations, lower_percentile, axis=1)
            upper_bound = np.percentile(simulations, upper_percentile, axis=1)
            
            prediction_intervals[f'{int(confidence_level*100)}%'] = {
                'lower': lower_bound,
                'upper': upper_bound,
                'width': upper_bound - lower_bound
            }
        
        # Calculate forecast statistics
        forecast_mean = np.mean(simulations, axis=1)
        forecast_std = np.std(simulations, axis=1)
        forecast_median = np.median(simulations, axis=1)
        
        result = {
            'point_forecast': point_predictions,
            'mean_forecast': forecast_mean,
            'median_forecast': forecast_median,
            'forecast_std': forecast_std,
            'prediction_intervals': prediction_intervals,
            'n_simulations': self.n_simulations
        }
        
        if return_simulations:
            result['simulations'] = simulations
        
        return result
    
    def _fit_student_t(self, residuals: pd.Series) -> Dict[str, float]:
        """
        Fit student-t distribution to residuals.
        
        Args:
            residuals: Model residuals
            
        Returns:
            Dictionary with distribution parameters
        """
        # Remove any NaN values
        clean_residuals = residuals.dropna()
        
        if len(clean_residuals) < 10:
            logger.warning("Insufficient residuals for distribution fitting, using normal approximation")
            return {
                'df': 30,  # High df approximates normal distribution
                'loc': float(clean_residuals.mean()),
                'scale': float(clean_residuals.std())
            }
        
        try:
            # Fit student-t distribution
            df, loc, scale = stats.t.fit(clean_residuals)
            
            # Ensure reasonable parameters
            df = max(df, 2.1)  # Ensure finite variance
            scale = max(scale, 1e-6)  # Ensure positive scale
            
            return {
                'df': float(df),
                'loc': float(loc),
                'scale': float(scale)
            }
            
        except Exception as e:
            logger.warning(f"Failed to fit student-t distribution: {e}, using normal approximation")
            return {
                'df': 30,
                'loc': float(clean_residuals.mean()),
                'scale': float(clean_residuals.std())
            }
    
    def evaluate_forecast_accuracy(
        self,
        forecasts: Dict[str, Any],
        y_true: pd.Series
    ) -> Dict[str, float]:
        """
        Evaluate forecast accuracy.
        
        Args:
            forecasts: Forecast results from forecast() method
            y_true: True values
            
        Returns:
            Dictionary of evaluation metrics
        """
        point_forecast = forecasts['point_forecast']
        mean_forecast = forecasts['mean_forecast']
        
        metrics = {
            'point_rmse': np.sqrt(mean_squared_error(y_true, point_forecast)),
            'mean_rmse': np.sqrt(mean_squared_error(y_true, mean_forecast)),
            'point_mae': np.mean(np.abs(y_true - point_forecast)),
            'mean_mae': np.mean(np.abs(y_true - mean_forecast))
        }
        
        # Calculate coverage for prediction intervals
        for confidence_level in self.confidence_levels:
            interval_key = f'{int(confidence_level*100)}%'
            if interval_key in forecasts['prediction_intervals']:
                interval = forecasts['prediction_intervals'][interval_key]
                coverage = np.mean(
                    (y_true >= interval['lower']) & (y_true <= interval['upper'])
                )
                metrics[f'coverage_{interval_key}'] = coverage
                
                # Calculate average interval width
                metrics[f'width_{interval_key}'] = np.mean(interval['width'])
        
        return metrics
    
    def get_forecast_summary(
        self,
        forecasts: Dict[str, Any],
        feature_names: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """
        Get a summary of forecasts in DataFrame format.
        
        Args:
            forecasts: Forecast results
            feature_names: Names for the forecast periods (optional)
            
        Returns:
            DataFrame with forecast summary
        """
        n_forecasts = len(forecasts['point_forecast'])
        
        if feature_names is None:
            feature_names = [f'Period_{i+1}' for i in range(n_forecasts)]
        
        summary_data = {
            'Period': feature_names,
            'Point_Forecast': forecasts['point_forecast'],
            'Mean_Forecast': forecasts['mean_forecast'],
            'Median_Forecast': forecasts['median_forecast'],
            'Std_Dev': forecasts['forecast_std']
        }
        
        # Add prediction intervals
        for confidence_level in self.confidence_levels:
            interval_key = f'{int(confidence_level*100)}%'
            if interval_key in forecasts['prediction_intervals']:
                interval = forecasts['prediction_intervals'][interval_key]
                summary_data[f'Lower_{interval_key}'] = interval['lower']
                summary_data[f'Upper_{interval_key}'] = interval['upper']
        
        return pd.DataFrame(summary_data)
    
    def get_residuals_info(self) -> Optional[Dict[str, float]]:
        """
        Get information about the fitted residuals distribution.
        
        Returns:
            Dictionary with residuals distribution parameters
        """
        return self.residuals_params
