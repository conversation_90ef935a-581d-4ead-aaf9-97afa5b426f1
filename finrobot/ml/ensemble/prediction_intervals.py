"""Prediction intervals calculation and validation."""

import logging
from typing import Dict, Any, <PERSON>, Tuple, Optional
import numpy as np
import pandas as pd
from scipy import stats

logger = logging.getLogger(__name__)


class PredictionIntervals:
    """
    Calculate and validate prediction intervals for forecasts.
    
    Supports multiple methods for interval calculation including
    parametric, non-parametric, and bootstrap approaches.
    """
    
    def __init__(
        self,
        confidence_levels: Tuple[float, ...] = (0.8, 0.95),
        method: str = 'student_t'
    ):
        """
        Initialize prediction intervals calculator.
        
        Args:
            confidence_levels: Confidence levels for intervals
            method: Method for interval calculation ('student_t', 'bootstrap', 'quantile')
        """
        self.confidence_levels = confidence_levels
        self.method = method
        
        self.residuals_stats = None
        self.is_fitted = False
        
    def fit(
        self,
        residuals: np.ndarray,
        predictions: Optional[np.ndarray] = None
    ) -> 'PredictionIntervals':
        """
        Fit the prediction intervals model.
        
        Args:
            residuals: Model residuals from training/validation
            predictions: Model predictions (for heteroscedastic intervals)
            
        Returns:
            Self for method chaining
        """
        logger.info(f"Fitting prediction intervals using {self.method} method")
        
        # Clean residuals
        clean_residuals = residuals[~np.isnan(residuals)]
        
        if len(clean_residuals) < 10:
            logger.warning("Insufficient residuals for interval fitting")
            self.residuals_stats = {'std': 1.0, 'df': 30}
            self.is_fitted = True
            return self
        
        if self.method == 'student_t':
            self.residuals_stats = self._fit_student_t(clean_residuals)
        elif self.method == 'bootstrap':
            self.residuals_stats = self._fit_bootstrap(clean_residuals)
        elif self.method == 'quantile':
            self.residuals_stats = self._fit_quantile(clean_residuals)
        else:
            raise ValueError(f"Unsupported method: {self.method}")
        
        self.is_fitted = True
        logger.info("Prediction intervals fitted successfully")
        
        return self
    
    def calculate_intervals(
        self,
        predictions: np.ndarray,
        forecast_horizon: int = 1
    ) -> Dict[str, Dict[str, np.ndarray]]:
        """
        Calculate prediction intervals for given predictions.
        
        Args:
            predictions: Point predictions
            forecast_horizon: Number of steps ahead (affects interval width)
            
        Returns:
            Dictionary with intervals for each confidence level
        """
        if not self.is_fitted:
            raise ValueError("Must fit intervals before calculation")
        
        intervals = {}
        
        for confidence_level in self.confidence_levels:
            alpha = 1 - confidence_level
            
            if self.method == 'student_t':
                lower, upper = self._calculate_student_t_intervals(
                    predictions, alpha, forecast_horizon
                )
            elif self.method == 'bootstrap':
                lower, upper = self._calculate_bootstrap_intervals(
                    predictions, alpha, forecast_horizon
                )
            elif self.method == 'quantile':
                lower, upper = self._calculate_quantile_intervals(
                    predictions, alpha, forecast_horizon
                )
            
            intervals[f'{int(confidence_level*100)}%'] = {
                'lower': lower,
                'upper': upper,
                'width': upper - lower
            }
        
        return intervals
    
    def _fit_student_t(self, residuals: np.ndarray) -> Dict[str, float]:
        """Fit student-t distribution to residuals."""
        try:
            df, loc, scale = stats.t.fit(residuals)
            df = max(df, 2.1)  # Ensure finite variance
            scale = max(scale, 1e-6)  # Ensure positive scale
            
            return {
                'df': float(df),
                'loc': float(loc),
                'scale': float(scale),
                'std': float(np.std(residuals))
            }
        except Exception as e:
            logger.warning(f"Student-t fitting failed: {e}")
            return {
                'df': 30.0,
                'loc': 0.0,
                'scale': float(np.std(residuals)),
                'std': float(np.std(residuals))
            }
    
    def _fit_bootstrap(self, residuals: np.ndarray) -> Dict[str, Any]:
        """Fit bootstrap distribution to residuals."""
        return {
            'residuals': residuals.copy(),
            'std': float(np.std(residuals)),
            'n_bootstrap': 1000
        }
    
    def _fit_quantile(self, residuals: np.ndarray) -> Dict[str, float]:
        """Fit quantile-based intervals to residuals."""
        quantiles = {}
        for confidence_level in self.confidence_levels:
            alpha = 1 - confidence_level
            lower_q = alpha / 2
            upper_q = 1 - alpha / 2
            
            quantiles[f'q_{lower_q:.3f}'] = float(np.quantile(residuals, lower_q))
            quantiles[f'q_{upper_q:.3f}'] = float(np.quantile(residuals, upper_q))
        
        quantiles['std'] = float(np.std(residuals))
        return quantiles
    
    def _calculate_student_t_intervals(
        self,
        predictions: np.ndarray,
        alpha: float,
        forecast_horizon: int
    ) -> Tuple[np.ndarray, np.ndarray]:
        """Calculate intervals using student-t distribution."""
        df = self.residuals_stats['df']
        scale = self.residuals_stats['scale']
        
        # Adjust for forecast horizon (uncertainty increases with horizon)
        adjusted_scale = scale * np.sqrt(forecast_horizon)
        
        # Calculate critical value
        t_critical = stats.t.ppf(1 - alpha/2, df)
        
        # Calculate intervals
        margin = t_critical * adjusted_scale
        lower = predictions - margin
        upper = predictions + margin
        
        return lower, upper
    
    def _calculate_bootstrap_intervals(
        self,
        predictions: np.ndarray,
        alpha: float,
        forecast_horizon: int
    ) -> Tuple[np.ndarray, np.ndarray]:
        """Calculate intervals using bootstrap resampling."""
        residuals = self.residuals_stats['residuals']
        n_bootstrap = self.residuals_stats['n_bootstrap']
        
        # Bootstrap samples
        bootstrap_predictions = np.zeros((len(predictions), n_bootstrap))
        
        for i in range(n_bootstrap):
            # Sample residuals with replacement
            sampled_residuals = np.random.choice(
                residuals, size=len(predictions), replace=True
            )
            # Adjust for forecast horizon
            sampled_residuals *= np.sqrt(forecast_horizon)
            bootstrap_predictions[:, i] = predictions + sampled_residuals
        
        # Calculate percentiles
        lower_percentile = (alpha / 2) * 100
        upper_percentile = (1 - alpha / 2) * 100
        
        lower = np.percentile(bootstrap_predictions, lower_percentile, axis=1)
        upper = np.percentile(bootstrap_predictions, upper_percentile, axis=1)
        
        return lower, upper
    
    def _calculate_quantile_intervals(
        self,
        predictions: np.ndarray,
        alpha: float,
        forecast_horizon: int
    ) -> Tuple[np.ndarray, np.ndarray]:
        """Calculate intervals using pre-computed quantiles."""
        lower_q = alpha / 2
        upper_q = 1 - alpha / 2
        
        lower_quantile = self.residuals_stats[f'q_{lower_q:.3f}']
        upper_quantile = self.residuals_stats[f'q_{upper_q:.3f}']
        
        # Adjust for forecast horizon
        adjustment = np.sqrt(forecast_horizon)
        
        lower = predictions + lower_quantile * adjustment
        upper = predictions + upper_quantile * adjustment
        
        return lower, upper
    
    def validate_coverage(
        self,
        intervals: Dict[str, Dict[str, np.ndarray]],
        y_true: np.ndarray
    ) -> Dict[str, float]:
        """
        Validate prediction interval coverage.
        
        Args:
            intervals: Calculated prediction intervals
            y_true: True values
            
        Returns:
            Dictionary with coverage statistics
        """
        coverage_stats = {}
        
        for confidence_level, interval_data in intervals.items():
            lower = interval_data['lower']
            upper = interval_data['upper']
            
            # Calculate empirical coverage
            coverage = np.mean((y_true >= lower) & (y_true <= upper))
            
            # Expected coverage
            expected_coverage = float(confidence_level.rstrip('%')) / 100
            
            coverage_stats[confidence_level] = {
                'empirical_coverage': coverage,
                'expected_coverage': expected_coverage,
                'coverage_error': coverage - expected_coverage,
                'average_width': np.mean(interval_data['width'])
            }
        
        return coverage_stats

    def get_interval_diagnostics(self) -> Dict[str, Any]:
        """
        Get diagnostic information about the fitted intervals.

        Returns:
            Dictionary with diagnostic information
        """
        if not self.is_fitted:
            return {'status': 'not_fitted'}

        diagnostics = {
            'method': self.method,
            'confidence_levels': self.confidence_levels,
            'is_fitted': self.is_fitted
        }

        if self.residuals_stats:
            diagnostics['residuals_stats'] = self.residuals_stats.copy()

        return diagnostics
