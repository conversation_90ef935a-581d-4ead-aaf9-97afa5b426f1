"""Value at Risk (VaR) calculation with multiple methods."""

import logging
from typing import Dict, Any, List, Optional, Tuple
import numpy as np
import pandas as pd
from scipy import stats
from sklearn.mixture import GaussianMixture

logger = logging.getLogger(__name__)


class VaRCalculator:
    """
    Value at Risk calculator supporting multiple methodologies.
    
    Implements parametric, historical simulation, and Monte Carlo VaR
    with support for GARCH volatility modeling and extreme value theory.
    """
    
    def __init__(
        self,
        confidence_levels: Tuple[float, ...] = (0.95, 0.99),
        lookback_window: int = 252,
        random_state: int = 42
    ):
        """
        Initialize VaR calculator.
        
        Args:
            confidence_levels: Confidence levels for VaR calculation
            lookback_window: Number of periods for historical data
            random_state: Random seed for reproducibility
        """
        self.confidence_levels = confidence_levels
        self.lookback_window = lookback_window
        self.random_state = random_state
        
        self.historical_returns = None
        self.distribution_params = None
        self.is_fitted = False
        
        np.random.seed(random_state)
        
    def fit(
        self,
        returns: pd.Series,
        method: str = 'historical'
    ) -> 'VaRCalculator':
        """
        Fit the VaR model to historical returns.
        
        Args:
            returns: Historical returns data
            method: VaR method ('historical', 'parametric', 'monte_carlo', 'garch')
            
        Returns:
            Self for method chaining
        """
        logger.info(f"Fitting VaR model using {method} method")
        
        # Clean and prepare data
        clean_returns = returns.dropna()
        if len(clean_returns) < self.lookback_window:
            logger.warning(f"Insufficient data: {len(clean_returns)} < {self.lookback_window}")
            self.lookback_window = max(30, len(clean_returns) // 2)
        
        # Use most recent data
        self.historical_returns = clean_returns.tail(self.lookback_window)
        
        # Fit distribution parameters based on method
        if method == 'parametric':
            self.distribution_params = self._fit_parametric_distribution()
        elif method == 'monte_carlo':
            self.distribution_params = self._fit_monte_carlo_params()
        elif method == 'garch':
            self.distribution_params = self._fit_garch_model()
        elif method == 'historical':
            self.distribution_params = {'method': 'historical'}
        else:
            raise ValueError(f"Unsupported VaR method: {method}")
        
        self.method = method
        self.is_fitted = True
        
        logger.info(f"VaR model fitted successfully with {len(self.historical_returns)} observations")
        return self
    
    def calculate_var(
        self,
        portfolio_value: float = 1.0,
        holding_period: int = 1
    ) -> Dict[str, Dict[str, float]]:
        """
        Calculate Value at Risk for given portfolio value and holding period.
        
        Args:
            portfolio_value: Current portfolio value
            holding_period: Holding period in days
            
        Returns:
            Dictionary with VaR estimates for each confidence level
        """
        if not self.is_fitted:
            raise ValueError("VaR model must be fitted before calculation")
        
        var_results = {}
        
        for confidence_level in self.confidence_levels:
            alpha = 1 - confidence_level
            
            if self.method == 'historical':
                var_estimate = self._calculate_historical_var(alpha, holding_period)
            elif self.method == 'parametric':
                var_estimate = self._calculate_parametric_var(alpha, holding_period)
            elif self.method == 'monte_carlo':
                var_estimate = self._calculate_monte_carlo_var(alpha, holding_period)
            elif self.method == 'garch':
                var_estimate = self._calculate_garch_var(alpha, holding_period)
            
            # Convert to portfolio value terms
            var_dollar = abs(var_estimate * portfolio_value)
            
            var_results[f'{int(confidence_level*100)}%'] = {
                'var_percent': var_estimate,
                'var_dollar': var_dollar,
                'confidence_level': confidence_level,
                'holding_period': holding_period
            }
        
        return var_results
    
    def calculate_expected_shortfall(
        self,
        portfolio_value: float = 1.0,
        holding_period: int = 1
    ) -> Dict[str, Dict[str, float]]:
        """
        Calculate Expected Shortfall (Conditional VaR).
        
        Args:
            portfolio_value: Current portfolio value
            holding_period: Holding period in days
            
        Returns:
            Dictionary with ES estimates for each confidence level
        """
        if not self.is_fitted:
            raise ValueError("VaR model must be fitted before ES calculation")
        
        es_results = {}
        
        for confidence_level in self.confidence_levels:
            alpha = 1 - confidence_level
            
            if self.method == 'historical':
                es_estimate = self._calculate_historical_es(alpha, holding_period)
            elif self.method == 'parametric':
                es_estimate = self._calculate_parametric_es(alpha, holding_period)
            elif self.method == 'monte_carlo':
                es_estimate = self._calculate_monte_carlo_es(alpha, holding_period)
            elif self.method == 'garch':
                es_estimate = self._calculate_garch_es(alpha, holding_period)
            
            # Convert to portfolio value terms
            es_dollar = abs(es_estimate * portfolio_value)
            
            es_results[f'{int(confidence_level*100)}%'] = {
                'es_percent': es_estimate,
                'es_dollar': es_dollar,
                'confidence_level': confidence_level,
                'holding_period': holding_period
            }
        
        return es_results
    
    def _fit_parametric_distribution(self) -> Dict[str, Any]:
        """Fit parametric distribution to returns."""
        returns = self.historical_returns.values
        
        # Try multiple distributions and select best fit
        distributions = ['norm', 't', 'skewnorm']
        best_dist = None
        best_params = None
        best_aic = float('inf')
        
        for dist_name in distributions:
            try:
                dist = getattr(stats, dist_name)
                params = dist.fit(returns)
                
                # Calculate AIC
                log_likelihood = np.sum(dist.logpdf(returns, *params))
                aic = 2 * len(params) - 2 * log_likelihood
                
                if aic < best_aic:
                    best_aic = aic
                    best_dist = dist_name
                    best_params = params
                    
            except Exception as e:
                logger.debug(f"Failed to fit {dist_name}: {e}")
                continue
        
        if best_dist is None:
            # Fallback to normal distribution
            best_dist = 'norm'
            best_params = stats.norm.fit(returns)
        
        logger.info(f"Best distribution: {best_dist} (AIC: {best_aic:.2f})")
        
        return {
            'distribution': best_dist,
            'params': best_params,
            'aic': best_aic
        }
    
    def _fit_monte_carlo_params(self) -> Dict[str, Any]:
        """Fit parameters for Monte Carlo simulation."""
        returns = self.historical_returns.values
        
        # Fit Gaussian Mixture Model for more flexible distribution
        try:
            gmm = GaussianMixture(n_components=2, random_state=self.random_state)
            gmm.fit(returns.reshape(-1, 1))
            
            return {
                'method': 'gmm',
                'model': gmm,
                'n_components': 2
            }
        except Exception as e:
            logger.warning(f"GMM fitting failed: {e}, using normal distribution")
            return {
                'method': 'normal',
                'mean': np.mean(returns),
                'std': np.std(returns)
            }
    
    def _fit_garch_model(self) -> Dict[str, Any]:
        """Fit GARCH model for volatility forecasting."""
        # Simplified GARCH implementation
        returns = self.historical_returns.values
        
        # Calculate rolling volatility as proxy for GARCH
        window = min(30, len(returns) // 3)
        rolling_vol = pd.Series(returns).rolling(window=window).std()
        
        return {
            'method': 'rolling_garch',
            'current_volatility': rolling_vol.iloc[-1],
            'mean_return': np.mean(returns),
            'volatility_series': rolling_vol.dropna().values
        }
    
    def _calculate_historical_var(self, alpha: float, holding_period: int) -> float:
        """Calculate historical simulation VaR."""
        returns = self.historical_returns.values
        
        # Adjust for holding period
        if holding_period > 1:
            returns = returns * np.sqrt(holding_period)
        
        # Calculate percentile
        var_estimate = np.percentile(returns, alpha * 100)
        return var_estimate
    
    def _calculate_parametric_var(self, alpha: float, holding_period: int) -> float:
        """Calculate parametric VaR."""
        dist_name = self.distribution_params['distribution']
        params = self.distribution_params['params']
        
        dist = getattr(stats, dist_name)
        var_estimate = dist.ppf(alpha, *params)
        
        # Adjust for holding period
        if holding_period > 1:
            var_estimate = var_estimate * np.sqrt(holding_period)
        
        return var_estimate
    
    def _calculate_monte_carlo_var(self, alpha: float, holding_period: int) -> float:
        """Calculate Monte Carlo VaR."""
        n_simulations = 10000
        
        if self.distribution_params['method'] == 'gmm':
            # Sample from Gaussian Mixture Model
            gmm = self.distribution_params['model']
            simulated_returns = gmm.sample(n_simulations)[0].flatten()
        else:
            # Sample from normal distribution
            mean = self.distribution_params['mean']
            std = self.distribution_params['std']
            simulated_returns = np.random.normal(mean, std, n_simulations)
        
        # Adjust for holding period
        if holding_period > 1:
            simulated_returns = simulated_returns * np.sqrt(holding_period)
        
        # Calculate VaR
        var_estimate = np.percentile(simulated_returns, alpha * 100)
        return var_estimate
    
    def _calculate_garch_var(self, alpha: float, holding_period: int) -> float:
        """Calculate GARCH-based VaR."""
        current_vol = self.distribution_params['current_volatility']
        mean_return = self.distribution_params['mean_return']
        
        # Adjust for holding period
        if holding_period > 1:
            current_vol = current_vol * np.sqrt(holding_period)
            mean_return = mean_return * holding_period
        
        # Assume normal distribution with GARCH volatility
        var_estimate = stats.norm.ppf(alpha, loc=mean_return, scale=current_vol)
        return var_estimate
    
    def _calculate_historical_es(self, alpha: float, holding_period: int) -> float:
        """Calculate historical Expected Shortfall."""
        returns = self.historical_returns.values
        
        # Adjust for holding period
        if holding_period > 1:
            returns = returns * np.sqrt(holding_period)
        
        # Calculate VaR threshold
        var_threshold = np.percentile(returns, alpha * 100)
        
        # Calculate ES as mean of returns below VaR
        tail_returns = returns[returns <= var_threshold]
        es_estimate = np.mean(tail_returns) if len(tail_returns) > 0 else var_threshold
        
        return es_estimate
    
    def _calculate_parametric_es(self, alpha: float, holding_period: int) -> float:
        """Calculate parametric Expected Shortfall."""
        dist_name = self.distribution_params['distribution']
        params = self.distribution_params['params']
        
        dist = getattr(stats, dist_name)
        
        # Calculate ES using truncated expectation
        var_threshold = dist.ppf(alpha, *params)
        
        # Numerical integration for ES
        x_values = np.linspace(dist.ppf(0.001, *params), var_threshold, 1000)
        pdf_values = dist.pdf(x_values, *params)
        es_estimate = np.trapz(x_values * pdf_values, x_values) / alpha
        
        # Adjust for holding period
        if holding_period > 1:
            es_estimate = es_estimate * np.sqrt(holding_period)
        
        return es_estimate
    
    def _calculate_monte_carlo_es(self, alpha: float, holding_period: int) -> float:
        """Calculate Monte Carlo Expected Shortfall."""
        n_simulations = 10000
        
        if self.distribution_params['method'] == 'gmm':
            gmm = self.distribution_params['model']
            simulated_returns = gmm.sample(n_simulations)[0].flatten()
        else:
            mean = self.distribution_params['mean']
            std = self.distribution_params['std']
            simulated_returns = np.random.normal(mean, std, n_simulations)
        
        # Adjust for holding period
        if holding_period > 1:
            simulated_returns = simulated_returns * np.sqrt(holding_period)
        
        # Calculate VaR threshold
        var_threshold = np.percentile(simulated_returns, alpha * 100)
        
        # Calculate ES
        tail_returns = simulated_returns[simulated_returns <= var_threshold]
        es_estimate = np.mean(tail_returns) if len(tail_returns) > 0 else var_threshold
        
        return es_estimate
    
    def _calculate_garch_es(self, alpha: float, holding_period: int) -> float:
        """Calculate GARCH-based Expected Shortfall."""
        current_vol = self.distribution_params['current_volatility']
        mean_return = self.distribution_params['mean_return']
        
        # Adjust for holding period
        if holding_period > 1:
            current_vol = current_vol * np.sqrt(holding_period)
            mean_return = mean_return * holding_period
        
        # Calculate ES for normal distribution
        var_threshold = stats.norm.ppf(alpha, loc=mean_return, scale=current_vol)
        
        # ES for normal distribution
        phi = stats.norm.pdf(stats.norm.ppf(alpha))
        es_estimate = mean_return - current_vol * phi / alpha
        
        return es_estimate
    
    def backtest_var(
        self,
        returns: pd.Series,
        portfolio_values: pd.Series,
        refit_frequency: int = 22
    ) -> Dict[str, Any]:
        """
        Backtest VaR model performance.
        
        Args:
            returns: Out-of-sample returns for backtesting
            portfolio_values: Portfolio values over time
            refit_frequency: How often to refit the model (in days)
            
        Returns:
            Dictionary with backtesting results
        """
        logger.info("Starting VaR backtesting")
        
        backtest_results = {}
        
        for confidence_level in self.confidence_levels:
            violations = []
            var_estimates = []
            
            for i in range(len(returns)):
                # Refit model periodically
                if i % refit_frequency == 0:
                    lookback_start = max(0, i - self.lookback_window)
                    historical_data = returns.iloc[lookback_start:i] if i > 0 else self.historical_returns
                    
                    if len(historical_data) >= 30:  # Minimum data requirement
                        temp_calculator = VaRCalculator(
                            confidence_levels=(confidence_level,),
                            lookback_window=len(historical_data)
                        )
                        temp_calculator.fit(historical_data, method=self.method)
                        
                        var_result = temp_calculator.calculate_var(portfolio_values.iloc[i])
                        var_estimate = var_result[f'{int(confidence_level*100)}%']['var_percent']
                    else:
                        var_estimate = self.calculate_var(portfolio_values.iloc[i])[f'{int(confidence_level*100)}%']['var_percent']
                else:
                    var_estimate = self.calculate_var(portfolio_values.iloc[i])[f'{int(confidence_level*100)}%']['var_percent']
                
                var_estimates.append(var_estimate)
                
                # Check for violation
                actual_return = returns.iloc[i]
                violation = actual_return < var_estimate
                violations.append(violation)
            
            # Calculate backtesting statistics
            violation_rate = np.mean(violations)
            expected_violation_rate = 1 - confidence_level
            
            # Kupiec test statistic
            n_violations = sum(violations)
            n_observations = len(violations)
            
            if n_violations > 0 and n_violations < n_observations:
                lr_stat = 2 * (
                    n_violations * np.log(violation_rate / expected_violation_rate) +
                    (n_observations - n_violations) * np.log((1 - violation_rate) / (1 - expected_violation_rate))
                )
            else:
                lr_stat = float('inf')
            
            backtest_results[f'{int(confidence_level*100)}%'] = {
                'violation_rate': violation_rate,
                'expected_violation_rate': expected_violation_rate,
                'n_violations': n_violations,
                'n_observations': n_observations,
                'kupiec_lr_stat': lr_stat,
                'violations': violations,
                'var_estimates': var_estimates
            }
        
        logger.info("VaR backtesting completed")
        return backtest_results
    
    def get_var_summary(self) -> Dict[str, Any]:
        """
        Get summary of VaR model configuration and parameters.
        
        Returns:
            Dictionary with model summary
        """
        summary = {
            'method': getattr(self, 'method', 'not_fitted'),
            'confidence_levels': self.confidence_levels,
            'lookback_window': self.lookback_window,
            'is_fitted': self.is_fitted,
            'n_observations': len(self.historical_returns) if self.historical_returns is not None else 0
        }
        
        if self.distribution_params:
            summary['distribution_params'] = self.distribution_params.copy()
        
        return summary
