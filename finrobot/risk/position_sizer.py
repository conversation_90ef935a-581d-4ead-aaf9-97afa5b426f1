"""Position sizing algorithms for risk management."""

import logging
from typing import Dict, Any, Optional, Union
import numpy as np
import pandas as pd

logger = logging.getLogger(__name__)


class PositionSizer:
    """
    Position sizing algorithms for risk management.
    
    Implements Kelly criterion, fixed fractional, volatility targeting,
    and risk parity position sizing methods.
    """
    
    def __init__(
        self,
        max_position_size: float = 0.1,
        min_position_size: float = 0.001,
        risk_free_rate: float = 0.02
    ):
        """
        Initialize position sizer.
        
        Args:
            max_position_size: Maximum position size as fraction of portfolio
            min_position_size: Minimum position size as fraction of portfolio
            risk_free_rate: Risk-free rate for Sharpe ratio calculations
        """
        self.max_position_size = max_position_size
        self.min_position_size = min_position_size
        self.risk_free_rate = risk_free_rate
        
    def kelly_criterion(
        self,
        expected_return: float,
        volatility: float,
        win_rate: Optional[float] = None,
        avg_win: Optional[float] = None,
        avg_loss: Optional[float] = None
    ) -> float:
        """
        Calculate position size using Kelly criterion.
        
        Args:
            expected_return: Expected return of the asset
            volatility: Volatility of the asset
            win_rate: Probability of winning trade (optional)
            avg_win: Average winning trade return (optional)
            avg_loss: Average losing trade return (optional)
            
        Returns:
            Optimal position size as fraction of portfolio
        """
        if win_rate is not None and avg_win is not None and avg_loss is not None:
            # Discrete Kelly formula
            kelly_fraction = (win_rate * avg_win - (1 - win_rate) * abs(avg_loss)) / avg_win
        else:
            # Continuous Kelly formula
            if volatility <= 0:
                return 0
            kelly_fraction = expected_return / (volatility ** 2)
        
        # Apply constraints
        kelly_fraction = max(0, kelly_fraction)  # No short positions
        kelly_fraction = min(kelly_fraction, self.max_position_size)
        kelly_fraction = max(kelly_fraction, self.min_position_size) if kelly_fraction > 0 else 0
        
        return kelly_fraction
    
    def fixed_fractional(
        self,
        risk_per_trade: float,
        stop_loss_distance: float
    ) -> float:
        """
        Calculate position size using fixed fractional method.
        
        Args:
            risk_per_trade: Risk per trade as fraction of portfolio
            stop_loss_distance: Distance to stop loss as fraction
            
        Returns:
            Position size as fraction of portfolio
        """
        if stop_loss_distance <= 0:
            return 0
        
        position_size = risk_per_trade / stop_loss_distance
        
        # Apply constraints
        position_size = min(position_size, self.max_position_size)
        position_size = max(position_size, self.min_position_size) if position_size > 0 else 0
        
        return position_size
    
    def volatility_targeting(
        self,
        target_volatility: float,
        asset_volatility: float,
        correlation_adjustment: float = 1.0
    ) -> float:
        """
        Calculate position size using volatility targeting.
        
        Args:
            target_volatility: Target portfolio volatility
            asset_volatility: Volatility of the asset
            correlation_adjustment: Adjustment for correlation with existing positions
            
        Returns:
            Position size as fraction of portfolio
        """
        if asset_volatility <= 0:
            return 0
        
        position_size = (target_volatility / asset_volatility) * correlation_adjustment
        
        # Apply constraints
        position_size = min(position_size, self.max_position_size)
        position_size = max(position_size, self.min_position_size) if position_size > 0 else 0
        
        return position_size
    
    def risk_parity_sizing(
        self,
        asset_volatilities: np.ndarray,
        correlation_matrix: np.ndarray,
        target_risk_contribution: Optional[np.ndarray] = None
    ) -> np.ndarray:
        """
        Calculate position sizes using risk parity approach.
        
        Args:
            asset_volatilities: Array of asset volatilities
            correlation_matrix: Correlation matrix between assets
            target_risk_contribution: Target risk contribution for each asset
            
        Returns:
            Array of position sizes
        """
        n_assets = len(asset_volatilities)
        
        if target_risk_contribution is None:
            target_risk_contribution = np.ones(n_assets) / n_assets
        
        # Convert correlation to covariance matrix
        vol_matrix = np.outer(asset_volatilities, asset_volatilities)
        covariance_matrix = correlation_matrix * vol_matrix
        
        # Initial equal weights
        weights = np.ones(n_assets) / n_assets
        
        # Iterative risk parity optimization
        for _ in range(100):  # Maximum iterations
            # Calculate risk contributions
            portfolio_variance = np.dot(weights, np.dot(covariance_matrix, weights))
            marginal_contrib = np.dot(covariance_matrix, weights)
            risk_contrib = weights * marginal_contrib / portfolio_variance
            
            # Update weights based on risk contribution error
            risk_error = risk_contrib - target_risk_contribution
            adjustment = 0.1 * risk_error  # Learning rate
            weights = weights - adjustment
            
            # Normalize and apply constraints
            weights = np.maximum(weights, self.min_position_size)
            weights = np.minimum(weights, self.max_position_size)
            weights = weights / np.sum(weights)  # Normalize to sum to 1
            
            # Check convergence
            if np.max(np.abs(risk_error)) < 1e-6:
                break
        
        return weights
    
    def optimal_f(
        self,
        returns: pd.Series,
        n_simulations: int = 1000
    ) -> float:
        """
        Calculate optimal position size using Optimal F method.
        
        Args:
            returns: Historical returns series
            n_simulations: Number of Monte Carlo simulations
            
        Returns:
            Optimal position size as fraction of portfolio
        """
        if len(returns) < 10:
            return self.min_position_size
        
        # Calculate largest loss
        largest_loss = abs(returns.min()) if returns.min() < 0 else 0.01
        
        if largest_loss <= 0:
            return self.min_position_size
        
        # Test different f values
        f_values = np.linspace(0.01, 0.5, 50)
        terminal_wealth = []
        
        for f in f_values:
            # Monte Carlo simulation
            wealth_paths = []
            
            for _ in range(n_simulations):
                # Sample returns with replacement
                sampled_returns = np.random.choice(returns.values, size=len(returns), replace=True)
                
                # Calculate wealth path
                wealth = 1.0
                for ret in sampled_returns:
                    position_size = min(f, wealth / largest_loss)  # Don't risk more than we have
                    wealth = wealth + position_size * ret
                    
                    if wealth <= 0:  # Bankruptcy
                        wealth = 0.001
                        break
                
                wealth_paths.append(wealth)
            
            # Calculate geometric mean of terminal wealth
            geometric_mean = np.exp(np.mean(np.log(np.maximum(wealth_paths, 1e-10))))
            terminal_wealth.append(geometric_mean)
        
        # Find optimal f
        optimal_idx = np.argmax(terminal_wealth)
        optimal_f_value = f_values[optimal_idx]
        
        # Apply constraints
        optimal_f_value = min(optimal_f_value, self.max_position_size)
        optimal_f_value = max(optimal_f_value, self.min_position_size)
        
        return optimal_f_value
    
    def calculate_position_sizes(
        self,
        signals: pd.DataFrame,
        method: str = 'kelly',
        **kwargs
    ) -> pd.DataFrame:
        """
        Calculate position sizes for multiple assets and signals.
        
        Args:
            signals: DataFrame with columns ['expected_return', 'volatility', 'signal_strength']
            method: Position sizing method ('kelly', 'fixed_fractional', 'volatility_targeting')
            **kwargs: Additional parameters for the chosen method
            
        Returns:
            DataFrame with position sizes
        """
        position_sizes = []
        
        for idx, row in signals.iterrows():
            expected_return = row.get('expected_return', 0)
            volatility = row.get('volatility', 0.1)
            signal_strength = row.get('signal_strength', 1.0)
            
            if method == 'kelly':
                size = self.kelly_criterion(expected_return, volatility)
            elif method == 'fixed_fractional':
                risk_per_trade = kwargs.get('risk_per_trade', 0.02)
                stop_loss_distance = kwargs.get('stop_loss_distance', 0.05)
                size = self.fixed_fractional(risk_per_trade, stop_loss_distance)
            elif method == 'volatility_targeting':
                target_volatility = kwargs.get('target_volatility', 0.15)
                size = self.volatility_targeting(target_volatility, volatility)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            # Adjust by signal strength
            size = size * signal_strength
            
            position_sizes.append({
                'asset': idx,
                'position_size': size,
                'expected_return': expected_return,
                'volatility': volatility,
                'signal_strength': signal_strength
            })
        
        return pd.DataFrame(position_sizes)
    
    def get_sizing_info(self) -> Dict[str, Any]:
        """
        Get information about the position sizer configuration.
        
        Returns:
            Dictionary with sizer information
        """
        return {
            'max_position_size': self.max_position_size,
            'min_position_size': self.min_position_size,
            'risk_free_rate': self.risk_free_rate,
            'available_methods': [
                'kelly_criterion',
                'fixed_fractional',
                'volatility_targeting',
                'risk_parity_sizing',
                'optimal_f'
            ]
        }
