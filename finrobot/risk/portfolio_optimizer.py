"""Portfolio optimization using mean-variance and risk parity approaches."""

import logging
from typing import Dict, Any, List, Optional, Tuple
import numpy as np
import pandas as pd
from scipy.optimize import minimize
from sklearn.covariance import LedoitWolf

logger = logging.getLogger(__name__)


class PortfolioOptimizer:
    """
    Portfolio optimization with multiple objective functions.
    
    Supports mean-variance optimization, risk parity, minimum variance,
    and maximum diversification approaches with transaction cost considerations.
    """
    
    def __init__(
        self,
        risk_aversion: float = 1.0,
        transaction_cost: float = 0.001,
        max_weight: float = 0.4,
        min_weight: float = 0.0
    ):
        """
        Initialize portfolio optimizer.
        
        Args:
            risk_aversion: Risk aversion parameter for mean-variance optimization
            transaction_cost: Transaction cost as fraction of trade value
            max_weight: Maximum weight for any single asset
            min_weight: Minimum weight for any single asset
        """
        self.risk_aversion = risk_aversion
        self.transaction_cost = transaction_cost
        self.max_weight = max_weight
        self.min_weight = min_weight
        
        self.expected_returns = None
        self.covariance_matrix = None
        self.asset_names = None
        self.is_fitted = False
        
    def fit(
        self,
        returns: pd.DataFrame,
        expected_returns: Optional[pd.Series] = None,
        covariance_estimator: str = 'ledoit_wolf'
    ) -> 'PortfolioOptimizer':
        """
        Fit the portfolio optimizer to historical data.
        
        Args:
            returns: Historical returns DataFrame (assets as columns)
            expected_returns: Expected returns (if None, use historical mean)
            covariance_estimator: Method for covariance estimation
            
        Returns:
            Self for method chaining
        """
        logger.info("Fitting portfolio optimizer")
        
        # Clean data
        clean_returns = returns.dropna()
        self.asset_names = clean_returns.columns.tolist()
        
        # Estimate expected returns
        if expected_returns is None:
            self.expected_returns = clean_returns.mean()
        else:
            self.expected_returns = expected_returns.reindex(self.asset_names)
        
        # Estimate covariance matrix
        if covariance_estimator == 'ledoit_wolf':
            lw = LedoitWolf()
            self.covariance_matrix = pd.DataFrame(
                lw.fit(clean_returns).covariance_,
                index=self.asset_names,
                columns=self.asset_names
            )
        elif covariance_estimator == 'sample':
            self.covariance_matrix = clean_returns.cov()
        else:
            raise ValueError(f"Unsupported covariance estimator: {covariance_estimator}")
        
        self.is_fitted = True
        logger.info(f"Portfolio optimizer fitted for {len(self.asset_names)} assets")
        
        return self
    
    def optimize_portfolio(
        self,
        objective: str = 'mean_variance',
        target_return: Optional[float] = None,
        target_risk: Optional[float] = None,
        current_weights: Optional[np.ndarray] = None
    ) -> Dict[str, Any]:
        """
        Optimize portfolio weights.
        
        Args:
            objective: Optimization objective ('mean_variance', 'min_variance', 'risk_parity', 'max_diversification')
            target_return: Target return for constrained optimization
            target_risk: Target risk for constrained optimization
            current_weights: Current portfolio weights (for transaction costs)
            
        Returns:
            Dictionary with optimization results
        """
        if not self.is_fitted:
            raise ValueError("Optimizer must be fitted before optimization")
        
        n_assets = len(self.asset_names)
        
        # Initial guess (equal weights)
        x0 = np.ones(n_assets) / n_assets
        
        # Bounds for weights
        bounds = [(self.min_weight, self.max_weight) for _ in range(n_assets)]
        
        # Constraints
        constraints = [{'type': 'eq', 'fun': lambda x: np.sum(x) - 1}]  # Weights sum to 1
        
        # Add target return constraint if specified
        if target_return is not None:
            constraints.append({
                'type': 'eq',
                'fun': lambda x: np.dot(x, self.expected_returns) - target_return
            })
        
        # Add target risk constraint if specified
        if target_risk is not None:
            constraints.append({
                'type': 'eq',
                'fun': lambda x: np.sqrt(np.dot(x, np.dot(self.covariance_matrix, x))) - target_risk
            })
        
        # Define objective function
        if objective == 'mean_variance':
            obj_func = self._mean_variance_objective
        elif objective == 'min_variance':
            obj_func = self._min_variance_objective
        elif objective == 'risk_parity':
            obj_func = self._risk_parity_objective
        elif objective == 'max_diversification':
            obj_func = self._max_diversification_objective
        else:
            raise ValueError(f"Unsupported objective: {objective}")
        
        # Optimize
        try:
            result = minimize(
                fun=lambda x: obj_func(x, current_weights),
                x0=x0,
                method='SLSQP',
                bounds=bounds,
                constraints=constraints,
                options={'maxiter': 1000, 'ftol': 1e-9}
            )
            
            if not result.success:
                logger.warning(f"Optimization did not converge: {result.message}")
            
            optimal_weights = result.x
            
        except Exception as e:
            logger.error(f"Optimization failed: {e}")
            optimal_weights = x0  # Fallback to equal weights
            result = None
        
        # Calculate portfolio metrics
        portfolio_metrics = self._calculate_portfolio_metrics(optimal_weights)
        
        # Calculate transaction costs if current weights provided
        transaction_costs = 0
        if current_weights is not None:
            turnover = np.sum(np.abs(optimal_weights - current_weights))
            transaction_costs = turnover * self.transaction_cost
        
        return {
            'weights': pd.Series(optimal_weights, index=self.asset_names),
            'expected_return': portfolio_metrics['expected_return'],
            'volatility': portfolio_metrics['volatility'],
            'sharpe_ratio': portfolio_metrics['sharpe_ratio'],
            'transaction_costs': transaction_costs,
            'optimization_success': result.success if result else False,
            'objective': objective
        }
    
    def _mean_variance_objective(
        self,
        weights: np.ndarray,
        current_weights: Optional[np.ndarray] = None
    ) -> float:
        """Mean-variance objective function."""
        expected_return = np.dot(weights, self.expected_returns)
        variance = np.dot(weights, np.dot(self.covariance_matrix, weights))
        
        # Add transaction costs
        transaction_cost = 0
        if current_weights is not None:
            turnover = np.sum(np.abs(weights - current_weights))
            transaction_cost = turnover * self.transaction_cost
        
        # Maximize utility: return - risk_aversion * variance - transaction_costs
        utility = expected_return - self.risk_aversion * variance - transaction_cost
        
        return -utility  # Minimize negative utility
    
    def _min_variance_objective(
        self,
        weights: np.ndarray,
        current_weights: Optional[np.ndarray] = None
    ) -> float:
        """Minimum variance objective function."""
        variance = np.dot(weights, np.dot(self.covariance_matrix, weights))
        
        # Add transaction costs
        transaction_cost = 0
        if current_weights is not None:
            turnover = np.sum(np.abs(weights - current_weights))
            transaction_cost = turnover * self.transaction_cost
        
        return variance + transaction_cost
    
    def _risk_parity_objective(
        self,
        weights: np.ndarray,
        current_weights: Optional[np.ndarray] = None
    ) -> float:
        """Risk parity objective function."""
        # Calculate risk contributions
        portfolio_variance = np.dot(weights, np.dot(self.covariance_matrix, weights))
        marginal_contrib = np.dot(self.covariance_matrix, weights)
        risk_contrib = weights * marginal_contrib / portfolio_variance
        
        # Target equal risk contribution
        target_contrib = 1 / len(weights)
        risk_parity_error = np.sum((risk_contrib - target_contrib) ** 2)
        
        # Add transaction costs
        transaction_cost = 0
        if current_weights is not None:
            turnover = np.sum(np.abs(weights - current_weights))
            transaction_cost = turnover * self.transaction_cost
        
        return risk_parity_error + transaction_cost
    
    def _max_diversification_objective(
        self,
        weights: np.ndarray,
        current_weights: Optional[np.ndarray] = None
    ) -> float:
        """Maximum diversification objective function."""
        # Diversification ratio = weighted average volatility / portfolio volatility
        individual_vols = np.sqrt(np.diag(self.covariance_matrix))
        weighted_avg_vol = np.dot(weights, individual_vols)
        portfolio_vol = np.sqrt(np.dot(weights, np.dot(self.covariance_matrix, weights)))
        
        diversification_ratio = weighted_avg_vol / portfolio_vol
        
        # Add transaction costs
        transaction_cost = 0
        if current_weights is not None:
            turnover = np.sum(np.abs(weights - current_weights))
            transaction_cost = turnover * self.transaction_cost
        
        return -diversification_ratio + transaction_cost  # Maximize diversification
    
    def _calculate_portfolio_metrics(self, weights: np.ndarray) -> Dict[str, float]:
        """Calculate portfolio performance metrics."""
        expected_return = np.dot(weights, self.expected_returns)
        variance = np.dot(weights, np.dot(self.covariance_matrix, weights))
        volatility = np.sqrt(variance)
        
        # Sharpe ratio (assuming risk-free rate = 0)
        sharpe_ratio = expected_return / volatility if volatility > 0 else 0
        
        return {
            'expected_return': expected_return,
            'volatility': volatility,
            'variance': variance,
            'sharpe_ratio': sharpe_ratio
        }

    def calculate_efficient_frontier(
        self,
        n_points: int = 50,
        return_range: Optional[Tuple[float, float]] = None
    ) -> pd.DataFrame:
        """
        Calculate the efficient frontier.

        Args:
            n_points: Number of points on the frontier
            return_range: Range of returns to consider (min, max)

        Returns:
            DataFrame with efficient frontier points
        """
        if not self.is_fitted:
            raise ValueError("Optimizer must be fitted before calculating efficient frontier")

        # Determine return range
        if return_range is None:
            min_return = self.expected_returns.min()
            max_return = self.expected_returns.max()
        else:
            min_return, max_return = return_range

        target_returns = np.linspace(min_return, max_return, n_points)

        frontier_results = []

        for target_return in target_returns:
            try:
                result = self.optimize_portfolio(
                    objective='min_variance',
                    target_return=target_return
                )

                frontier_results.append({
                    'target_return': target_return,
                    'expected_return': result['expected_return'],
                    'volatility': result['volatility'],
                    'sharpe_ratio': result['sharpe_ratio'],
                    'weights': result['weights'].to_dict()
                })

            except Exception as e:
                logger.debug(f"Failed to optimize for return {target_return}: {e}")
                continue

        return pd.DataFrame(frontier_results)

    def get_optimizer_info(self) -> Dict[str, Any]:
        """
        Get information about the optimizer configuration.

        Returns:
            Dictionary with optimizer information
        """
        info = {
            'risk_aversion': self.risk_aversion,
            'transaction_cost': self.transaction_cost,
            'max_weight': self.max_weight,
            'min_weight': self.min_weight,
            'is_fitted': self.is_fitted
        }

        if self.is_fitted:
            info.update({
                'n_assets': len(self.asset_names),
                'asset_names': self.asset_names,
                'expected_returns': self.expected_returns.to_dict(),
                'covariance_eigenvalues': np.linalg.eigvals(self.covariance_matrix.values).tolist()
            })

        return info
