"""Comprehensive risk metrics calculation."""

import logging
from typing import Dict, Any, List, Optional, Tuple
import numpy as np
import pandas as pd
from scipy import stats

logger = logging.getLogger(__name__)


class RiskMetrics:
    """
    Comprehensive risk metrics calculator.
    
    Calculates various risk measures including volatility, drawdowns,
    tail risk measures, and risk-adjusted performance metrics.
    """
    
    def __init__(
        self,
        risk_free_rate: float = 0.02,
        confidence_levels: Tuple[float, ...] = (0.95, 0.99)
    ):
        """
        Initialize risk metrics calculator.
        
        Args:
            risk_free_rate: Risk-free rate for Sharpe ratio calculations
            confidence_levels: Confidence levels for tail risk measures
        """
        self.risk_free_rate = risk_free_rate
        self.confidence_levels = confidence_levels
        
    def calculate_volatility_metrics(
        self,
        returns: pd.Series,
        annualization_factor: int = 252
    ) -> Dict[str, float]:
        """
        Calculate various volatility metrics.
        
        Args:
            returns: Returns series
            annualization_factor: Factor for annualizing metrics
            
        Returns:
            Dictionary with volatility metrics
        """
        clean_returns = returns.dropna()
        
        if len(clean_returns) < 2:
            return {'error': 'Insufficient data for volatility calculation'}
        
        metrics = {}
        
        # Basic volatility
        metrics['volatility'] = clean_returns.std() * np.sqrt(annualization_factor)
        
        # Downside volatility (semi-deviation)
        downside_returns = clean_returns[clean_returns < 0]
        if len(downside_returns) > 0:
            metrics['downside_volatility'] = downside_returns.std() * np.sqrt(annualization_factor)
        else:
            metrics['downside_volatility'] = 0
        
        # Upside volatility
        upside_returns = clean_returns[clean_returns > 0]
        if len(upside_returns) > 0:
            metrics['upside_volatility'] = upside_returns.std() * np.sqrt(annualization_factor)
        else:
            metrics['upside_volatility'] = 0
        
        # Rolling volatility statistics
        rolling_vol = clean_returns.rolling(window=min(30, len(clean_returns)//2)).std()
        metrics['volatility_of_volatility'] = rolling_vol.std() * np.sqrt(annualization_factor)
        metrics['max_volatility'] = rolling_vol.max() * np.sqrt(annualization_factor)
        metrics['min_volatility'] = rolling_vol.min() * np.sqrt(annualization_factor)
        
        return metrics
    
    def calculate_drawdown_metrics(
        self,
        returns: pd.Series
    ) -> Dict[str, float]:
        """
        Calculate drawdown metrics.
        
        Args:
            returns: Returns series
            
        Returns:
            Dictionary with drawdown metrics
        """
        clean_returns = returns.dropna()
        
        if len(clean_returns) < 2:
            return {'error': 'Insufficient data for drawdown calculation'}
        
        # Calculate cumulative returns
        cumulative_returns = (1 + clean_returns).cumprod()
        
        # Calculate running maximum
        running_max = cumulative_returns.expanding().max()
        
        # Calculate drawdowns
        drawdowns = (cumulative_returns - running_max) / running_max
        
        metrics = {}
        
        # Maximum drawdown
        metrics['max_drawdown'] = drawdowns.min()
        
        # Average drawdown
        negative_drawdowns = drawdowns[drawdowns < 0]
        metrics['average_drawdown'] = negative_drawdowns.mean() if len(negative_drawdowns) > 0 else 0
        
        # Drawdown duration
        in_drawdown = drawdowns < 0
        drawdown_periods = []
        current_period = 0
        
        for is_dd in in_drawdown:
            if is_dd:
                current_period += 1
            else:
                if current_period > 0:
                    drawdown_periods.append(current_period)
                current_period = 0
        
        if current_period > 0:  # Still in drawdown at end
            drawdown_periods.append(current_period)
        
        if drawdown_periods:
            metrics['max_drawdown_duration'] = max(drawdown_periods)
            metrics['average_drawdown_duration'] = np.mean(drawdown_periods)
        else:
            metrics['max_drawdown_duration'] = 0
            metrics['average_drawdown_duration'] = 0
        
        # Recovery time (time to recover from max drawdown)
        max_dd_idx = drawdowns.idxmin()
        recovery_returns = cumulative_returns[max_dd_idx:]
        max_dd_value = running_max.loc[max_dd_idx]
        
        recovery_idx = recovery_returns[recovery_returns >= max_dd_value].index
        if len(recovery_idx) > 0:
            recovery_time = recovery_idx[0] - max_dd_idx
            metrics['recovery_time'] = recovery_time.days if hasattr(recovery_time, 'days') else recovery_time
        else:
            metrics['recovery_time'] = len(recovery_returns)  # Still recovering
        
        return metrics
    
    def calculate_tail_risk_metrics(
        self,
        returns: pd.Series
    ) -> Dict[str, float]:
        """
        Calculate tail risk metrics.
        
        Args:
            returns: Returns series
            
        Returns:
            Dictionary with tail risk metrics
        """
        clean_returns = returns.dropna()
        
        if len(clean_returns) < 10:
            return {'error': 'Insufficient data for tail risk calculation'}
        
        metrics = {}
        
        for confidence_level in self.confidence_levels:
            alpha = 1 - confidence_level
            
            # Value at Risk (VaR)
            var = np.percentile(clean_returns, alpha * 100)
            metrics[f'var_{int(confidence_level*100)}%'] = var
            
            # Expected Shortfall (Conditional VaR)
            tail_returns = clean_returns[clean_returns <= var]
            if len(tail_returns) > 0:
                es = tail_returns.mean()
            else:
                es = var
            metrics[f'expected_shortfall_{int(confidence_level*100)}%'] = es
        
        # Skewness and Kurtosis
        metrics['skewness'] = stats.skew(clean_returns)
        metrics['kurtosis'] = stats.kurtosis(clean_returns)
        metrics['excess_kurtosis'] = stats.kurtosis(clean_returns, fisher=True)
        
        # Jarque-Bera test for normality
        jb_stat, jb_pvalue = stats.jarque_bera(clean_returns)
        metrics['jarque_bera_stat'] = jb_stat
        metrics['jarque_bera_pvalue'] = jb_pvalue
        metrics['is_normal'] = jb_pvalue > 0.05
        
        return metrics
    
    def calculate_performance_metrics(
        self,
        returns: pd.Series,
        benchmark_returns: Optional[pd.Series] = None,
        annualization_factor: int = 252
    ) -> Dict[str, float]:
        """
        Calculate risk-adjusted performance metrics.
        
        Args:
            returns: Returns series
            benchmark_returns: Benchmark returns for comparison
            annualization_factor: Factor for annualizing metrics
            
        Returns:
            Dictionary with performance metrics
        """
        clean_returns = returns.dropna()
        
        if len(clean_returns) < 2:
            return {'error': 'Insufficient data for performance calculation'}
        
        metrics = {}
        
        # Basic return metrics
        total_return = (1 + clean_returns).prod() - 1
        annualized_return = (1 + total_return) ** (annualization_factor / len(clean_returns)) - 1
        
        metrics['total_return'] = total_return
        metrics['annualized_return'] = annualized_return
        
        # Volatility
        volatility = clean_returns.std() * np.sqrt(annualization_factor)
        metrics['volatility'] = volatility
        
        # Sharpe ratio
        excess_return = annualized_return - self.risk_free_rate
        metrics['sharpe_ratio'] = excess_return / volatility if volatility > 0 else 0
        
        # Sortino ratio (using downside deviation)
        downside_returns = clean_returns[clean_returns < 0]
        if len(downside_returns) > 0:
            downside_deviation = downside_returns.std() * np.sqrt(annualization_factor)
            metrics['sortino_ratio'] = excess_return / downside_deviation
        else:
            metrics['sortino_ratio'] = float('inf') if excess_return > 0 else 0
        
        # Calmar ratio (return / max drawdown)
        drawdown_metrics = self.calculate_drawdown_metrics(clean_returns)
        max_drawdown = abs(drawdown_metrics.get('max_drawdown', 0.01))
        metrics['calmar_ratio'] = annualized_return / max_drawdown if max_drawdown > 0 else 0
        
        # Win rate and profit factor
        winning_trades = clean_returns[clean_returns > 0]
        losing_trades = clean_returns[clean_returns < 0]
        
        metrics['win_rate'] = len(winning_trades) / len(clean_returns)
        
        if len(winning_trades) > 0 and len(losing_trades) > 0:
            avg_win = winning_trades.mean()
            avg_loss = abs(losing_trades.mean())
            metrics['profit_factor'] = (len(winning_trades) * avg_win) / (len(losing_trades) * avg_loss)
            metrics['average_win'] = avg_win
            metrics['average_loss'] = -avg_loss
        else:
            metrics['profit_factor'] = float('inf') if len(losing_trades) == 0 else 0
            metrics['average_win'] = winning_trades.mean() if len(winning_trades) > 0 else 0
            metrics['average_loss'] = losing_trades.mean() if len(losing_trades) > 0 else 0
        
        # Benchmark comparison
        if benchmark_returns is not None:
            benchmark_clean = benchmark_returns.dropna()
            aligned_returns, aligned_benchmark = clean_returns.align(benchmark_clean, join='inner')
            
            if len(aligned_returns) > 1:
                # Beta
                covariance = np.cov(aligned_returns, aligned_benchmark)[0, 1]
                benchmark_variance = np.var(aligned_benchmark)
                beta = covariance / benchmark_variance if benchmark_variance > 0 else 0
                metrics['beta'] = beta
                
                # Alpha
                benchmark_annualized = (1 + aligned_benchmark).prod() ** (annualization_factor / len(aligned_benchmark)) - 1
                alpha = annualized_return - (self.risk_free_rate + beta * (benchmark_annualized - self.risk_free_rate))
                metrics['alpha'] = alpha
                
                # Information ratio
                excess_returns = aligned_returns - aligned_benchmark
                tracking_error = excess_returns.std() * np.sqrt(annualization_factor)
                metrics['information_ratio'] = excess_returns.mean() * annualization_factor / tracking_error if tracking_error > 0 else 0
                
                # Correlation
                metrics['correlation'] = np.corrcoef(aligned_returns, aligned_benchmark)[0, 1]
        
        return metrics
    
    def calculate_comprehensive_risk_report(
        self,
        returns: pd.Series,
        benchmark_returns: Optional[pd.Series] = None,
        portfolio_value: Optional[pd.Series] = None
    ) -> Dict[str, Any]:
        """
        Generate comprehensive risk report.
        
        Args:
            returns: Returns series
            benchmark_returns: Benchmark returns for comparison
            portfolio_value: Portfolio value series
            
        Returns:
            Dictionary with comprehensive risk metrics
        """
        logger.info("Generating comprehensive risk report")
        
        report = {}
        
        # Basic statistics
        clean_returns = returns.dropna()
        report['basic_stats'] = {
            'n_observations': len(clean_returns),
            'start_date': clean_returns.index[0] if len(clean_returns) > 0 else None,
            'end_date': clean_returns.index[-1] if len(clean_returns) > 0 else None,
            'mean_return': clean_returns.mean(),
            'median_return': clean_returns.median(),
            'std_return': clean_returns.std()
        }
        
        # Volatility metrics
        report['volatility_metrics'] = self.calculate_volatility_metrics(clean_returns)
        
        # Drawdown metrics
        report['drawdown_metrics'] = self.calculate_drawdown_metrics(clean_returns)
        
        # Tail risk metrics
        report['tail_risk_metrics'] = self.calculate_tail_risk_metrics(clean_returns)
        
        # Performance metrics
        report['performance_metrics'] = self.calculate_performance_metrics(
            clean_returns, benchmark_returns
        )
        
        # Portfolio value metrics if provided
        if portfolio_value is not None:
            clean_values = portfolio_value.dropna()
            if len(clean_values) > 1:
                report['portfolio_metrics'] = {
                    'initial_value': clean_values.iloc[0],
                    'final_value': clean_values.iloc[-1],
                    'peak_value': clean_values.max(),
                    'trough_value': clean_values.min(),
                    'value_volatility': clean_values.pct_change().std()
                }
        
        logger.info("Risk report generated successfully")
        return report
    
    def get_risk_summary(
        self,
        risk_report: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Get a summary of key risk metrics.
        
        Args:
            risk_report: Comprehensive risk report
            
        Returns:
            Dictionary with key risk metrics summary
        """
        summary = {}
        
        # Extract key metrics
        perf_metrics = risk_report.get('performance_metrics', {})
        drawdown_metrics = risk_report.get('drawdown_metrics', {})
        tail_metrics = risk_report.get('tail_risk_metrics', {})
        vol_metrics = risk_report.get('volatility_metrics', {})
        
        summary['key_metrics'] = {
            'annualized_return': perf_metrics.get('annualized_return', 0),
            'volatility': perf_metrics.get('volatility', 0),
            'sharpe_ratio': perf_metrics.get('sharpe_ratio', 0),
            'max_drawdown': drawdown_metrics.get('max_drawdown', 0),
            'var_95%': tail_metrics.get('var_95%', 0),
            'win_rate': perf_metrics.get('win_rate', 0)
        }
        
        # Risk assessment
        sharpe = perf_metrics.get('sharpe_ratio', 0)
        max_dd = abs(drawdown_metrics.get('max_drawdown', 0))
        
        if sharpe > 1.5 and max_dd < 0.1:
            risk_level = 'Low'
        elif sharpe > 1.0 and max_dd < 0.2:
            risk_level = 'Moderate'
        elif sharpe > 0.5 and max_dd < 0.3:
            risk_level = 'High'
        else:
            risk_level = 'Very High'
        
        summary['risk_assessment'] = {
            'risk_level': risk_level,
            'risk_score': min(100, max(0, (1 - max_dd) * sharpe * 50))
        }
        
        return summary
