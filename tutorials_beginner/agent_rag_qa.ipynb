{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# RetrieveChat based FinRobot-RAG"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this demo, we showcase the RAG usecase of our finrobot, which inherits from autogen's RetrieveChat implementation.\n", "\n", "\n", "Instead of using `RetrieveUserProxyAgent` directly, we register the context retrieval as a function for our bots.\n", "For detailed implementation, refer to [rag function](../finrobot/functional/rag.py) and [rag workflow](../finrobot/agents/workflow.py) of `SingleAssistantRAG` "]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import autogen\n", "from finrobot.agents.workflow import SingleAssistantRAG"]}, {"cell_type": "markdown", "metadata": {}, "source": ["for openai configuration, rename OAI_CONFIG_LIST_sample to OAI_CONFIG_LIST and replace the api keys"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Read OpenAI API keys from a JSON file\n", "llm_config = {\n", "    \"config_list\": autogen.config_list_from_json(\n", "        \"../OAI_CONFIG_LIST\",\n", "        filter_dict={\"model\": [\"gpt-4-0125-preview\"]},\n", "    ),\n", "    \"timeout\": 120,\n", "    \"temperature\": 0,\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["From `finrobot.agents.workflow` we import the `SingleAssistantRAG`, which takes a `retrieve_config` as input.\n", "For `docs_path`, we first put our generated pdf report from [this notebook](./agent_annual_report.ipynb). \n", "\n", "For more configuration, refer to [autogen's documentation](https://microsoft.github.io/autogen/docs/reference/agentchat/contrib/retrieve_user_proxy_agent)\n", "\n", "Then, lets do a simple Q&A."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33mUser_Proxy\u001b[0m (to Data_Analyst):\n", "\n", "How's msft's 2023 income? Provide with some analysis.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mData_Analyst\u001b[0m (to User_Proxy):\n", "\n", "\u001b[32m***** Suggested tool call (call_89dEBuptUkQCyH9KKfj3MTa6): retrieve_content *****\u001b[0m\n", "Arguments: \n", "{\"message\":\"Microsoft's 2023 income statement analysis\",\"n_results\":5}\n", "\u001b[32m*********************************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION retrieve_content...\u001b[0m\n", "Trying to create collection.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Number of requested results 5 is greater than number of elements in index 1, updating n_results = 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["doc_ids:  [['doc_0']]\n", "\u001b[32mAdding content of doc doc_0 to context.\u001b[0m\n", "\u001b[33mUser_Proxy\u001b[0m (to Data_Analyst):\n", "\n", "\u001b[33mUser_Proxy\u001b[0m (to Data_Analyst):\n", "\n", "\u001b[32m***** Response from calling tool (call_89dEBuptUkQCyH9KKfj3MTa6) *****\u001b[0m\n", "Below is the context retrieved from the required file based on your query.\n", "If you can't answer the question with or without the current context, you should try using a more refined search query according to your requirements, or ask for more contexts.\n", "\n", "Your current query is: Microsoft's 2023 income statement analysis\n", "\n", "Retrieved context is: Equity Research Report: Microsoft Corporation\n", "Income Summarization\n", "The company experienced a 7% Year-over-Year increase in revenue, driven by\n", "significant contributions from its Intelligent Cloud and Productivity and Business\n", "Processes segments, indicating a strong demand for cloud-based solutions and\n", "productivity software. Despite the revenue growth, the Cost of Goods Sold (COGS)\n", "increased by 5%, suggesting a need for closer cost control measures to improve cost\n", "efficiency and maintain profitability. The gross margin increased by 8%, while\n", "operating income grew by 6%, highlighting effective cost management and\n", "operational efficiency. However, net income slightly decreased by 1%, underscoring\n", "challenges in sustaining net profitability against operational costs and investments.\n", "The Diluted EPS remained stable at $9.68, reflecting a balanced investor outlook but\n", "indicating the need for strategic initiatives to enhance shareholder value.\n", "Business Highlights\n", "Productivity and Business Processes segment saw a notable revenue increase,\n", "driven by Office 365 Commercial and LinkedIn. This growth highlights the robust\n", "demand for Microsoft's productivity tools and professional networking platform,\n", "reflecting the company's ability to meet evolving workplace needs. More Personal\n", "Computing segment experienced a decrease in revenue, primarily due to declines in\n", "Windows and Devices. This downturn underscores the challenges faced by the\n", "segment, including shifting consumer preferences and the competitive landscape in\n", "PC and device markets.\n", "Company Situation\n", "Microsoft Corporation operates in the technology industry, focusing on empowering\n", "individuals and organizations globally through a wide array of products and services.\n", "Its core strengths lie in its diverse portfolio, including cloud-based solutions, software,\n", "devices, and services that leverage artificial intelligence (AI) to enhance productivity\n", "and business processes. Microsoft's competitive advantages include its innovation in\n", "AI, cloud infrastructure, and collaboration tools, such as Microsoft Teams and Office\n", "365. Current industry trends emphasize digital transformation, cloud computing, and\n", "AI innovation. Opportunities for Microsoft include expanding its cloud services and\n", "integrating AI across its product suite. Challenges involve navigating a highly\n", "competitive technology landscape and adapting to changing consumer and business\n", "needs. Recent strategic initiatives include investing in AI capabilities, expanding its\n", "cloud infrastructure, and acquiring companies like Nuance to bolster its healthcare AI\n", "solutions. Microsoft's response to market conditions includes focusing on cloud and\n", "AI technologies to drive future growth. Microsoft's strategic focus on AI and cloud\n", "computing, coupled with its ability to innovate and adapt, positions it well for\n", "continued success in the dynamic technology industry.\n", "Risk Assessment\n", "Microsoft Corporation faces significant challenges primarily from strategic and\n", "competitive risks due to intense competition and rapid technological changes in the\n", "tech sector. The company's increasing focus on cloud-based services introduces\n", "execution and competitive risks, including the need to innovate and manage costs\n", "effectively. Additionally, Microsoft is subject to various legal, regulatory, and litigation\n", "risks that could impact its business operations and financial performance. These\n", "include government scrutiny under U.S. and foreign competition laws, as well as\n", "potential liabilities from claims of intellectual property infringement.\n", "Financial Metrics\n", " FY (USD mn)\n", "2019\n", "2020\n", "2021\n", "2022\n", "2023\n", "Operating Revenue\n", "125843.0\n", "143015.0\n", "168088.0\n", "198270.0\n", "211915.0\n", "Adjusted Net Profit\n", "39240.0\n", "44281.0\n", "61271.0\n", "72738.0\n", "72361.0\n", "Adjusted EPS\n", "5.11\n", "5.82\n", "8.12\n", "9.7\n", "9.72\n", "EBIT Margin\n", "0.34\n", "0.37\n", "0.42\n", "0.42\n", "0.42\n", "ROE\n", "0.38\n", "0.37\n", "0.43\n", "0.44\n", "0.35\n", "PE Ratio\n", "26.53\n", "34.97\n", "33.37\n", "26.47\n", "35.04\n", "EV/EBITDA\n", "19.32\n", "23.48\n", "25.03\n", "19.74\n", "24.35\n", "PB Ratio\n", "10.17\n", "13.09\n", "14.4\n", "11.56\n", "12.3\n", "FinRobot\n", "https://ai4finance.org/\n", "https://github.com/AI4Finance-Foundation/FinRobot\n", "Report date: 2023-07-27\n", "Key data\n", " Rating\n", "<PERSON><PERSON><PERSON>\n", "Target Price\n", "400 - 400 (md. 400.0)\n", "6m avg daily vol (USDmn)\n", "29.99\n", "Closing Price (USD)\n", "335.15\n", "Market Cap (USDmn)\n", "2456918.88\n", "52 Week Price Range (USD)\n", "217.07 - 349.70\n", "BVPS (USD)\n", "27.70\n", "Share Performance\n", "PE & EPS\n", "\n", "\n", "\u001b[32m**********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mData_Analyst\u001b[0m (to User_Proxy):\n", "\n", "### Microsoft's 2023 Income Analysis\n", "\n", "#### Revenue and Profitability\n", "- **Revenue Growth:** Microsoft reported a 7% Year-over-Year (YoY) increase in revenue for 2023, reaching $211.915 billion. This growth was primarily driven by significant contributions from its Intelligent Cloud and Productivity and Business Processes segments, indicating strong demand for cloud-based solutions and productivity software.\n", "- **Cost of Goods Sold (COGS):** COGS increased by 5%, suggesting that while Microsoft is growing, there is a need for closer cost control measures to improve cost efficiency and maintain profitability.\n", "- **Gross Margin and Operating Income:** The gross margin increased by 8%, and operating income grew by 6%, highlighting effective cost management and operational efficiency.\n", "- **Net Income:** Despite the positive trends in revenue and operating income, net income slightly decreased by 1%, underscoring challenges in sustaining net profitability against operational costs and investments.\n", "- **Earnings Per Share (EPS):** The Diluted EPS remained stable at $9.68, reflecting a balanced investor outlook but indicating the need for strategic initiatives to enhance shareholder value.\n", "\n", "#### Business Highlights\n", "- **Productivity and Business Processes:** This segment saw a notable revenue increase, driven by Office 365 Commercial and LinkedIn, highlighting robust demand for Microsoft's productivity tools and professional networking platform.\n", "- **More Personal Computing:** This segment experienced a decrease in revenue, primarily due to declines in Windows and Devices, underscoring the challenges faced by the segment, including shifting consumer preferences and the competitive landscape in PC and device markets.\n", "\n", "#### Company Situation and Strategic Focus\n", "- Microsoft operates in the technology industry, focusing on empowering individuals and organizations globally through a wide array of products and services. Its core strengths lie in its diverse portfolio, including cloud-based solutions, software, devices, and services that leverage artificial intelligence (AI) to enhance productivity and business processes.\n", "- **Strategic Initiatives:** Recent strategic initiatives include investing in AI capabilities, expanding its cloud infrastructure, and acquiring companies like Nuance to bolster its healthcare AI solutions.\n", "- **Opportunities and Challenges:** Opportunities for Microsoft include expanding its cloud services and integrating AI across its product suite. Challenges involve navigating a highly competitive technology landscape and adapting to changing consumer and business needs.\n", "\n", "#### Financial Metrics for 2023\n", "- **Operating Revenue:** $211.915 billion\n", "- **Adjusted Net Profit:** $72.361 billion\n", "- **Adjusted EPS:** $9.72\n", "- **EBIT Margin:** 42%\n", "- **Return on Equity (ROE):** 35%\n", "- **Price-<PERSON>arnings (PE) Ratio:** 35.04\n", "- **Enterprise Value/EBITDA (EV/EBITDA):** 24.35\n", "- **Price to Book (PB) Ratio:** 12.3\n", "\n", "#### Market Performance\n", "- **Rating:** Strong Buy with a target price of $400.\n", "- **Market Cap:** $2,456,918.88 million\n", "- **52 Week Price Range:** $217.07 - $349.70\n", "- **Book Value Per Share (BVPS):** $27.70\n", "\n", "#### Conclusion\n", "Microsoft's financial performance in 2023 showcases a company that continues to grow its revenue, particularly through its cloud and productivity segments. Despite facing challenges in net income and the More Personal Computing segment, the company's strategic focus on AI and cloud computing, coupled with its ability to innovate and adapt, positions it well for continued success in the dynamic technology industry.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mUser_Proxy\u001b[0m (to Data_Analyst):\n", "\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mData_Analyst\u001b[0m (to User_Proxy):\n", "\n", "TERMINATE\n", "\n", "--------------------------------------------------------------------------------\n", "Current chat finished. Resetting agents ...\n"]}], "source": ["assitant = SingleAssistantRAG(\n", "    \"Data_Analyst\",\n", "    llm_config,\n", "    human_input_mode=\"NEVER\",\n", "    retrieve_config={\n", "        \"task\": \"qa\",\n", "        \"vector_db\": None,  # Autogen has bug for this version\n", "        \"docs_path\": [\n", "            \"../report/Microsoft_Annual_Report_2023.pdf\",\n", "        ],\n", "        \"chunk_token_size\": 1000,\n", "        \"get_or_create\": True,\n", "        \"collection_name\": \"msft_analysis\",\n", "        \"must_break_at_empty_line\": False,\n", "    },\n", ")\n", "assitant.chat(\"How's msft's 2023 income? Provide with some analysis.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here we come up with a more complex case, where we put the 10-k report of MSFT here.\n", "\n", "Let' see how the agent work this out."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33mUser_Proxy\u001b[0m (to Data_Analyst):\n", "\n", "How's msft's 2023 income? Provide with some analysis.\n", "\n", "--------------------------------------------------------------------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["huggingface/tokenizers: The current process just got forked, after parallelism has already been used. Disabling parallelism to avoid deadlocks...\n", "To disable this warning, you can either:\n", "\t- Avoid using `tokenizers` before the fork if possible\n", "\t- Explicitly set the environment variable TOKENIZERS_PARALLELISM=(true | false)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[33mData_Analyst\u001b[0m (to User_Proxy):\n", "\n", "\u001b[32m***** Suggested tool call (call_5p8Z5RJeBwMQa8EWxgWdLDJz): retrieve_content *****\u001b[0m\n", "Arguments: \n", "{\"message\":\"Microsoft's 2023 income statement details\"}\n", "\u001b[32m*********************************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[35m\n", ">>>>>>>> EXECUTING FUNCTION retrieve_content...\u001b[0m\n", "Trying to create collection.\n", "doc_ids:  [['doc_17', 'doc_18', 'doc_42']]\n", "\u001b[32mAdding content of doc doc_17 to context.\u001b[0m\n", "\u001b[32mAdding content of doc doc_18 to context.\u001b[0m\n", "\u001b[32mAdding content of doc doc_42 to context.\u001b[0m\n", "\u001b[33mUser_Proxy\u001b[0m (to Data_Analyst):\n", "\n", "\u001b[33mUser_Proxy\u001b[0m (to Data_Analyst):\n", "\n", "\u001b[32m***** Response from calling tool (call_5p8Z5RJeBwMQa8EWxgWdLDJz) *****\u001b[0m\n", "Below is the context retrieved from the required file based on your query.\n", "If you can't answer the question with or without the current context, you should try using a more refined search query according to your requirements, or ask for more contexts.\n", "\n", "Your current query is: Microsoft's 2023 income statement details\n", "\n", "Retrieved context is: estimate was eﬀective beginning ﬁscal year 2023. Based on the carrying amount of server and network \n", "equipment included in property and equipment, net as of June 30, 2022, the eﬀect of this change in \n", "estimate for ﬁscal year 2023 was an increase in operating income of $3.7 billion and net income of $3.0 \n", "billion, or $0.40 per both basic and diluted share.\n", "41PART II\n", "Item 7\n", " \n", "Reportable Segments\n", "We report our ﬁnancial performance based on the following segments: Productivity and Business \n", "Processes, Intelligent Cloud, and More Personal Computing. The segment amounts included in MD&A are \n", "presented on a basis consistent with our internal management reporting. We have recast certain prior \n", "period amounts to conform to the way we internally manage and monitor our business.\n", "Additional information on our reportable segments is contained in Note 19 – Segment Information and \n", "Geographic Data of the Notes to Financial Statements (Part II, Item 8 of this Form 10-K). \n", "Metrics\n", "We use metrics in assessing the performance of our business and to make informed decisions regarding \n", "the allocation of resources. We disclose metrics to enable investors to evaluate progress against our \n", "ambitions, provide transparency into performance trends, and reﬂect the continued evolution of our \n", "products and services. Our commercial and other business metrics are fundamentally connected based \n", "on how customers use our products and services. The metrics are disclosed in the MD&A or the Notes to \n", "Financial Statements (Part II, Item 8 of this Form 10-K). Financial metrics are calculated based on \n", "ﬁnancial results prepared in accordance with accounting principles generally accepted in the United \n", "States of America (“GAAP”), and growth comparisons relate to the corresponding period of last ﬁscal \n", "year.\n", "In the ﬁrst quarter of ﬁscal year 2023, we made updates to the presentation and method of calculation \n", "for certain metrics, most notably expanding our Surface metric into a broader Devices metric to \n", "incorporate additional revenue streams, along with other minor changes to align with how we manage \n", "our businesses. \n", "Commercial\n", "Our commercial business primarily consists of Server products and cloud services, Oﬃce Commercial, \n", "Windows Commercial, the commercial portion of LinkedIn, Enterprise Services, and Dynamics. Our \n", "commercial metrics allow management and investors to assess the overall health of our commercial \n", "business and include leading indicators of future performance.\n", " \n", "Commercial remaining performance \n", "obligation \n", " Commercial portion of revenue allocated to remaining \n", "performance obligations, which includes unearned revenue \n", "and amounts that will be invoiced and recognized as revenue \n", "in future periods\n", "     \n", "Microsoft Cloud revenue \n", " Revenue from Azure and other cloud services, Oﬃce 365 \n", "Commercial, the commercial portion of LinkedIn, Dynamics \n", "365, and other commercial cloud properties\n", "     \n", "Microsoft Cloud gross margin \n", "percentage  Gross margin percentage for our Microsoft Cloud business \n", " \n", "42PART II\n", "Item 7\n", " \n", "Productivity and Business Processes and Intelligent Cloud\n", "Metrics related to our Productivity and Business Processes and Intelligent Cloud segments assess the \n", "health of our core businesses within these segments. The metrics reﬂect our cloud and on-premises \n", "product strategies and trends.\n", " \n", "Oﬃce Commercial products and cloud \n", "services revenue growth\n", " Revenue from Oﬃce Commercial products and cloud services \n", "(Oﬃce 365 subscriptions, the Oﬃce 365 portion of Microsoft \n", "365 Commercial subscriptions, and Oﬃce licensed on-\n", "premises), comprising Oﬃce, Exchange, SharePoint, Microsoft \n", "Teams, Oﬃce 365 Security and Compliance, Microsoft Viva, \n", "and Microsoft 365 Copilot\n", "     \n", "Oﬃce Consumer products and cloud \n", "services revenue growth\n", " Revenue from Oﬃce Consumer products and cloud services, \n", "including Microsoft 365 Consumer subscriptions, Oﬃce \n", "licensed on-premises, and other Oﬃce services\n", "     \n", "Oﬃce 365 Commercial seat growth\n", " The number of Oﬃce 365 Commercial seats at end of period \n", "where seats are paid users covered by an Oﬃce 365 \n", "Commercial subscription\n", "     \n", "Microsoft 365 Consumer subscribers\n", " The number of Microsoft 365 Consumer subscribers at end of \n", "period\n", "     \n", "Dynamics products and cloud services \n", "revenue growth\n", " Revenue from Dynamics products and cloud services, \n", "including Dynamics 365, comprising a set of intelligent, cloud-\n", "based applications across ERP, CRM (including Customer \n", "Insights), Power Apps, and Power Automate; and on-premises \n", "ERP and CRM applications\n", "     \n", "LinkedIn revenue growth\n", " Revenue from LinkedIn, including Talent Solutions, Marketing \n", "Solutions, Premium Subscriptions, and Sales Solutions\n", "     \n", "Server products and cloud services \n", "revenue growth\n", " Revenue from Server products and cloud services, including \n", "Azure and other cloud services; SQL Server, Windows Server, \n", "Visual Studio, System Center, and related Client Access \n", "Licenses (“CALs”); and Nuance and GitHub\n", " \n", "More Personal Computing\n", "Metrics related to our More Personal Computing segment assess the performance of key lines of \n", "business within this segment. These metrics provide strategic product insights which allow us to assess \n", "the performance across our commercial and consumer businesses. As we have diversity of target \n", "audiences and sales motions within the Windows business, we monitor metrics that are reﬂective of \n", "those varying motions.\n", " \n", "Windows OEM revenue growth\n", " Revenue from sales of Windows Pro and non-Pro licenses sold \n", "through the OEM channel\n", "     \n", "Windows Commercial products and \n", "cloud services revenue growth\n", " Revenue from Windows Commercial products and cloud \n", "services, comprising volume licensing of the Windows \n", "operating system, Windows cloud services, and other \n", "Windows commercial oﬀerings\n", "     \n", "Devices revenue growth\n", " Revenue from Devices, including Surface, HoloLens, and PC \n", "accessories\n", "     \n", "Xbox content and services revenue \n", "growth \n", " Revenue from Xbox content and services, comprising ﬁrst- \n", "and third-party content (including games and in-game \n", "content), Xbox Game Pass and other subscriptions, Xbox \n", "Cloud Gaming, advertising, third-party disc royalties, and \n", "other cloud services\n", "     \n", "Search and news advertising revenue \n", "(ex  TAC) growth\n", " Revenue from search and news advertising excluding traﬃc \n", "acquisition costs (“TAC”) paid to Bing Ads network publishers \n", "and news partners\n", " \n", "43PART II\n", "Item 7\n", " \n", "SUMMARY RESULTS OF OPERATIONS\n", " \n", "(In millions, except percentages and per share amounts)  2023   2022   Percentage\n", "Change   \n", "                 \n", "                 \n", "Revenue  $211,915  $ 198,270    7% \n", "Gross margin   146,052    135,620    8% \n", "Operating income    88,523    83,383    6% \n", "Net income    72,361    72,738    (1)% \n", "Diluted earnings per share    9.68    9.65    0% \n", "                        \n", "Adjusted gross margin (non-GAAP)   146,204    135,620    8% \n", "Adjusted operating income (non-GAAP)    89,694    83,383    8% \n", "Adjusted net income (non-GAAP)    73,307    69,447    6% \n", "Adjusted diluted earnings per share (non-GAAP)    9.81    9.21    7% \n", "                      \n", " \n", "Adjusted gross margin, operating income, net income, and diluted earnings per share (“EPS”) are non-\n", "GAAP ﬁnancial measures. Current year non-GAAP ﬁnancial measures exclude the impact of the Q2 \n", "charge, which includes employee severance expenses, impairment charges resulting from changes to \n", "our hardware portfolio, and costs related to lease consolidation activities. Prior year non-GAAP ﬁnancial \n", "measures exclude the net income tax beneﬁt related to transfer of intangible properties in the ﬁrst \n", "quarter of ﬁscal year 2022. Refer to Note 12 – Income Taxes of the Notes to Financial Statements (Part II, \n", "Item 8 of this Form 10-K) for further discussion. Refer to the Non-GAAP Financial Measures section below \n", "for a reconciliation of our ﬁnancial results reported in accordance with GAAP to non-GAAP ﬁnancial \n", "results.\n", "Fiscal Year 2023 Compared with Fiscal Year 2022\n", "Revenue increased $13.6 billion or 7% driven by growth in Intelligent Cloud and Productivity and \n", "Business Processes, oﬀset in part by a decline in More Personal Computing. Intelligent Cloud revenue \n", "increased driven by Azure and other cloud services. Productivity and Business Processes revenue \n", "increased driven by Oﬃce 365 Commercial and LinkedIn. More Personal Computing revenue decreased \n", "driven by Windows  and Devices. \n", "Cost of revenue increased $3.2 billion or 5% driven by growth in Microsoft Cloud, oﬀset in part by the \n", "change in accounting estimate.\n", "Gross margin increased $10.4 billion or 8% driven by growth in Intelligent Cloud and Productivity and \n", "Business Processes and the change in accounting estimate, oﬀset in part by a decline in More Personal \n", "Computing. \n", "•Gross margin percentage increased slightly. Excluding the impact of the change in accounting \n", "estimate, gross margin percentage decreased 1 point driven by declines in Intelligent Cloud \n", "and More Personal Computing, oﬀset in part by sales mix shift between our segments.\n", "•Microsoft Cloud gross margin percentage increased 2 points to 72%. Excluding the impact of \n", "the change in accounting estimate, Microsoft Cloud gross margin percentage decreased \n", "slightly driven by a decline in Azure and other cloud services and sales mix shift to Azure and \n", "other cloud services, oﬀset in part by improvement in Oﬃce 365 Commercial.\n", "Operating expenses increased $5.3 billion or 10% driven by employee severance expenses, 2 points of \n", "growth from the Nuance and Xandr acquisitions, investments in cloud engineering, and LinkedIn.\n", "Operating income increased $5.1 billion or 6% driven by growth in Productivity and Business Processes \n", "and Intelligent Cloud and the change in accounting estimate, oﬀset in part by a decline in More Personal \n", "Computing.\n", "Revenue, gross margin, and operating income included an unfavorable foreign currency impact of 4%, \n", "4%, and 6%, respectively. Cost of revenue and operating expenses both included a favorable foreign \n", "currency impact of 2%.\n", "Current year gross margin, operating income, net income, and diluted EPS were negatively impacted by \n", "the Q2 charge, which resulted in decreases of $152 million, $1.2 billion, $946 million, and $0.13, \n", "respectively. Prior year net income and diluted EPS were positively impacted by the net tax beneﬁt \n", "related to the transfer of intangible properties, which resulted in an increase to net income and diluted \n", "EPS of $3.3 billion and $0.44, respectively.\n", "44PART II\n", "Item 7\n", " \n", "SEGMENT RESULTS OF OPERATIONS\n", " \n", "(In millions, except percentages)  2023   2022   Percentage\n", "Change   \n", "                  \n", "                 \n", "Revenue             \n", "    \n", "Productivity and Business Processes  $ 69,274  $ 63,364   9% \n", "Intelligent Cloud    87,907    74,965   17% \n", "More Personal Computing    54,734    59,941   (9)% \n", "              \n", "            \n", "Total  $211,915  $ 198,270   7% \n", "                  \n", "    \n", "Operating Income                \n", "                     \n", "Productivity and Business Processes  $ 34,189  $ 29,690   15% \n", "Intelligent Cloud    37,884    33,203   14% \n", "More Personal Computing    16,450    20,490   (20)% \n", "              \n", "            \n", "Total  $ 88,523  $ 83,383   6% \n", "                   \n", " \n", "Reportable Segments\n", "Fiscal Year 2023 Compared with Fiscal Year 2022 \n", "Productivity and Business Processes \n", "Revenue increased $5.9 billion or 9%. \n", "•Oﬃce Commercial products and cloud services revenue increased $3.7 billion or 10%. Oﬃce \n", "365 Commercial revenue grew 13% with seat growth of 11%, driven by small and medium \n", "business and frontline worker oﬀerings, as well as growth in revenue per user. Oﬃce \n", "Commercial products revenue declined 21% driven by continued customer shift to cloud \n", "oﬀerings. \n", "•Oﬃce Consumer products and cloud services revenue increased $140 million or 2%. Microsoft \n", "365 Consumer subscribers grew 12% to 67.0 million. \n", "•LinkedIn revenue increased $1.3 billion or 10% driven by Talent Solutions.\n", "•Dynamics products and cloud services revenue increased $750 million or 16% driven by \n", "Dynamics 365 growth of 24%. \n", "Operating income increased $4.5 billion or 15%.\n", "•Gross margin increased $5.8 billion or 12% driven by growth in Oﬃce 365 Commercial and \n", "LinkedIn, as well as the change in accounting estimate. Gross margin percentage increased. \n", "Excluding the impact of the change in accounting estimate, gross margin percentage increased \n", "slightly driven by improvement in Oﬃce 365 Commercial, oﬀset in part by sales mix shift to  \n", "cloud oﬀerings.\n", "•Operating expenses increased $1.3 billion or 7% driven by investment in LinkedIn and \n", "employee severance expenses. \n", "Revenue, gross margin, and operating income included an unfavorable foreign currency impact of 5%, \n", "5%, and 8%, respectively.\n", "Intelligent Cloud \n", "Revenue increased $12.9 billion or 17%. \n", "•Server products and cloud services revenue increased $12.6 billion or 19% driven by Azure and \n", "other cloud services. Azure and other cloud services revenue grew 29% driven by growth in our \n", "consumption-based services. Server products revenue decreased 1%.\n", "•Enterprise Services revenue increased $315 million or 4% driven by growth in Enterprise \n", "Support Services, oﬀset in part by a decline in Industry Solutions (formerly Microsoft Consulting \n", "Services).\n", "45PART II\n", "Item 7\n", " \n", "Operating income increased $4.7 billion or 14%.\n", "•Gross margin increased $8.9 billion or 17% driven by growth in Azure and other cloud services \n", "and the change in accounting estimate. Gross margin percentage decreased slightly. Excluding \n", "the impact of the change in accounting estimate, gross margin percentage decreased 3 points \n", "driven by sales mix shift to Azure and other cloud services and a decline in Azure and other \n", "cloud services.\n", "•Operating expenses increased $4.2 billion or 21% driven by investments in Azure, 4 points of \n", "growth from the Nuance acquisition, and employee severance expenses.\n", "Revenue, gross margin, and operating income included an unfavorable foreign currency impact of 4%, \n", "4%, and 6%, respectively. Operating expenses included a favorable foreign currency impact of 2%.\n", "More Personal Computing \n", "Revenue decreased $5.2 billion or 9%.\n", "•Windows revenue decreased $3.2 billion or 13% driven by a decrease in Windows OEM. \n", "Windows OEM revenue decreased 25% as elevated channel inventory levels continued to drive \n", "additional weakness beyond declining PC demand. Windows Commercial products and cloud \n", "services revenue increased 5% driven by demand for Microsoft 365.\n", "•Devices revenue decreased $1.8 billion or 24% as elevated channel inventory levels continued \n", "to drive additional weakness beyond declining PC demand.\n", "•Gaming revenue decreased $764 million or 5% driven by declines in Xbox hardware and Xbox \n", "content and services. Xbox hardware revenue decreased 11% driven by lower volume and \n", "price of consoles sold. Xbox content and services revenue decreased 3% driven by a decline in \n", "ﬁrst-party content, oﬀset in part by growth in Xbox Game Pass.\n", "•Search and news advertising revenue increased $617 million or 5%. Search and news \n", "advertising revenue excluding traﬃc acquisition costs increased 11% driven by higher search \n", "volume and the Xandr acquisition.\n", "Operating income decreased $4.0 billion or 20%.\n", "•Gross margin decreased $4.2 billion or 13% driven by declines in Windows and Devices. Gross \n", "margin percentage decreased driven by a decline in Devices.\n", "•Operating expenses decreased $195 million or 2% driven by a decline in Devices, oﬀset in part \n", "by investments in Search and news advertising, including 2 points of growth from the Xandr \n", "acquisition.\n", "Revenue, gross margin, and operating income included an unfavorable foreign currency impact of 3%, \n", "4%, and 6%, respectively. Operating expenses included a favorable foreign currency impact of 2%.\n", "46PART II\n", "Item 7\n", " \n", "OPERATING EXPENSES\n", "Research and Development\n", " \n", "(In millions, except percentages)  2023   2022   Percentage\n", "Change  \n", " \n", "                  \n", "Research and development  $ 27,195  $ 24,512    11% \n", "As a percent of revenue   13%   12%   1ppt \n", "                        \n", " \n", "Research and development expenses include payroll, employee beneﬁts, stock-based compensation \n", "expense, and other headcount-related expenses associated with product development. Research and \n", "development expenses also include third-party development and programming costs and the \n", "amortization of purchased software code and services content. \n", "Fiscal Year 2023 Compared with Fiscal Year 2022\n", "Research and development expenses increased $2.7 billion or 11% driven by investments in cloud \n", "engineering and LinkedIn.\n", "Sales and Marketing\n", " \n", "(In millions, except percentages)  2023   2022   Percentage\n", "Change  \n", "                  \n", "                  \n", "Sales and marketing  $ 22,759  $ 21,825    4% \n", "As a percent of revenue   11%   11%   0ppt \n", "10-Q \n", "  3/31/2018 \n", "  10.27 \n", "  4/26/2018 \n", "                                       \n", "10.9\n", " Amended and Restated \n", "Oﬃcers’ Indemniﬁcation Trust \n", "Agreement between Microsoft \n", "Corporation and The Bank of \n", "New York Mellon Trust \n", "Company, N.A., as trustee     10-Q \n", "  9/30/2016 \n", "  10.12 \n", "  10/20/201\n", "6 \n", "            \n", "10.10\n", " Assumption of Beneﬁciaries’ \n", "Representative Obligations \n", "Under Amended and Restated \n", "Oﬃcers’ Indemniﬁcation Trust \n", "Agreement   \n", "  10-K \n", "  6/30/2020 \n", "  10.25 \n", "  7/30/2020 \n", "            \n", "10.11\n", " Form of Indemniﬁcation \n", "Agreement and Amended and \n", "Restated Directors’ \n", "Indemniﬁcation Trust \n", "Agreement between Microsoft \n", "Corporation and The Bank of \n", "New York Mellon Trust \n", "Company, N.A., as trustee      10-K \n", "  6/30/2019 \n", "  10.13 \n", "  8/1/2019 \n", "                                       \n", "10.12\n", " Assumption of Beneﬁciaries’ \n", "Representative Obligations \n", "Under Amended and Restated \n", "Directors’ Indemniﬁcation Trust \n", "Agreement      10-K \n", "  6/30/2020 \n", "  10.26 \n", "  7/30/2020 \n", "                                       \n", "10.14*\n", " Microsoft Corporation Deferred \n", "Compensation Plan for Non-\n", "Employee Directors      10-Q \n", "   12/31/20\n", "17 \n", "  10.14 \n", "  1/31/2018 \n", "            \n", "10.15*\n", " Microsoft Corporation \n", "Executive Incentive Plan      8-K         10.1   9/19/2018 \n", "            \n", "10.19*\n", " Microsoft Corporation \n", "Executive Incentive Plan      10-Q   9/30/2016   10.17   10/20/201\n", "6 \n", "                                       \n", "10.20*\n", " Form of Executive Incentive \n", "Plan (Executive Oﬃcer SAs) \n", "Stock Award Agreement under \n", "the Microsoft Corporation 2001 \n", "Stock Plan      10-Q   9/30/2016   10.18   10/20/201\n", "6 \n", "                                       \n", "10.21*\n", " Form of Executive Incentive \n", "Plan Performance Stock Award \n", "Agreement under the Microsoft \n", "Corporation 2001 Stock Plan      10-Q   9/30/2016   10.25   10/20/201\n", "6 \n", "             \n", "10.22*\n", " Senior Executive Severance \n", "Beneﬁt Plan      10-Q   9/30/2016   10.22   10/20/201\n", "6 \n", "            \n", "10.23*\n", " Oﬀer Letter, dated February 3, \n", "2014, between Microsoft \n", "Corporation and Satya <PERSON>      8-K         10.1   2/4/2014 \n", "106PART IV\n", "Item 15\n", " \n", "     Incorporated by Reference  \n", "Exhibit\n", "Number  Exhibit Description  Filed\n", "<PERSON><PERSON><PERSON>\n", "h  Form   Period\n", "Ending   Exhibit   Filing Date  \n", "            \n", "10.24*\n", " Long-Term Performance Stock \n", "Award Agreement between \n", "Microsoft Corporation and \n", "<PERSON><PERSON><PERSON>      10-Q   12/31/201\n", "4   10.24   1/26/2015 \n", "                                        \n", "10.25*\n", " Oﬀer Letter, dated October 25, \n", "2020, between Microsoft \n", "Corporation and Christopher \n", "<PERSON>      10-Q   9/30/2021   10.27   10/26/202\n", "1 \n", "             \n", "21  Subsidiaries of Registrant  X                \n", "            \n", "23.1\n", " Consent of Independent \n", "Registered Public Accounting \n", "Firm  X  \n", "    \n", "    \n", "    \n", "  \n", "            \n", "31.1\n", " Certiﬁcation of Chief Executive \n", "Oﬃcer Pursuant to Section 302 \n", "of the Sarbanes-Oxley Act of \n", "2002  X  \n", "    \n", "    \n", "    \n", "  \n", "                                       \n", "31.2\n", " Certiﬁcation of Chief Financial \n", "Oﬃcer Pursuant to Section 302 \n", "of the Sarbanes-Oxley Act of \n", "2002  X  \n", "    \n", "    \n", "    \n", "  \n", "                                       \n", "32.1**\n", " Certiﬁcation of Chief Executive \n", "Oﬃcer Pursuant to Section 906 \n", "of the Sarbanes-Oxley Act of \n", "2002  X  \n", "    \n", "    \n", "    \n", "  \n", "                                       \n", "32.2**\n", " Certiﬁcation of Chief Financial \n", "Oﬃcer Pursuant to Section 906 \n", "of the Sarbanes-Oxley Act of \n", "2002  X  \n", "    \n", "    \n", "    \n", "  \n", "                                       \n", "101.INS\n", " Inline XBRL Instance Document\n", "—the instance document does \n", "not appear in the Interactive \n", "Data File as its XBRL tags are \n", "embedded within the Inline \n", "XBRL document  X  \n", "    \n", "    \n", "    \n", "  \n", "                                       \n", "101.<PERSON><PERSON>\n", " Inline XBRL Taxonomy \n", "Extension Schema  X  \n", "    \n", "    \n", "    \n", "  \n", "                                       \n", "101.CAL\n", " Inline XBRL Taxonomy \n", "Extension Calculation Linkbase X  \n", "    \n", "    \n", "    \n", "  \n", "                                       \n", "101.DEF\n", " Inline XBRL Taxonomy \n", "Extension Deﬁnition Linkbase  X  \n", "    \n", "    \n", "    \n", "  \n", "                                       \n", "101.LAB\n", " Inline XBRL Taxonomy \n", "Extension Label Linkbase  X  \n", "    \n", "    \n", "    \n", "  \n", "                                       \n", "101.<PERSON><PERSON>\n", " Inline XBRL Taxonomy \n", "Extension Presentation \n", "Linkbase  X  \n", "    \n", "    \n", "    \n", "  \n", "                                       \n", "104\n", " Cover page formatted as Inline \n", "XBRL and contained in Exhibit \n", "101  X  \n", "    \n", "    \n", "    \n", "  \n", " \n", "* Indicates a management contract or compensatory plan or arrangement.\n", "** Furnished, not ﬁled.\n", "107PART IV\n", "Item 16\n", " \n", "ITEM 16. FORM 10-K SUMMAR Y\n", "None.\n", "108 \n", "SIGNATURES\n", "Pursuant to the requirements of Section 13 or 15(d) of the Securities Exchange Act of 1934, the \n", "Registrant has duly caused this report to be signed on its behalf by the undersigned; thereunto duly \n", "authorized, in the City of Redmond, State of Washington, on July 27, 2023. \n", " \n", "MICROSOF T CORPOR ATION\n", " \n", "/s/ ALICE L. JOLLA\n", "<PERSON>\n", "Corporate Vice President and Chief Accounting \n", "Oﬃcer (Principal Accounting Oﬃcer)\n", " \n", "Pursuant to the requirements of the Securities Exchange Act of 1934, this report has been signed below \n", "by the following persons on behalf of Registrant and in the capacities indicated on July 27, 2023. \n", " \n", "Signature  Title\n", " \n", "  \n", "/s/ SATYA NADELL A         Chairman and Chief Executive Oﬃcer\n", "(Principal Executive Oﬃcer)<PERSON><PERSON><PERSON>  \n", "  \n", "/s/ REID HOFFMAN           Director\n", "<PERSON> Hoﬀman  \n", "  \n", "/s/ HUGH F. JOHNSTON           Director\n", "<PERSON>  \n", "  \n", "/s/ TERI L. LIST  Director\n", "<PERSON><PERSON> L. <PERSON>  \n", "  \n", "/s/ SANDR A E. PETER<PERSON>   Lead Independent Director\n", "<PERSON>  \n", "   \n", "/s/ PENNY S. PRITZKER  Director\n", "<PERSON>   \n", "  \n", "/s/ CARLOS A. RODRIGUEZ  Director\n", "<PERSON>  \n", "   \n", "/s/ CHARLES  W. SCHARF          Director\n", "<PERSON>  \n", "  \n", "/s/ JOHN W. STANTON          Director\n", "<PERSON>  \n", "   \n", "/s/ JOHN W<PERSON>          Director\n", "<PERSON>  \n", "   \n", "/s/ EMMA N. WALMSLEY          Director\n", "<PERSON>  \n", "  \n", "/s/ PADMAS<PERSON><PERSON>  WARRIOR  Director\n", "Padma<PERSON>ree Warrior  \n", "  \n", "/s/ AMY E. HOOD         Executive Vice President and Chief Financial Oﬃcer\n", "(Principal Financial Oﬃcer)<PERSON>  \n", "  \n", "/s/ ALICE L. JOLLA Corporate Vice President and Chief Accounting \n", "Oﬃcer\n", "(Principal Accounting Oﬃcer)<PERSON> \n", " \n", "109\n", "\n", "\n", "\u001b[32m**********************************************************************\u001b[0m\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mData_Analyst\u001b[0m (to User_Proxy):\n", "\n", "Microsoft's financial performance in fiscal year 2023 shows a robust and diversified business model, with significant growth in key areas despite challenges in others. Here's a detailed analysis based on the provided data:\n", "\n", "### Overall Financial Health\n", "- **Revenue:** Increased by 7% to $211.9 billion from $198.3 billion in 2022, indicating strong sales across various segments.\n", "- **Gross Margin:** Improved by 8% to $146.1 billion, reflecting efficient cost management and a strong mix of high-margin products and services.\n", "- **Operating Income:** Grew by 6% to $88.5 billion, showcasing effective operational management and profitability.\n", "- **Net Income:** Slightly decreased by 1% to $72.4 billion, which could be attributed to specific charges and investments made during the year.\n", "- **Diluted Earnings Per Share (EPS):** Remained relatively stable with a minor increase from $9.65 to $9.68, indicating steady profitability on a per-share basis.\n", "\n", "### Segment Performance\n", "- **Productivity and Business Processes:** Saw a 9% revenue increase to $69.3 billion, driven by strong Office 365 Commercial and LinkedIn performance.\n", "- **Intelligent Cloud:** Experienced a significant 17% revenue increase to $87.9 billion, primarily due to Azure and other cloud services, highlighting Microsoft's strong position in the cloud market.\n", "- **More Personal Computing:** Faced a 9% decline in revenue to $54.7 billion, affected by decreased Windows OEM and Devices revenue, reflecting challenges in the PC market.\n", "\n", "### Key Metrics and Growth Drivers\n", "- **Microsoft Cloud Revenue:** Indicates robust demand for Microsoft's cloud offerings, a critical growth driver.\n", "- **Office 365 Commercial Seat Growth:** Demonstrates Microsoft's expanding footprint in enterprise productivity solutions.\n", "- **LinkedIn and Dynamics 365 Revenue Growth:** Highlights the increasing importance of social networking and business applications to Microsoft's portfolio.\n", "\n", "### Challenges and Adjustments\n", "- **More Personal Computing Decline:** The decline in this segment underscores the challenges in the PC market, including high inventory levels and shifting consumer demand.\n", "- **Foreign Currency Impact:** Revenue, gross margin, and operating income were unfavorably impacted by currency fluctuations, indicating the global economic challenges faced.\n", "\n", "### Strategic Investments and Expenses\n", "- **Research and Development:** Increased by 11% to $27.2 billion, emphasizing Microsoft's commitment to innovation.\n", "- **Sales and Marketing:** Saw a moderate increase, supporting the company's growth and market expansion efforts.\n", "\n", "### Non-GAAP Measures\n", "- Adjusted figures exclude specific charges and benefits, providing an alternative view of Microsoft's performance that highlights operational strengths.\n", "\n", "### Conclusion\n", "Microsoft's fiscal year 2023 performance showcases a company that continues to grow its core segments, particularly in cloud and productivity solutions, while navigating challenges in the PC market and global economic fluctuations. The company's strategic investments in research and development and sales and marketing support its long-term growth trajectory. Despite a slight decrease in net income, the overall financial health remains strong, with significant opportunities for future growth in its diversified business model.\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mUser_Proxy\u001b[0m (to Data_Analyst):\n", "\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "\u001b[33mData_Analyst\u001b[0m (to User_Proxy):\n", "\n", "TERMINATE\n", "\n", "--------------------------------------------------------------------------------\n", "Current chat finished. Resetting agents ...\n"]}], "source": ["assitant = SingleAssistantRAG(\n", "    \"Data_Analyst\",\n", "    llm_config,\n", "    human_input_mode=\"NEVER\",\n", "    retrieve_config={\n", "        \"task\": \"qa\",\n", "        \"vector_db\": None,  # Autogen has bug for this version\n", "        \"docs_path\": [\n", "            \"../report/2023-07-27_10-K_msft-20230630.htm.pdf\",\n", "        ],\n", "        \"chunk_token_size\": 2000,\n", "        \"collection_name\": \"msft_10k\",\n", "        \"get_or_create\": True,\n", "        \"must_break_at_empty_line\": False,\n", "    },\n", "    rag_description=\"Retrieve content from MSFT's 2023 10-K report for detailed question answering.\",\n", ")\n", "assitant.chat(\"How's msft's 2023 income? Provide with some analysis.\")"]}], "metadata": {"kernelspec": {"display_name": "finrobot", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}