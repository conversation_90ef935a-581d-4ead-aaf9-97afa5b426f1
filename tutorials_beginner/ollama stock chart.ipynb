{"cells": [{"cell_type": "code", "execution_count": 1, "id": "8fdc8ae2-2664-491c-86dd-8e53d82fbb5d", "metadata": {}, "outputs": [], "source": ["from autogen import AssistantAgent, UserProxyAgent"]}, {"cell_type": "code", "execution_count": 27, "id": "d81b7469-2e64-48b1-8dc8-804f2d946ab4", "metadata": {}, "outputs": [], "source": ["config_list = [\n", "    {\n", "        \"model\": \"llama3\",\n", "        \"base_url\": \"http://127.0.0.1:4000\",\n", "        \"api_key\": \"ollama\",\n", "    }\n", "]"]}, {"cell_type": "code", "execution_count": 28, "id": "a74c0853-9d79-4a94-a213-b46da1c850e9", "metadata": {}, "outputs": [], "source": ["assistant = AssistantAgent(\"assistant\", llm_config={\"config_list\": config_list})\n", "\n", "user_proxy = UserProxyAgent(\"user_proxy\", code_execution_config={\"work_dir\": \"../result code plot\", \"use_docker\": False})"]}, {"cell_type": "code", "execution_count": 29, "id": "af3c04b8-5225-4faa-bfef-7814696fe21d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33muser_proxy\u001b[0m (to assistant):\n", "\n", "Plot a chart of apple stock price change in 2023 and save in a file. Get information using yfinance.\n", "\n", "--------------------------------------------------------------------------------\n", "[autogen.oai.client: 07-29 19:57:30] {329} WARNING - Model ollama/llama3 is not found. The cost will be 0. In your config_list, add field {\"price\" : [prompt_price_per_1k, completion_token_price_per_1k]} for customized pricing.\n", "\u001b[33massistant\u001b[0m (to user_proxy):\n", "\n", "I'd be happy to help you with that! Here's the code block:\n", "\n", "```python\n", "# filename: plot_apple_stock.py\n", "import yfinance as yf\n", "import matplotlib.pyplot as plt\n", "\n", "apple_data = yf.download('AAPL', start='2023-01-01', end='2023-12-31')['Adj Close']\n", "\n", "plt.figure(figsize=(10, 6))\n", "plt.plot(apple_data.index, apple_data.values)\n", "plt.title('Apple Stock Price Change in 2023')\n", "plt.xlabel('Date')\n", "plt.ylabel('Price (USD)')\n", "plt.savefig('apple_stock_price_change.png')\n", "\n", "print(\"Chart saved as 'apple_stock_price_change.png' in the same directory.\")\n", "```\n", "\n", "Please execute this code block to plot the chart of Apple stock price change in 2023 and save it in a file. The `yfinance` library is used to retrieve the adjusted closing prices of Apple (AAPL) from January 1st, 2023 to December 31st, 2023. The `matplotlib` library is used to create the chart.\n", "\n", "TERMINATE\n", "\n", "--------------------------------------------------------------------------------\n"]}, {"name": "stdin", "output_type": "stream", "text": ["Provide feedback to assistant. Press enter to skip and use auto-reply, or type 'exit' to end the conversation:  \n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[31m\n", ">>>>>>>> NO HUMAN INPUT RECEIVED.\u001b[0m\n", "\u001b[31m\n", ">>>>>>>> USING AUTO REPLY...\u001b[0m\n", "\u001b[31m\n", ">>>>>>>> EXECUTING CODE BLOCK 0 (inferred language is python)...\u001b[0m\n", "\u001b[33muser_proxy\u001b[0m (to assistant):\n", "\n", "exitcode: 0 (execution succeeded)\n", "Code output: \n", "Chart saved as 'apple_stock_price_change.png' in the same directory.\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "[autogen.oai.client: 07-29 19:58:44] {329} WARNING - Model ollama/llama3 is not found. The cost will be 0. In your config_list, add field {\"price\" : [prompt_price_per_1k, completion_token_price_per_1k]} for customized pricing.\n", "\u001b[33massistant\u001b[0m (to user_proxy):\n", "\n", "### Assistant:\n", "\n", "Thank you for executing the code block! It seems that the code ran successfully and the chart of Apple stock price change in 2023 has been saved in a file named \"apple_stock_price_change.png\".\n", "\n", "Since the code execution was successful, I can consider my task as complete. Therefore, I will now TERMINATE.\n", "\n", "TERMINATE\n", "\n", "--------------------------------------------------------------------------------\n"]}, {"name": "stdin", "output_type": "stream", "text": ["Provide feedback to assistant. Press enter to skip and use auto-reply, or type 'exit' to end the conversation:  exit\n"]}, {"data": {"text/plain": ["ChatResult(chat_id=None, chat_history=[{'content': 'Plot a chart of apple stock price change in 2023 and save in a file. Get information using yfinance.', 'role': 'assistant'}, {'content': 'I\\'d be happy to help you with that! Here\\'s the code block:\\n\\n```python\\n# filename: plot_apple_stock.py\\nimport yfinance as yf\\nimport matplotlib.pyplot as plt\\n\\napple_data = yf.download(\\'AAPL\\', start=\\'2023-01-01\\', end=\\'2023-12-31\\')[\\'Adj Close\\']\\n\\nplt.figure(figsize=(10, 6))\\nplt.plot(apple_data.index, apple_data.values)\\nplt.title(\\'Apple Stock Price Change in 2023\\')\\nplt.xlabel(\\'Date\\')\\nplt.ylabel(\\'Price (USD)\\')\\nplt.savefig(\\'apple_stock_price_change.png\\')\\n\\nprint(\"Chart saved as \\'apple_stock_price_change.png\\' in the same directory.\")\\n```\\n\\nPlease execute this code block to plot the chart of Apple stock price change in 2023 and save it in a file. The `yfinance` library is used to retrieve the adjusted closing prices of Apple (AAPL) from January 1st, 2023 to December 31st, 2023. The `matplotlib` library is used to create the chart.\\n\\nTERMINATE', 'role': 'user'}, {'content': \"exitcode: 0 (execution succeeded)\\nCode output: \\nChart saved as 'apple_stock_price_change.png' in the same directory.\\n\", 'role': 'assistant'}, {'content': '### Assistant:\\n\\nThank you for executing the code block! It seems that the code ran successfully and the chart of Apple stock price change in 2023 has been saved in a file named \"apple_stock_price_change.png\".\\n\\nSince the code execution was successful, I can consider my task as complete. Therefore, I will now TERMINATE.\\n\\nTERMINATE', 'role': 'user'}], summary='### Assistant:\\n\\nThank you for executing the code block! It seems that the code ran successfully and the chart of Apple stock price change in 2023 has been saved in a file named \"apple_stock_price_change.png\".\\n\\nSince the code execution was successful, I can consider my task as complete. Therefore, I will now .\\n\\n', cost={'usage_including_cached_inference': {'total_cost': 0, 'ollama/llama3': {'cost': 0, 'prompt_tokens': 288, 'completion_tokens': 295, 'total_tokens': 583}}, 'usage_excluding_cached_inference': {'total_cost': 0, 'ollama/llama3': {'cost': 0, 'prompt_tokens': 288, 'completion_tokens': 295, 'total_tokens': 583}}}, human_input=['', 'exit'])"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["year=2023\n", "company=\"apple\"\n", "\n", "user_proxy.initiate_chat(assistant, message=f\"Plot a chart of {company} stock price change in {year} and save in a file. Get information using yfinance.\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "4f37b572-1b38-416e-bf88-67900d8ddd7e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}