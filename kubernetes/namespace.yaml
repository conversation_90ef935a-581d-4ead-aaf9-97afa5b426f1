apiVersion: v1
kind: Namespace
metadata:
  name: finrobot
  labels:
    name: finrobot
    environment: production

---
apiVersion: v1
kind: Secret
metadata:
  name: finrobot-secrets
  namespace: finrobot
type: Opaque
data:
  # Base64 encoded secrets - replace with actual values
  database-url: ************************************************************************
  redis-url: cmVkaXM6Ly9yZWRpczozNjM5LzA=
  alpha-vantage-api-key: eW91ci1hbHBoYS12YW50YWdlLWFwaS1rZXk=
  finnhub-api-key: eW91ci1maW5uaHViLWFwaS1rZXk=
  polygon-api-key: eW91ci1wb2x5Z29uLWFwaS1rZXk=
  tiingo-api-key: eW91ci10aWluZ28tYXBpLWtleQ==

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: finrobot-config
  namespace: finrobot
data:
  config.yaml: |
    # FinRobot-Pro Production Configuration
    
    # Data Configuration
    data:
      cache_ttl_hours: 6
      max_symbols: 100
      update_frequency: 300  # 5 minutes
      
    # Model Configuration
    models:
      lightgbm:
        n_estimators: 1000
        learning_rate: 0.05
        num_leaves: 31
        max_depth: -1
        min_child_samples: 20
        subsample: 0.8
        colsample_bytree: 0.8
        reg_alpha: 0.1
        reg_lambda: 0.1
        
      xgboost:
        n_estimators: 1000
        learning_rate: 0.05
        max_depth: 6
        min_child_weight: 1
        subsample: 0.8
        colsample_bytree: 0.8
        reg_alpha: 0.1
        reg_lambda: 0.1
        gamma: 0.0
        
      lstm:
        hidden_size: 64
        num_layers: 2
        dropout: 0.2
        learning_rate: 0.001
        batch_size: 32
        sequence_length: 20
        epochs: 100
    
    # Training Configuration
    training:
      target_horizons: [1, 5, 10, 20]
      validation_split: 0.2
      test_split: 0.1
      walk_forward_validation: true
      min_train_samples: 1000
      
    # Risk Management
    risk:
      max_position_size: 0.1
      var_confidence_levels: [0.95, 0.99]
      lookback_window: 252
      
    # API Configuration
    api:
      host: "0.0.0.0"
      port: 8000
      workers: 4
      timeout: 30
      
    # Logging Configuration
    logging:
      level: INFO
      format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
      file: "/app/logs/finrobot.log"
      max_size: "10MB"
      backup_count: 5

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: finrobot-service-account
  namespace: finrobot

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: finrobot
  name: finrobot-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: finrobot-role-binding
  namespace: finrobot
subjects:
- kind: ServiceAccount
  name: finrobot-service-account
  namespace: finrobot
roleRef:
  kind: Role
  name: finrobot-role
  apiGroup: rbac.authorization.k8s.io
