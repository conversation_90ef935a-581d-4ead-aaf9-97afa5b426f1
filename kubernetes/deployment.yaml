apiVersion: apps/v1
kind: Deployment
metadata:
  name: finrobot-api
  namespace: finrobot
  labels:
    app: finrobot-api
    version: v1
spec:
  replicas: 3
  selector:
    matchLabels:
      app: finrobot-api
  template:
    metadata:
      labels:
        app: finrobot-api
        version: v1
    spec:
      containers:
      - name: finrobot-api
        image: finrobot:latest
        ports:
        - containerPort: 8000
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: LOG_LEVEL
          value: "INFO"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: finrobot-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: finrobot-secrets
              key: redis-url
        - name: ALPHA_VANTAGE_API_KEY
          valueFrom:
            secretKeyRef:
              name: finrobot-secrets
              key: alpha-vantage-api-key
        - name: FINNHUB_API_KEY
          valueFrom:
            secretKeyRef:
              name: finrobot-secrets
              key: finnhub-api-key
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: data-volume
          mountPath: /app/data
        - name: models-volume
          mountPath: /app/models
        - name: cache-volume
          mountPath: /app/cache
      volumes:
      - name: data-volume
        persistentVolumeClaim:
          claimName: finrobot-data-pvc
      - name: models-volume
        persistentVolumeClaim:
          claimName: finrobot-models-pvc
      - name: cache-volume
        emptyDir: {}
      imagePullSecrets:
      - name: finrobot-registry-secret

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: finrobot-worker
  namespace: finrobot
  labels:
    app: finrobot-worker
    version: v1
spec:
  replicas: 2
  selector:
    matchLabels:
      app: finrobot-worker
  template:
    metadata:
      labels:
        app: finrobot-worker
        version: v1
    spec:
      containers:
      - name: finrobot-worker
        image: finrobot:latest
        command: ["python", "-m", "finrobot.worker.main"]
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: LOG_LEVEL
          value: "INFO"
        - name: WORKER_TYPE
          value: "training"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: finrobot-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: finrobot-secrets
              key: redis-url
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        volumeMounts:
        - name: data-volume
          mountPath: /app/data
        - name: models-volume
          mountPath: /app/models
        - name: cache-volume
          mountPath: /app/cache
      volumes:
      - name: data-volume
        persistentVolumeClaim:
          claimName: finrobot-data-pvc
      - name: models-volume
        persistentVolumeClaim:
          claimName: finrobot-models-pvc
      - name: cache-volume
        emptyDir: {}
      imagePullSecrets:
      - name: finrobot-registry-secret

---
apiVersion: v1
kind: Service
metadata:
  name: finrobot-api-service
  namespace: finrobot
  labels:
    app: finrobot-api
spec:
  selector:
    app: finrobot-api
  ports:
  - name: http
    port: 80
    targetPort: 8000
    protocol: TCP
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: finrobot-ingress
  namespace: finrobot
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - api.finrobot.com
    secretName: finrobot-tls
  rules:
  - host: api.finrobot.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: finrobot-api-service
            port:
              number: 80

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: finrobot-data-pvc
  namespace: finrobot
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: finrobot-models-pvc
  namespace: finrobot
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 5Gi
  storageClassName: fast-ssd

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: finrobot-api-hpa
  namespace: finrobot
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: finrobot-api
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
